:global ::-webkit-scrollbar {
  width: 5px;
}
:global ::-webkit-scrollbar-track {
  border-radius: 10px;
  background: #eee;
}
:global ::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: #ccc;
}
:global ::-webkit-scrollbar-thumb:hover {
  border-radius: 10px;
  background: #bbb;
}
:global ::-webkit-scrollbar-thumb:active {
  border-radius: 10px;
  background: #bbb;
}
.nodeContent {
  height: 100%;
  padding: 20px;
}
.nodeContent .title {
  margin-bottom: 16px;
  font-size: 16px;
}
.nodeContent .content {
  height: calc(100% - 80px);
  overflow-y: auto;
}
.nodeContent .content .nodeInfoWrapper {
  height: 160px;
  box-sizing: border-box;
  padding: 10px 3px;
}
.nodeContent .content .nodeInfo {
  height: 126px;
  box-sizing: content-box;
  padding: 12px;
  border-radius: 8px;
  background-color: #f7f8fa;
  cursor: pointer;
}
.nodeContent .content .nodeInfo .nodeInfoTop {
  height: 56px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.04);
}
.nodeContent .content .nodeInfo .nodeInfoTop .nodeInfoTopText {
  display: flex;
  height: 20px;
  align-items: center;
  justify-content: space-between;
}
.nodeContent .content .nodeInfo .nodeInfoTop .nodeInfoTopText .text {
  overflow: hidden;
  width: 180px;
  margin-left: 4px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  font-weight: 400;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.nodeContent .content .nodeInfo .nodeInfoTop .nodeInfoTopText .nodeStatusIcon {
  display: flex;
  width: 20px;
  height: 20px;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  background-color: #d8fbe7;
}
.nodeContent .content .nodeInfo .nodeInfoTop .nodeInfoTopText button {
  border: 0;
  color: #0068fa;
  font-size: 12px;
}
.nodeContent .content .nodeInfo .nodeInfoTop .id {
  margin-top: 7px;
  margin-left: 24px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
}
.nodeContent .content .nodeInfo .nodeInfoBootom {
  height: calc(100% - 85px);
  padding: 16px 0;
}
.nodeContent .content .nodeInfo .nodeInfoBootom .nodeInfoBootomItem .ItemName {
  margin-bottom: 4px;
  color: rgba(0, 0, 0, 0.6);
  font-size: 12px;
}
.nodeContent .content .nodeInfo .nodeInfoBootom .nodeInfoBootomItem .ItemName :global(.anticon) {
  margin-right: 4px;
}
.nodeContent .content .nodeInfo .nodeInfoBootom .nodeInfoBootomItem .ItemNumber {
  margin-left: 12px;
  color: rgba(0, 0, 0, 0.88);
  cursor: pointer;
  font-size: 18px;
  font-weight: 500;
}
.nodeContent .content .nodeInfo .nodeInfoBootom .nodeInfoBootomItem .ItemNumber:hover {
  color: #0068fa;
}
.nodeContent .content .nodeInfo:hover {
  border: 1px solid #9a0000;
  margin-top: -1px;
  margin-left: -1px;
}
.nodePopoverList {
  border-radius: 0;
  border-top: 1px solid rgba(5, 5, 5, 0.06);
  border-right: none !important;
  border-bottom: 1px solid rgba(5, 5, 5, 0.06);
  border-left: none !important;
}
.nodePopoverList :global(.ant-list-item) {
  padding: 4px 8px !important;
}
.nodePopoverList .nodeStatusIcon {
  display: flex;
  width: 20px;
  height: 20px;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  background-color: #d8fbe7;
}
