import { Descriptions } from 'antd';
import dayjs from 'dayjs';

import { StatusTag } from '@/components/status-tag';

import type { AdminLogListData } from '../constant';
import { getStatusLabel, getStatusType } from '../constant';

const labelMap = {
  clientIp: '客户端IP',
  module: '模块',
  operationType: '操作类型',
  resultStatus: '结果状态',
  requestParams: '请求参数',
  requestPath: '请求路径',
  responseData: '响应数据',
  level: '日志级别',
  thread: '触发线程',
  logger: '日志记录器',
  message: '日志内容',
  timestamp: '时间',
  userId: '用户ID',
  sessionId: '会话ID',
};

export const AdminLogDetail = ({ data }: { data: AdminLogListData }) => {
  return (
    <Descriptions column={2}>
      {Object.keys(labelMap).map((key) => {
        const value = data[key as keyof AdminLogListData];
        const label = labelMap[key as keyof typeof labelMap];
        if (key === 'resultStatus') {
          return (
            <Descriptions.Item key={key} label={label}>
              <StatusTag
                type={getStatusType(value as string)}
                text={getStatusLabel(value as string)}
              />
            </Descriptions.Item>
          );
        } else if (key === 'timestamp') {
          return (
            <Descriptions.Item key={key} label={label}>
              {dayjs(Number(value)).format('YYYY-MM-DD HH:mm:ss')}
            </Descriptions.Item>
          );
        }
        return (
          <Descriptions.Item key={key} label={label}>
            {value || '-'}
          </Descriptions.Item>
        );
      })}
    </Descriptions>
  );
};
