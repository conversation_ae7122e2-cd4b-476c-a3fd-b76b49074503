@import url('@/variables.less');

.main {
  display: flex;
  min-width: 1100px;
  box-sizing: border-box;
  padding: 20px;
  margin-top: 4px;
  gap: 16px;

  .left {
    flex: 1;
  }

  .right {
    width: 400px;
  }

  .flexContent {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .header {
    padding: 28px 20px;
    border-radius: 8px;
    margin-bottom: 16px;
    background-color: #fff;

    .title {
      display: flex;
      color: rgb(0 0 0 / 85%);
      font-size: 22px;
      font-weight: 600;
      line-height: 32px;

      .titleName {
        color: @PrimaryColor;
      }
    }

    .titleDesc {
      margin-top: 12px;
      color: rgb(0 0 0 / 60%);
    }
  }

  .message {
    padding: 0;

    .eventTitle {
      padding: 20px 24px 0;
      margin-bottom: 16px;
      color: rgb(0 0 0 / 85%);
      font-size: 18px;
      font-weight: 500;
    }

    .messageCard {
      padding-bottom: -8px;
      margin-top: -30px;
    }
  }

  .project {
    display: grid;
    padding: 0;
  }
}

.card {
  height: 100%;
  box-sizing: border-box;
  padding: 16px 20px 20px;
  border-radius: 5px;
  background-color: #fff;

  .title {
    position: relative;
    margin-bottom: 12px;
    color: rgb(0 0 0 / 80%);
    font-size: 16px;
    font-weight: 700;
    line-height: 24px;

    &::after {
      position: absolute;
      top: 6px;
      left: -20px;
      width: 3px;
      height: 12px;
      background-color: @PrimaryColor;
      content: '';
    }
  }
}

.workbenchNodesWrapper {
  display: flex;
  align-items: center;

  .nodeWrapper {
    padding: 8px;
    border-radius: 4px;
    margin-bottom: 12px;
    background: #f6f6f6;

    &:last-child {
      margin-bottom: 0;
    }

    :global(.ant-table-thead th) {
      padding: 5px 8px !important;
    }
  }

  .nodeTitle {
    margin-bottom: 8px;
  }
}

.datatableWrapper {
  height: 590px;
  margin-bottom: 16px;

  .datatable {
    display: flex;
    height: calc(100% - 40px);
    flex-direction: column;
    justify-content: space-between;
  }

  .datatableItem {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid rgb(0 0 0 / 8%);
    line-height: 21px;

    .datatableItemInfo {
      color: rgb(0 0 0 / 40%);
    }
  }
}

.workbenchBars {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  container-type: inline-size;
  gap: 16px;

  .bar {
    display: flex;
    flex: 1;
    flex-direction: row-reverse;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    border-radius: 8px;
    background-color: #fff;

    .barTitle {
      margin-top: 7px;
      color: rgb(0 0 0 / 80%);
      font-size: 16px;
      font-weight: 700;
      line-height: 24px;
    }

    .barDesc {
      margin-top: 2px;
      color: rgb(0 0 0 / 60%);
      font-size: 12px;
      line-height: 18px;
    }

    .barLink {
      margin-top: 16px;
      color: rgb(0 0 0 / 40%);
      cursor: pointer;
      font-size: 12px;
      line-height: 18px;
    }
  }
}

.chartsWrapper {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 16px;

  .chart {
    height: 321px;
    flex: 1;

    .chartContent {
      width: 100%;
      height: calc(100% - 20px);
    }
  }
}

.messageWrapper {
  max-height: 390px;
}

@container (max-width: 994px) {
  .workbenchBars .bar {
    flex-direction: column;
    padding: 20px 0 16px;
    text-align: center;
  }
}

@media screen and (max-width: 1500px) {
  .datatableWrapper {
    height: 670px;
  }
}
