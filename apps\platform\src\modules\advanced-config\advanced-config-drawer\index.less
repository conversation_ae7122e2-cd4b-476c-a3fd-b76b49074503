.configDrawer {
  position: absolute;

  .fontSizeSmall() {
    color: rgb(0 0 0 / 88%);
    font-size: 12px !important;
    line-height: 20px;
  }

  :global {
    .ant-drawer-content-wrapper {
      box-shadow: 0 1px 4px 0 rgb(0 0 0 / 15%);
    }

    .ant-drawer-title {
      font-size: 14px;
    }

    .ant-drawer-body {
      padding: 0 12px;
    }

    .ant-drawer-header {
      padding: 10px 12px 20px;
      border-bottom-color: transparent;
    }

    .ant-form-item {
      margin-bottom: 12px;
    }

    .ant-alert-message {
      color: rgb(0 0 0 / 45%);
      font-size: 12px;
    }
  }

  &:focus {
    outline: none;
  }

  .footer {
    position: sticky;
    bottom: 0;
    left: 0;
    height: 52px;
    background-color: white;
  }

  .task {
    .fontSizeSmall();
  }

  .nodeStoragePath {
    :global(.ant-col-24.ant-form-item-label) {
      padding: 0;
    }

    :global(.ant-form-item .ant-form-item-extra) {
      min-height: 16px;
      margin-top: 4px;
      font-size: 12px;
    }

    .label {
      .fontSizeSmall();
    }
  }
}
