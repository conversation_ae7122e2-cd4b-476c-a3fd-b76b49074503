.container {
  width: 100%;
  height: 100%;
}
.graph {
  width: 100%;
  height: 100%;
}
.empty {
  position: absolute;
  display: flex;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
}
.toolbutton {
  display: flex;
  border-radius: 6px;
}
.toolbutton .search {
  display: flex;
  align-items: center;
  border: 1px solid #e6e8eb;
  border-radius: 6px 0 0 6px;
}
.toolbutton .search .searchselect {
  overflow: hidden;
  width: 0;
  flex-shrink: 0;
  transition: width 0.3s;
}
.toolbutton .search button {
  width: 36px;
  flex-shrink: 0;
  border: none;
}
.toolbutton .search :global(.ant-select-selector) {
  border: none !important;
}
.toolbutton .search :global(.ant-select-focused) {
  border: 1px solid #2989ff;
  border-radius: 6px;
}
.toolbutton .btns {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e6e8eb;
  border-radius: 0 6px 6px 0;
  border-left: none;
}
.toolbutton .btns button {
  width: 36px;
  height: 36px;
  border: 1px solid #fff;
}
.toolbar {
  display: flex;
  height: 100%;
  align-items: center;
  justify-content: center;
}
.toolbar .runAll {
  display: flex;
  height: 28px;
  align-items: center;
  border-radius: 4px;
  margin-right: 8px;
  background-color: #9a0000;
  color: white;
}
.toolbar .runAll:hover {
  background-color: #9a0000 !important;
  color: white !important;
}
.toolbar .notRunAll:hover {
  background-color: #f6f8fa !important;
  color: #c1615a !important;
}
.toolbar .disabledBtn:hover {
  background-color: #f6f8fa !important;
  color: rgba(0, 0, 0, 0.25) !important;
}
.toolbar .runAllDisabled {
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  background-color: #f6f8fa !important;
  color: rgba(0, 0, 0, 0.88) !important;
}
.toolbar .runAllDisabled:hover {
  background-color: #f6f8fa !important;
}
.toolbar .runAllDisabled :global .ant-btn-text:disabled {
  display: flex;
  height: 100%;
  align-items: center;
}
.toolbar .runAllDisabled :global .ant-btn-icon svg > g > path:last-child {
  fill: rgba(0, 0, 0, 0.15);
}
.toolbar button {
  padding: 4px 7px;
  color: rgba(0, 0, 0, 0.65);
  font-size: 12px;
  font-weight: 400;
}
.toolbar button :global(.ant-btn-icon) {
  margin-inline-end: 4px !important;
}
.toolbar button.active {
  color: #c1615a;
}
.popoverContent {
  width: 208px;
}
.popoverContent :global(.ant-popover-inner) {
  border-radius: 8px;
}
.popoverContent .title {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.popoverContent .title .titleText {
  width: 40px;
  height: 22px;
  color: #000;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 22px;
}
.popoverContent .descContent {
  margin-top: -8px;
}
.popoverContent .descContent .text {
  width: 184px;
  height: 36px;
  color: rgba(0, 0, 0, 0.65);
  font-size: 12px;
  font-weight: 400;
}
.popoverContent .descContent img {
  width: 100%;
  height: 144px;
  background-color: #f8f8fb;
}
.tooltip-title {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 4px;
}
