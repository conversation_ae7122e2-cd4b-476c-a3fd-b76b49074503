.nodePopover {
  :global(.ant-popover-inner) {
    width: 520px;
    height: 530px;
    padding: 12px 16px;
    background-color: #fff;
    box-shadow: 0 3px 6px -4px rgb(0 0 0 / 12%), 0 6px 16px 0 rgb(0 0 0 / 8%),
      0 9px 28px 8px rgb(0 0 0 / 5%);
  }

  :global(.anticon-pushpin) {
    color: #1563fe;
    cursor: pointer;
  }

  .title {
    margin-right: 10px;
  }

  :global(.ant-typography) {
    position: absolute;
    bottom: 15px;
  }

  :global(.ant-popover-inner-content) {
    height: 100%;
  }

  :global(.ant-table-wrapper) {
    height: 100%;
  }

  :global(.ant-spin-nested-loading) {
    height: 100%;
  }

  :global(.ant-spin-container) {
    height: 100%;
  }

  :global(.ant-table) {
    position: relative;
    overflow: auto;
    height: calc(100% - 77px);
  }
}

.nodeData {
  width: 295px;
  height: 102px;
  padding: 10px;
  border-radius: 8px;
  background-color: rgb(0 0 0 / 2%);

  p {
    margin: 0;
    font-weight: 500;

    svg {
      margin-right: 4px;
    }
  }

  .list {
    margin-top: 12px;
    margin-left: 18px;

    .nodeList {
      margin-bottom: 10px;
    }
  }
}
