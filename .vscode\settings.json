{"files.associations": {".vscode/*.json": "jsonc", "turbo.json": "jsonc", "nx.json": "jsonc", "tsconfig.*.json": "jsonc", "tsconfig.json": "jsonc"}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/coverage": true}, "editor.insertSpaces": true, "[typescript]": {"editor.tabSize": 2, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.tabSize": 2, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.tabSize": 2, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "[jsonc]": {"editor.tabSize": 2, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "typescript.tsdk": "node_modules/typescript/lib", "files.insertFinalNewline": true, "editor.rulers": [88], "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.fixAll.stylelint": "explicit"}, "[markdown]": {"editor.quickSuggestions": {"other": true, "comments": true, "strings": true}}, "cSpell.allowCompoundWords": true, "cSpell.enabled": true, "cSpell.words": ["ahooks", "ahook<PERSON><PERSON>s", "amet", "antd", "ANTLR", "antv", "<PERSON><PERSON>", "APIV", "<PERSON><PERSON>", "cmdk", "CUDA", "deduped", "deduplication", "dtype", "dumi", "<PERSON><PERSON><PERSON>", "esbuild", "estree", "favicons", "gfind", "GFLOPS", "Immer", "immerable", "interactjs", "isinstance", "itertools", "jmespath", "lcov", "mfsu", "nocheck", "npmrc", "nrwl", "numpy", "odps", "Picklable", "pnpm", "prettierrc", "reconstructor", "reindex", "repr", "rtype", "scql", "strat", "stringifier", "Struct", "Structs", "stylelintrc", "svgr", "tempy", "tnpm", "tsup", "Turborepo", "umij<PERSON>", "umirc", "unstage", "urlunsplit", "yoctocolors"]}