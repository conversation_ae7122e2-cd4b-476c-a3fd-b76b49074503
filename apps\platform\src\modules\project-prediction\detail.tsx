import { LoadingOutlined } from '@ant-design/icons';
import type { DescriptionsProps, StepsProps } from 'antd';
import { Descriptions, Steps, Table } from 'antd';
import classNames from 'classnames';
import { useEffect, useState } from 'react';
import { useLocation } from 'umi';

import { StatusTag } from '@/components/status-tag';
import { useModel } from '@/util/valtio-helper';

import { LogList } from '../project-psi/components/psi-detail';

import { DetailBox } from './components/detail-box';
import { checkMatchStatus } from './components/feature-table';
import styles from './detail.less';

import { PredictionModel } from '.';

const getCurrentStep = (status: string) => {
  if (['REJECTED', 'CANCELED'].includes(status)) {
    return {
      current: 0,
      status: 'error',
    };
  }
  if (status === 'PENDING_APPROVAL') {
    return {
      current: 0,
      status: 'loading',
    };
  }
  if (status === 'RUNNING') {
    return {
      current: 1,
      status: 'loading',
    };
  }
  if (status === 'FI_PSI_COMPLETED') {
    return {
      current: 2,
      status: 'loading',
    };
  }
  if (status === 'SUCCEED') {
    return {
      current: 3,
      status: 'finish',
    };
  }
  if (status === 'FAILED') {
    return {
      current: 2,
      status: 'error',
    };
  }
  return {
    current: 0,
    status: 'loading',
  };
};

const PredictionDetail = () => {
  const model = useModel(PredictionModel);
  const descData =
    model.currentPrediction ||
    JSON.parse(localStorage.getItem('currentPredictionItem') || '{}');

  const { pathname } = useLocation();
  const isMessageCenter = pathname.includes('/edge/messages');
  const descriptionInfos: DescriptionsProps['items'] = [
    {
      key: 'jobName',
      label: '任务名称',
      children: descData.jobName,
    },
    {
      key: 'description',
      label: '描述',
      children: descData.description || '-',
    },
    {
      key: 'startTime',
      label: '发起时间',
      children: descData.startTime || '-',
    },
    {
      key: 'finishedTime',
      label: '完成时间',
      children: descData.finishedTime || '-',
    },
    {
      key: 'projectName',
      label: '模型项目来源',
      children: descData.sourceProjectName,
    },
    {
      key: 'modelName',
      label: '模型名称',
      children: descData.modelName,
    },
    {
      key: 'receiverParties',
      label: '结果接收方',
      children: descData.receiverParties.join(','),
    },
    {
      key: 'resultColumns',
      label: '预测结果列名',
      children: descData.resultColumns,
    },
  ];

  const partyConfigs = JSON.parse(descData.partyConfig);

  const initiatorData = {
    nodeId: descData.initiator,
    datatableId: descData.initiatorDatatableName,
    indexId: JSON.parse(descData.initiatorKeyColumns).join(','),
    features: partyConfigs.find((item: any) => item.nodeId === descData.initiator)
      ?.features,
  };

  const partnerData = {
    nodeId: descData.partner,
    datatableId: descData.partnerDatatableName,
    indexId: JSON.parse(descData.partnerKeyColumns).join(','),
    features: partyConfigs.find((item: any) => item.nodeId === descData.partner)
      ?.features,
  };

  const initiatorDataInfos: DescriptionsProps['items'] = [
    {
      key: 'nodeId',
      label: '预测节点',
      children: initiatorData.nodeId,
    },
    {
      key: 'datatableId',
      label: '数据表',
      children: initiatorData.datatableId,
    },
    {
      key: 'indexId',
      label: '索引列',
      children: initiatorData.indexId,
    },
    {
      key: 'matchStatus',
      label: '匹配状态',
      className: styles.matchStatus,
      children: checkMatchStatus(initiatorData.features) ? (
        <StatusTag type="success" text="匹配成功" />
      ) : (
        <StatusTag type="failed" text="匹配失败" />
      ),
    },
  ];

  const partnerDataInfos: DescriptionsProps['items'] = [
    {
      key: 'nodeId',
      label: '预测节点',
      children: partnerData.nodeId,
    },
    {
      key: 'datatableId',
      label: '数据表',
      children: partnerData.datatableId,
    },
    {
      key: 'indexId',
      label: '索引列',
      children: partnerData.indexId,
    },
    {
      key: 'matchStatus',
      label: '匹配状态',
      className: styles.matchStatus,
      children: checkMatchStatus(partnerData.features) ? (
        <StatusTag type="success" text="匹配成功" />
      ) : (
        <StatusTag type="failed" text="匹配失败" />
      ),
    },
  ];

  const columns = [
    {
      title: '序号',
      key: 'index',
      render: (_: any, __: any, index: number) => <div>{index + 1}</div>,
    },
    {
      title: '入模特征',
      dataIndex: 'offlineName',
      key: 'offlineName',
    },
    {
      title: '在线特征',
      dataIndex: 'onlineName',
      key: 'onlineName',
    },
  ];

  const processStepItems: StepsProps['items'] = [
    { title: '数据加载' },
    { title: '隐私求交' },
    { title: '模型预测' },
    { title: '结果产出' },
  ];

  const { current: stepCurrent, status: stepStatus } = getCurrentStep(descData.status);
  if (stepStatus === 'loading') {
    processStepItems[stepCurrent].icon = <LoadingOutlined />;
  } else {
    processStepItems[stepCurrent].status = stepStatus as
      | 'error'
      | 'wait'
      | 'process'
      | 'finish'
      | undefined;
  }

  const [logsList, setLogsList] = useState<API2.PSILogsResponse['data']>([]);
  useEffect(() => {
    if (!isMessageCenter) {
      model
        .getJobLogs({
          jobId: descData.jobId,
          nodeId: descData.currentNodeId,
        })
        .then((res) => {
          if (res) {
            setLogsList(res);
          }
        });
    }
  }, []);

  return (
    <div
      className={classNames(styles.predictionDetail, {
        [styles.detailInMessage]: isMessageCenter,
      })}
    >
      <DetailBox title="基本信息">
        <Descriptions column={3} items={descriptionInfos} />
      </DetailBox>
      <DetailBox title="发起方数据" showCollapse>
        <Descriptions
          style={{ marginBottom: 16 }}
          column={4}
          items={initiatorDataInfos}
        />
        <Table
          size="small"
          columns={columns}
          dataSource={initiatorData.features}
          rowKey="offlineName"
        />
      </DetailBox>
      <DetailBox title="合作方数据" showCollapse>
        <Descriptions
          style={{ marginBottom: 16 }}
          column={4}
          items={partnerDataInfos}
        />
        <Table
          size="small"
          columns={columns}
          dataSource={partnerData.features}
          rowKey="offlineName"
        />
      </DetailBox>
      {!isMessageCenter && (
        <>
          <DetailBox title="执行进度">
            <div className={styles.stepWrapper}>
              <Steps
                size="small"
                current={stepCurrent}
                items={processStepItems}
              ></Steps>
            </div>
          </DetailBox>
          <DetailBox title="执行日志">
            <div className={styles.logWrapper}>
              <LogList
                type="fi"
                logsList={logsList}
                jobId={descData.jobId}
                nodeId={descData.currentNodeId}
              />
            </div>
          </DetailBox>
        </>
      )}
    </div>
  );
};

export default PredictionDetail;
