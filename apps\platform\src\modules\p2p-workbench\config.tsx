import type { RenderConfig } from 'pb-bi-render';
import { ItemType } from 'pb-bi-render';

import { Message } from './component/message';
import { Nodes } from './component/nodes';

export const pageConfig: RenderConfig = {
  colNum: 3,
  padding: 0,
  col<PERSON>utter: 16,
  row<PERSON>utter: 16,
  // autofit: true,
  children: [
    {
      key: 'custom1-62958b0b',
      type: ItemType.CUSTOM,
      id: 'custom1',
      col: 1,
      row: 2,
      rowSpan: 1,
      colSpan: 3,
      // height: 'auto',
      customNode: Message,
    },
    {
      key: 'custom1-d6e026d1',
      type: ItemType.CUSTOM,
      id: 'custom1',
      col: 1,
      row: 3,
      rowSpan: 1,
      colSpan: 3,
      customNode: Nodes,
    },
    // {
    //   key: 'custom1-7e996842',
    //   type: ItemType.CUSTOM,
    //   id: 'custom1',
    //   col: 1,
    //   row: 2,
    //   rowSpan: 2,
    //   colSpan: 3,
    //   customNode: () => <div>custom3</div>,
    // },
    {
      key: 'bar-16565a41',
      type: ItemType.CHART,
      id: 'bar',
      col: 2,
      row: 1,
      rowSpan: 1,
      colSpan: 1,
      height: 300,
      itemBackground: '#fff',
      chartOptions: {
        title: { text: '模型服务访问态势' },
        grid: {},
        tooltip: {},
        xAxis: { data: ['3-10', '3-11', '3-12', '3-13', '3-14', '3-15'] },
        yAxis: {},
        series: [
          { name: '模型服务访问量', type: 'bar', data: [232, 248, 223, 368, 329, 205] },
        ],
      },
    },
    {
      key: 'line-8bebd98a',
      type: ItemType.CHART,
      id: 'line',
      col: 1,
      row: 1,
      rowSpan: 1,
      colSpan: 1,
      height: 300,
      itemBackground: '#fff',
      chartOptions: {
        title: { text: '任务执行趋势' },
        xAxis: {
          type: 'category',
          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        },
        yAxis: { type: 'value' },
        series: [{ data: [150, 230, 224, 218, 135, 147, 260], type: 'line' }],
      },
    },
    {
      key: 'bar-c2f9b1e1',
      type: ItemType.CHART,
      id: 'bar',
      col: 3,
      row: 1,
      rowSpan: 1,
      colSpan: 1,
      height: 300,
      itemBackground: '#fff',
      chartOptions: {
        title: { text: '日志审计量' },
        tooltip: {},
        xAxis: { data: ['3-10', '3-11', '3-12', '3-13', '3-14', '3-15'] },
        yAxis: {},
        series: [
          {
            name: '日志审计量',
            type: 'bar',
            data: [1155, 2045, 3672, 4234, 2234, 2934],
          },
        ],
      },
    },
  ],
};
