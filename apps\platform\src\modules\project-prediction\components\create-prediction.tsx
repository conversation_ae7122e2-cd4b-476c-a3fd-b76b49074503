import type { FormInstance } from 'antd';
import { Drawer, Space, Flex, Button } from 'antd';
import { useEffect, useMemo, useRef, useState } from 'react';

import { AutoModal } from '@/components/auto-modal';
import FormBuilder from '@/components/form-builder/form';
import type { FormItemConfig } from '@/components/form-builder/search-form';
import { FormItemType } from '@/components/form-builder/search-form';
import { PredictionModel } from '@/modules/project-prediction';
import { useModel } from '@/util/valtio-helper';

import { FeatureTable } from './feature-table';

interface IProps {
  open: boolean;
  onClose: () => void;
  onConfirm?: (data: any) => void;
}

export const CreatePredictionModal = (props: IProps) => {
  const { open, onClose, onConfirm } = props;
  const formRef = useRef<{ form: FormInstance }>(null);
  const model = useModel(PredictionModel);

  const initialConfig: FormItemConfig[] = [
    {
      name: 'name',
      type: FormItemType.INPUT,
      itemProps: {
        label: '任务名称',
        rules: [
          { required: true, message: '请输入任务名称' },
          {
            pattern: /^[a-zA-Z0-9_\u4e00-\u9fa5-]+$/,
            message: '请输入中文、大小写英文、数字、下划线、中划线',
          },
          { max: 32, message: '请输入32个字符以内' },
        ],
      },
      props: {
        placeholder: '请输入中文、大小写英文、数字、下划线、中划线，32个字符以内',
      },
    },
    {
      name: 'projectId',
      type: FormItemType.CONTROLLED_SELECT,
      itemProps: {
        label: '模型项目来源',
        rules: [{ required: true, message: '请选择' }],
      },
      props: {
        placeholder: '请选择',
      },
    },
    {
      name: 'modelId',
      type: FormItemType.CONTROLLED_SELECT,
      itemProps: {
        label: '选择模型',
        rules: [{ required: true, message: '请选择' }],
      },
      props: {
        placeholder: '请选择',
      },
      watchFields: ['projectId'],
      watchHooks: {
        projectId: (value: any, form: FormInstance) => {
          model.getProjectModelList(value).then(() => {
            const ins = form.getFieldInstance('modelId');
            const options =
              model.modelList
                ?.filter((item) => item.modelStats !== 'DISCARDED')
                .map((item) => ({
                  label: item.modelName,
                  value: item.modelId,
                })) || [];
            ins.setOptions(options);
            form.setFieldValue('modelId', undefined);
          });
        },
      },
    },
    {
      name: 'datasets1',
      type: FormItemType.CUSTOM,
      itemProps: {
        required: true,
        label: '本地数据',
        validateStatus: 'success',
        initialValue: {
          nodeId: undefined,
          datatableId: undefined,
          indexId: undefined,
          features: undefined,
        },
        rules: [
          {
            validator(rule, value) {
              if (!value) return Promise.reject(new Error('请填写本地数据信息'));
              console.log(value, 'value validating');
              for (const key in value) {
                if (value[key] === undefined) {
                  return Promise.reject(new Error('请补全信息'));
                }
              }
              for (const feature of value?.features || []) {
                if (feature.onlineName === undefined) {
                  return Promise.reject(new Error('请补全在线特征'));
                }
              }
              return Promise.resolve();
            },
          },
        ],
      },
      customNode: () => (
        <FeatureTable
          parties={model.parties}
          modelDetail={model.modelDetail}
          type="local"
          disabled={model.datasetDisabled}
        />
      ),
      watchFields: ['modelId', 'projectId'],
      watchHooks: {
        modelId: (value: string, form: FormInstance) => {
          const projectId = form.getFieldValue('projectId');
          const modelItem = model.modelList.find((item) => item.modelId === value);
          console.log(value, form, modelItem, 'watched modelid change');
          if (modelItem) {
            model.getModelDetail(value, projectId).then(() => {
              model.datasetDisabled = false;
              setConfig([...initialConfig]);
            });
          }
        },
        projectId: (value: string, form: FormInstance) => {
          form.setFieldValue('datasets1', {
            nodeId: model.parties?.[0].nodeId,
            datatableId: undefined,
            indexId: undefined,
            features: undefined,
          });
          model.datasetDisabled = true;
          setConfig([...initialConfig]);
          console.log(value, form, 'watched projectid change');
        },
      },
    },
    {
      name: 'datasets2',
      type: FormItemType.CUSTOM,
      itemProps: {
        required: true,
        label: '外部数据集',
        validateStatus: 'success',
        initialValue: {
          nodeId: undefined,
          datatableId: undefined,
          indexId: undefined,
          features: undefined,
        },
        rules: [
          {
            validator(rule, value) {
              if (!value) return Promise.reject(new Error('请选择数据集'));
              for (const key in value) {
                if (value[key] === undefined) {
                  return Promise.reject(new Error('请补全信息'));
                }
              }
              for (const feature of value?.features || []) {
                if (feature.onlineName === undefined) {
                  return Promise.reject(new Error('请补全在线特征'));
                }
              }
              return Promise.resolve();
            },
          },
        ],
      },
      customNode: () => (
        <FeatureTable
          parties={model.parties}
          modelDetail={model.modelDetail}
          type="remote"
        />
      ),
      watchFields: ['projectId'],
      watchHooks: {
        projectId: (value: string, form: FormInstance) => {
          form.setFieldValue('datasets2', {
            nodeId: undefined,
            datatableId: undefined,
            indexId: undefined,
            features: undefined,
          });
        },
      },
    },
    {
      name: 'resultColumns',
      type: FormItemType.INPUT,
      itemProps: {
        label: '预测结果列名',
        rules: [{ required: true, message: '请输入预测结果列名' }],
      },
      props: {
        placeholder: '请输入预测结果列名',
      },
    },
    {
      name: 'description',
      type: FormItemType.TEXTAREA,
      itemProps: {
        label: '任务描述',
      },
      props: {
        placeholder: '请输入任务描述',
      },
    },
  ];

  const [config, setConfig] = useState(initialConfig);

  useEffect(() => {
    if (open) {
      model.getProjectList().then((data) => {
        formRef.current?.form.getFieldInstance('projectId').setOptions(
          data.map((item) => ({
            value: item.projectId,
            label: item.projectName,
          })),
        );
      });
    }
  }, [open]);

  const memoFormBuilder = useMemo(() => {
    return (
      <FormBuilder ref={formRef} config={config} formConfig={{ layout: 'vertical' }} />
    );
  }, [config]);

  const submit = () => {
    formRef.current?.form.validateFields().then((values) => {
      onConfirm?.(values);
      console.log(values, 'values');
    });
  };

  const cancelDrawer = () => {
    formRef.current?.form.resetFields();
    onClose();
  };
  return (
    <AutoModal
      title="新建任务"
      width={700}
      open={open}
      maskClosable={false}
      onCancel={cancelDrawer}
      footer={
        <Flex justify="end">
          <Space>
            <Button onClick={cancelDrawer}>取消</Button>
            <Button type="primary" onClick={submit} loading={model.createLoading}>
              提交
            </Button>
          </Space>
        </Flex>
      }
    >
      {memoFormBuilder}
    </AutoModal>
  );
};
