.main {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  border-radius: 8px;
  background-image: linear-gradient(270deg, #f0f4fa 0%, #fff 100%);
  box-shadow: 0 2px 4px 0 rgb(0 0 0 / 5%);

  .edit {
    padding-top: 15px;
    padding-left: 20px;

    .editIcon {
      width: 94px;
      height: 24px;
      border-radius: 4px;
      background-color: rgb(0 0 0 / 45%);
      color: #fff;
      cursor: pointer;
      font-size: 14px;
      line-height: 24px;
      text-align: center;

      :global(.anticon-export) {
        margin-right: 3px;
      }
    }
  }

  .create {
    padding-left: 66px;

    .title {
      padding-top: 33px;
      color: rgb(0 0 0 / 85%);
      font-size: 36px;
      font-weight: 500;
      line-height: 48px;
    }

    .titleDesc {
      padding-top: 8px;
      color: rgb(0 0 0 / 85%);
      font-size: 16px;
      font-weight: 400;
      line-height: 18px;
    }

    .nodeContent {
      margin-top: 58px;
    }
  }
}
