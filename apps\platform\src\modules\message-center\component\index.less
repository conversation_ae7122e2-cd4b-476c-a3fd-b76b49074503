.listItemTitleName {
  color: rgb(0 0 0 / 85%);
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}

.listItemTitleName:hover {
  color: #9a0000;
}

.listItemDescText {
  color: rgb(0 0 0 / 65%);
  font-size: 14px;
  font-weight: 400;
}

.listItemTitleTypeTag {
  display: flex;
  width: 80px;
  height: 20px;
  align-items: center;
  border: 1px solid rgb(0 0 0 / 6%);
  border-radius: 4px;
  margin-right: 0;
  color: rgb(0 10 26 / 88%);
  font-size: 10px;
  font-weight: 400;
}

.messageStateTagContent {
  display: flex;
  cursor: default;

  .label {
    display: flex;
    height: 20px;
    align-items: center;
    justify-content: center;
    border: none !important;
    border-radius: 4px 0 0 4px;
    margin: 0;
    color: #fff !important;
    font-size: 10px;
    font-weight: 500;
  }

  .statusText {
    display: flex;
    width: 52px;
    height: 20px;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    border-radius: 0 4px 4px 0;
    border-left: none !important;
    margin-right: 0;
    font-size: 10px;
    font-weight: 400;
  }
}

.nodeStatusListContent {
  display: flex;
  margin: 8px 0;
  margin-top: 0;
  gap: 8px;

  .text {
    color: rgb(0 0 0 / 88%);
    font-size: 14px;
    font-weight: 400;
  }

  .action {
    width: 65px;
    white-space: nowrap;
  }
}

.reasonTooltipContent {
  word-break: break-all;
}

.btnAgree {
  color: #1677ff;
}

.btnDisagree {
  color: #f5222d;
}

.messageStatus {
  .statusText {
    margin: 0 6px;
  }
}
