import type { ColumnsType } from 'antd/es/table';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';

import type { FormItemConfig } from '@/components/form-builder/search-form';
import { FormItemType } from '@/components/form-builder/search-form';
import { StatusTag } from '@/components/status-tag';
import type { LogListVO } from '@/services/secretpad/LogController';

export const LogListSearchConfig: FormItemConfig[] = [
  {
    name: 'dateRange',
    type: FormItemType.RANGE_PICKER,
    itemProps: {
      label: '日期范围',
      initialValue: [dayjs().subtract(6, 'day'), dayjs()],
    },
    props: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      disabledDate: (current: Dayjs) => {
        return current < dayjs().subtract(30, 'day') || current > dayjs();
      },
    },
  },
  {
    name: 'level',
    type: FormItemType.SELECT,
    itemProps: {
      label: '日志级别',
      style: {
        width: 260,
      },
    },
    props: {
      placeholder: '请选择',
      options: [
        { label: '查询', value: 'INFO' },
        { label: '错误', value: 'ERROR' },
        { label: '警告', value: 'WARN' },
        { label: '调试', value: 'DEBUG' },
      ],
    },
  },
  {
    name: 'resultStatus',
    type: FormItemType.SELECT,
    itemProps: {
      label: '状态',
      style: {
        width: 260,
      },
    },
    props: {
      placeholder: '请选择',
      options: [
        { label: '成功', value: 'SUCCESS' },
        { label: '失败', value: 'FAILED' },
      ],
    },
  },
  {
    name: 'message',
    type: FormItemType.INPUT,
    itemProps: {
      label: '日志内容',
    },
    props: {
      placeholder: '请输入',
    },
  },
  {
    name: 'userId',
    type: FormItemType.INPUT,
    itemProps: {
      label: '节点ID',
    },
    props: {
      placeholder: '请输入',
    },
  },
];

export const AdminLogListSearchConfig: FormItemConfig[] = [
  {
    name: 'module',
    type: FormItemType.SELECT,
    itemProps: {
      label: '模块',
      style: {
        width: 260,
      },
    },
    props: {
      placeholder: '请选择',
      options: [
        { label: 'USER', value: 'USER' },
        { label: 'JOB', value: 'JOB' },
        { label: 'DATA', value: 'DATA' },
        { label: 'PROJECT', value: 'PROJECT' },
        { label: 'SYSTEM', value: 'SYSTEM' },
        { label: 'MODEL', value: 'MODEL' },
        { label: 'PSI', value: 'PSI' },
        { label: 'FI', value: 'FI' },
        { label: 'VOTE', value: 'VOTE' },
        { label: 'NODE_INSTANCE', value: 'NODE_INSTANCE' },
      ],
    },
  },
  {
    name: 'operationType',
    type: FormItemType.SELECT,
    itemProps: {
      label: '操作类型',
      style: {
        width: 260,
      },
    },
    props: {
      placeholder: '请选择',
      options: [
        { label: '登录', value: 'LOGIN' },
        { label: '登出', value: 'LOGOUT' },
        { label: '创建', value: 'CREATE' },
        { label: '更新', value: 'UPDATE' },
        { label: '删除', value: 'DELETE' },
        { label: '查询', value: 'DOWNLOAD' },
        { label: '其他', value: 'UPLOAD' },
        { label: '取消', value: 'CANCEL' },
        { label: '重启', value: 'RESTART' },
        { label: '停止', value: 'STOP' },
        { label: '归档', value: 'ARCHIVE' },
        { label: '添加', value: 'ADD' },
        { label: '授权', value: 'GRANT' },
      ],
    },
  },
  {
    name: 'clientIp',
    type: FormItemType.INPUT,
    itemProps: {
      label: '客户端IP',
    },
    props: {
      placeholder: '请输入',
    },
  },
];

export interface LogListData extends LogListVO {
  projectId: string;
  level: string;
  projectName: string;
  jobName: string;
  jobStatus: string;
  timestamp: string;
  jobId?: string;
  errMsg?: string;
  jobType?: string;
  toStatus?: string;
  aggregatedLogs: {
    label: string;
    status: string;
    log: string;
    jobId: string;
    taskId: string;
    projectId: string;
  }[];
}

export interface AdminLogListData extends LogListVO {
  clientIp?: string;
  module?: string;
  operationType?: string;
  resultStatus?: string;
  requestParams?: string;
  requestPath?: string;
  responseData?: string;
  userId?: string;
  sessionId?: string;
}

export const getStatusLabel = (status: string) => {
  switch (status?.toLocaleLowerCase()) {
    case 'succeed':
    case 'success':
      return '成功';
    case 'failed':
      return '失败';
    default:
      return '失败';
  }
};

export const getStatusType = (status: string) => {
  console.log(status, 'status');
  switch (status?.toLocaleLowerCase()) {
    case 'succeed':
    case 'success':
      return 'success';
    default:
      return 'failed';
  }
};

export const LogListColumns: ColumnsType<Partial<LogListData>> = [
  // {
  //   key: 'level',
  //   title: '日志类型',
  //   dataIndex: 'level',
  // },
  {
    key: 'projectName',
    title: '项目名称',
    render: (_, record) => {
      if (record.jobType === 'PSI') {
        return '隐私求交';
      } else if (record.jobType === 'FI') {
        return '联合预测';
      }
      return record.projectName;
    },
  },
  {
    key: 'jobName',
    title: '任务名称',
    dataIndex: 'jobName',
  },
  {
    key: 'jobStatus',
    title: '执行状态',
    render: (_, record) => {
      const label = getStatusLabel(record.jobStatus || record.toStatus || '');
      return (
        <StatusTag
          type={getStatusType(record.jobStatus || record.toStatus || '')}
          text={label}
        ></StatusTag>
      );
    },
  },
  {
    key: 'timestamp',
    title: '操作时间',
    dataIndex: 'timestamp',
    render: (_, record) => {
      return <div>{dayjs(Number(record.timestamp)).format('YYYY-MM-DD HH:mm:ss')}</div>;
    },
  },
];

export const AdminLogListColumns: ColumnsType<Partial<AdminLogListData>> = [
  // {
  //   key: 'level',
  //   title: '日志类型',
  //   dataIndex: 'level',
  // },
  {
    key: 'logger',
    title: '日志名称',
    dataIndex: 'logger',
  },
  {
    key: 'module',
    title: '模块',
    dataIndex: 'module',
  },
  {
    key: 'operationType',
    title: '操作类型',
    dataIndex: 'operationType',
  },
  {
    key: 'resultStatus',
    title: '执行状态',
    dataIndex: 'resultStatus',
    render: (status) => {
      const label = getStatusLabel(status);
      return <StatusTag type={getStatusType(status)} text={label}></StatusTag>;
    },
  },
  {
    key: 'clientIp',
    title: '客户端IP',
    dataIndex: 'clientIp',
  },
  {
    key: 'timestamp',
    title: '操作时间',
    dataIndex: 'timestamp',
    render: (_, record) => {
      return <div>{dayjs(Number(record.timestamp)).format('YYYY-MM-DD HH:mm:ss')}</div>;
    },
  },
];
