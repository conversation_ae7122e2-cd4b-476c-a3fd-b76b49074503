.main {
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: column;
  align-items: center;
}

.goToHome {
  position: absolute;
  top: 18px;
  left: -16px;
  display: flex;
  width: 30px;
  height: 42px;
  align-items: center;
  justify-content: center;
  border-radius: 5px;
  background: #fff;
  color: #666;
  cursor: pointer;
  font-size: 20px;
}

.nodeInfo {
  display: flex;
  width: 98%;
  height: 82px;
  box-sizing: border-box;
  align-items: center;
  padding: 0 24px;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 2px 4px 0 rgb(0 0 0 / 5%);

  .currentNode {
    display: flex;
    flex: 1;
    flex-direction: column;
  }

  .nodeNameAndStatus {
    display: flex;
    height: 100%;
    flex: 1;
    align-items: center;
    padding: 9px 0;

    .nodeName {
      color: rgb(0 0 0 / 88%);
      cursor: pointer;
      font-size: 20px;
      font-weight: 500;
    }

    .nodeNameSpread {
      margin-left: 8px;
      color: rgb(0 0 0 / 45%);
      font-size: 14px;
    }

    .nodeStatus {
      margin-left: 14px;

      :global {
        .ant-tag-success {
          border: none;
          background: rgb(216 251 231 / 100%);
          color: rgb(35 182 95 / 100%);
        }
      }
    }
  }

  .nodeId {
    color: rgb(0 0 0 / 45%);
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0;
  }

  .tips {
    display: flex;
    width: 340px;
    align-items: center;
    color: #525964;
    font-size: 14px;
  }
}

.mainContent {
  width: 98%;
  height: 100px;
  flex: 1;
  border-radius: 8px;
  margin-top: 24px;

  :global {
    .ant-tabs,
    .ant-tabs-content-holder,
    .ant-tabs-content,
    .ant-tabs-tabpane {
      width: 100%;
      height: 100%;
    }

    .ant-tabs-card > .ant-tabs-nav .ant-tabs-tab {
      background: rgb(0 0 0 / 8%);
    }

    .ant-tabs-card > .ant-tabs-nav .ant-tabs-tab-active {
      background: #fff;
    }

    .ant-tabs-content-holder {
      overflow: auto;
      border-radius: 0 6px 6px;
    }

    .ant-tabs-nav {
      margin-bottom: 0;
    }

    .ant-tabs-tab {
      width: 104px;
      height: 48px;
      padding: 8px 24px !important;
      margin-left: 0 !important;
    }

    .ant-tabs-tab:nth-child(1) {
      border: 0;
      border-radius: 8px 0 0 !important;
    }

    .ant-tabs-tab:nth-child(2) {
      border: 0;
      border-radius: 0 8px 0 0 !important;
    }
  }
}
