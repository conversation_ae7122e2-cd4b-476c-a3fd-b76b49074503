.main {
  display: flex;
  overflow: auto;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  flex-direction: column;
  padding: 20px;
  padding-bottom: 0;
  border-radius: 8px;
  // background: #fff;
}

.toolbar {
  display: flex;
}

.content {
  flex: 1;
  padding: 20px;
  border-radius: 8px;
  background-color: #fff;

  // :global(.ant-table-thead) {
  //   height: 54px;
  // }

  // :global(.ant-table-row) {
  //   height: 54px;
  // }

  // :global(.ant-pagination) {
  //   position: sticky;
  //   bottom: 0;
  //   padding: 16px 16px 16px 0;
  //   margin: 0 !important;
  //   background-color: #fff;
  // }
}

.authProjectListPopover {
  overflow: auto;
  max-height: 200px;
  padding: 5px 10px;
  border-radius: 2px;
  background: #f5f5f5;
}

.authProjectListPopoverItem {
  // overflow: hidden;
  // max-width: 200px;
  padding: 2px 0;
  // text-overflow: ellipsis;
  // white-space: nowrap;
}

.uploadTitle {
  display: flex;

  .uploadText {
    margin-right: 9px;
  }

  .uploadIcon {
    color: rgb(0 0 0 / 45%);
    cursor: pointer;
  }
}

.uploadLoading {
  color: rgb(0 0 0 / 45%);
}

.uploadTag {
  :global(.ant-tag) {
    margin-right: 0;
  }

  :global(.ant-btn) {
    padding: 4px 7px;
  }
}

:global {
  .dataauth-tour {
    width: 353px;
  }

  .dataauth-tour .ant-tour-inner .ant-tour-footer .ant-tour-buttons {
    margin-inline-start: 0%;
  }
}

.uploadBtn {
  padding: 0;
}
