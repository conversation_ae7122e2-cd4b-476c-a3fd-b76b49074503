# Fix end-of-lines in Git versions older than 2.10
# https://github.com/git/git/blob/master/Documentation/RelNotes/2.10.0.txt#L248	# https://github.com/git/git/blob/master/Documentation/RelNotes/2.10.0.txt#L248
* text=auto eol=lf

# ===
# Binary Files (don't diff, don't fix line endings)
# ===

# Images
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.tiff binary

# Fonts
*.oft binary
*.ttf binary
*.eot binary
*.woff binary
*.woff2 binary

# Videos
*.mov binary
*.mp4 binary
*.webm binary
*.ogg binary
*.mpg binary
*.3gp binary
*.avi binary
*.wmv binary
*.flv binary
*.asf binary

# Audio
*.mp3 binary
*.wav binary
*.flac binary

# Compressed
*.gz binary
*.zip binary
*.7z binary
*.tar.gz binary
*.tgz binary
