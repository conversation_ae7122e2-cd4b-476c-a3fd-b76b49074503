.projectWrapper :global .ant-select {
  width: 228px;
  height: 30px;
  border-radius: 8px !important;
  color: #fefefe;
}
.projectWrapper :global .ant-select-single .ant-select-arrow {
  color: #fff;
}
.projectWrapper :global .ant-select-single .ant-select-arrow .anticon-caret-down {
  pointer-events: none;
}
.projectWrapper :global .ant-select-open .ant-select-arrow {
  color: rgba(0 0 0/25%) !important;
}
.projectWrapper :global .ant-select-focused:not(.ant-select-disabled).ant-select:not(
        .ant-select-customize-input
      ) .ant-select-selector {
  box-shadow: none;
}
.projectWrapper :global .ant-select:not(.ant-select-customize-input) .ant-select-selector {
  align-items: center;
  border: none;
  background-color: #741109;
  color: #a96c67;
}
.projectWrapper :global .ant-select-focused:not(.ant-select-disabled).ant-select:not(
        .ant-select-customize-input
      ).ant-select-selector {
  border: none !important;
}
.projectWrapper :global .ant-select-selector {
  border: none !important;
}
.projectWrapper :global .ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
  height: 28px;
}
.projectWrapper :global .ant-select-selection {
  border: none !important;
  box-shadow: none;
}
.projectWrapper :global .ant-select-selection--single {
  box-shadow: none;
}
.projectWrapper :global .ant-select-selection-item {
  display: flex;
}
.fontBold {
  overflow: hidden;
  width: 135px;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.projectDropdown {
  width: 260px !important;
  padding: 8px;
}
.projectDropdown :global .ant-select-item {
  line-height: 25px;
}
.projectDropdown :global .ant-select-item-option-content {
  font-weight: 500;
}
.projectDropdown :global .ant-select-item-option-content .ant-tag {
  font-weight: 400;
}
.projectDropdown :global .ant-select-item-option {
  border-radius: 8px;
}
.rows {
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.6);
  font-size: 12px;
  font-weight: normal;
}
.rows .nodeName {
  width: 100px;
}
.rows :global .ant-space {
  font-size: 12px;
}
.rows .line {
  width: 1px;
  height: 12px;
  margin: 0 8px;
  background: rgba(0, 10, 26, 0.16);
}
