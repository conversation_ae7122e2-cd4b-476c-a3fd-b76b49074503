import { parse } from 'query-string';
import { useEffect } from 'react';
import { useLocation } from 'umi';

import { MessageModel } from '@/modules/message-center';
import MessageTable from '@/modules/message-center/component/table';
import { MessageState } from '@/modules/message-center/message.service';
import { useModel } from '@/util/valtio-helper';

import styles from '../index.less';

import { Card } from './card';

export const Message = () => {
  const viewInstance = useModel(MessageModel);
  const { pathname } = useLocation();

  const { ownerId } = parse(window.location.search);
  if (ownerId) {
    viewInstance.ownerId = ownerId as string;
  }

  const onProcess = (params: API.VoteReplyRequest) => {
    viewInstance.processMessage(params, pathname);
  };

  const onShowDrawer = (voteId: string) => {
    const record = viewInstance.messageService.messageList.find(
      (i) => i.voteID === voteId,
    );
    if (record) viewInstance.showInfoDrawer(record);
  };
  useEffect(() => {
    viewInstance.pageSize = 5;
    viewInstance.filterState = MessageState.PENDING;
    viewInstance.getList();
  }, []);
  return (
    <div className={styles.messageWrapper}>
      <Card title="待我审批">
        <MessageTable
          type="process"
          data={viewInstance.messageService.messageTableData}
          loading={viewInstance.messageService.loading}
          size="middle"
          hideScroll
          pagination={{
            total: viewInstance.totalNum || 1,
            current: viewInstance.pageNumber,
            pageSize: 5,
            showTotal: (total) => `共 ${total} 条`,
            showSizeChanger: true,
            onChange: (page, pageSize) => {
              viewInstance.pageNumber = page;
              viewInstance.pageSize = pageSize;
              viewInstance.getList();
            },
          }}
          onProcess={onProcess}
          onShowDrawer={onShowDrawer}
        />
      </Card>
    </div>
  );
};
