.dataSheetTitle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;

  .title {
    color: rgb(0 0 0 / 88%);
    font-size: 14px;
    font-weight: 500;
    line-height: 22px;
  }

  .options {
    display: flex;
    gap: 24px;

    button {
      padding: 0;
    }
  }
}

.tableHeader {
  display: flex;
  width: 100%;
  height: 36px;
  box-sizing: border-box;
  padding: 8px 0;
  margin-bottom: 8px;
  background: rgb(0 0 0 / 2%);
  box-shadow: inset 0 -1px 0 0 #e8e9ea;
  color: rgb(0 0 0 / 85%);
  font-size: 12px;
  font-weight: 500;
  line-height: 20px;

  .tableHeaderFeature {
    width: 178px;
    border-right: 1px solid rgb(0 10 26 / 7%);
    margin-left: 32px;
  }

  .tableHeaderType {
    width: 230px;
    box-sizing: border-box;
    padding-left: 12px;
    border-right: 1px solid rgb(0 10 26 / 7%);
  }

  .tableHeaderDesc {
    width: 205px;
    box-sizing: border-box;
    padding-left: 12px;
    border-right: 1px solid rgb(0 10 26 / 7%);
  }

  .tableHeaderBtn {
    width: 40px;
    box-sizing: border-box;
    padding-left: 12px;
  }
}

.addBtn {
  margin-bottom: 8px;
}

.formFeatureName {
  width: 202px !important;
  margin-right: 16px;
}

.formFeatureDesc {
  width: 200px !important;
  margin-right: 16px;
}

.formFeatureDelete {
  margin-bottom: 24px;
  margin-left: -8px;

  &:hover {
    color: #9a0000;
  }
}
