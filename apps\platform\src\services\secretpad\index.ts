/* eslint-disable */
// API 更新时间：2024-09-02 18:26:14
// API 唯一标识：66d592c66eb33d73d5acbdf8
// 该文件由 OneAPI 自动生成，请勿手动修改！

import * as ApprovalController from './ApprovalController';
import * as AuthController from './AuthController';
import * as CenterDataSyncController from './CenterDataSyncController';
import * as CloudLogController from './CloudLogController';
import * as ComponentVersionController from './ComponentVersionController';
import * as DataController from './DataController';
import * as DataSourceController from './DataSourceController';
import * as DataSyncController from './DataSyncController';
import * as DatatableController from './DatatableController';
import * as FeatureDatasourceController from './FeatureDatasourceController';
import * as GraphController from './GraphController';
import * as IndexController from './IndexController';
import * as InstController from './InstController';
import * as MessageController from './MessageController';
import * as ModelExportController from './ModelExportController';
import * as ModelManagementController from './ModelManagementController';
import * as NodeController from './NodeController';
import * as NodeRouteController from './NodeRouteController';
import * as NodeUserController from './NodeUserController';
import * as P2PProjectController from './P2PProjectController';
import * as P2pNodeController from './P2pNodeController';
import * as ProjectController from './ProjectController';
import * as RemoteUserController from './RemoteUserController';
import * as ScheduledController from './ScheduledController';
import * as UserController from './UserController';
import * as VoteSyncController from './VoteSyncController';
export default {
  ApprovalController,
  AuthController,
  CenterDataSyncController,
  CloudLogController,
  ComponentVersionController,
  DataController,
  DataSourceController,
  DataSyncController,
  DatatableController,
  FeatureDatasourceController,
  GraphController,
  IndexController,
  InstController,
  MessageController,
  ModelExportController,
  ModelManagementController,
  NodeController,
  NodeRouteController,
  NodeUserController,
  P2PProjectController,
  P2pNodeController,
  ProjectController,
  RemoteUserController,
  ScheduledController,
  UserController,
  VoteSyncController,
};
