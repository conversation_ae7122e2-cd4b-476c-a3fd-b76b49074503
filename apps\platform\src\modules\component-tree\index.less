@import url('@/styles/mixins.less');
@import url('@/variables.less');

.components {
  width: 100%;
  height: 100%;
  background-color: #fff;

  .action {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    border-width: 1px 0;
    border-style: solid;
    border-color: rgb(0 0 0 / 8%);

    .title {
      font-weight: 700;
    }

    .search {
      width: 120px;
      height: 28px;
      margin: 0;
    }
  }

  .tree {
    overflow: auto;
    height: calc(100% - 44px);
    padding: 0 4px;

    .scrollbar-style();

    :global {
      .ant-tree-treenode {
        &:hover {
          background-color: unset;
        }

        // .ant-tree-node-content-wrapper-normal {
        //   &:hover {
        //     background-color: #f7f8fa;
        //   }
      }

      .ant-tree-indent,
      .ant-tree-switcher-noop {
        display: none;
      }

      .ant-tree-node-selected::before {
        background-color: unset !important;
      }

      .ant-tree-treenode-selected::before {
        background: transparent !important;
      }

      .ant-tree-switcher-icon {
        color: rgb(135 136 137 / 100%);
      }

      .ant-tree-node-content-wrapper:hover::before {
        background: unset !important;
      }
    }

    .dir {
      margin-left: -4px;
      color: rgb(0 10 26 / 68%);
      font-size: 12px;
    }

    .node {
      display: flex;
      height: 32px;
      align-items: center;
      padding: 0 8px;
      border: 1px solid #d4d7da;
      border-radius: 4px;
      margin: 0 4px;
      background-color: #fff;
      color: rgb(0 10 26 / 68%);
      font-size: 12px;

      &:hover {
        border: 1px solid @PrimaryColor;
        background-color: #fff;
        box-shadow: 0 0 0 2px @PrimaryBgColor;

        .nodeDragHolder {
          display: block;
        }
      }

      .nodeTitle {
        display: flex;
        flex: 1;
        align-items: center;
      }

      .nodeDragHolder {
        display: none;
        width: 10px;
      }

      .icon {
        margin-right: 8px;
        color: #bbbcbc;
        font-size: 14px;
      }
    }

    .disabled {
      opacity: 0.4;
    }
  }
}
