.drawerTitle {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.drawerTitle .drawerTitleName {
  display: flex;
  align-items: center;
}
.drawerTitle .title {
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.88);
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
}
.drawerTitle .title .left {
  width: 20px;
}
.drawerTitle .badge {
  margin-left: 16px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
}
.baseTitle {
  margin-bottom: 8px;
  color: rgba(0, 0, 0, 0.88);
  font-size: 14px;
  font-weight: 500;
}
.baseContent {
  height: 100%;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  background-color: #fafafa;
}
.disableText {
  color: rgba(0, 0, 0, 0.45);
}
.publicKeyPopover :global(.ant-popover-inner) {
  overflow: auto;
  max-width: 512px;
}
.publicKeyPopover :global(.ant-popover-inner-content) {
  overflow: auto;
  max-height: 400px;
  word-break: break-all;
}
.publicKeyPopover .publicTitle {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.publicKeyPopover .publicTitle span {
  color: rgba(0, 0, 0, 0.88);
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
}
.publicKeyPopover .publicTitle :global(.ant-typography) {
  margin-bottom: 0;
}
.publicKeyPopover .publicTitle :global(.ant-typography-copy) {
  color: #9a0000;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
}
.publicKeyPopover .publicKey {
  color: rgba(0, 0, 0, 0.88);
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  text-align: justify;
}
.tokenPopover :global(.ant-popover-inner) {
  width: 287px;
  height: 106px;
}
.tokenPopover :global(.ant-popover-title) {
  color: rgba(0, 0, 0, 0.88);
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
}
.tokenPopover .contentName {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}
.tokenPopover .contentName .name {
  overflow: hidden;
  width: 200px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 16px;
  font-weight: 500;
  line-height: 22px;
  text-align: justify;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.tokenPopover .tokenContent {
  display: flex;
  align-items: center;
  gap: 16px;
}
.tokenPopover .tokenContent :global(.ant-typography) {
  margin-bottom: 0;
}
.spanClick {
  color: #9a0000;
  cursor: pointer;
}
.nodeDrawer :global .ant-drawer-header {
  height: 57px;
}
.nodeDrawer :global .ant-descriptions-row:last-child .ant-descriptions-item {
  padding-bottom: 0 !important;
}
.nodeDrawer .title :global .ant-typography-ellipsis {
  font-size: 16px;
}
