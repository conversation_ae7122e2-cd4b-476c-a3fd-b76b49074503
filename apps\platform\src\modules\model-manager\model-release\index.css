.toggleBtnDisabled {
  color: #d9d9d9;
  opacity: 0.4;
}
.configToggle {
  color: #9a0000;
  cursor: pointer;
}
.nodeSettingContent {
  min-height: 92px;
  box-sizing: border-box;
  padding: 16px;
  border-radius: 2px;
  background: rgba(0, 0, 0, 0.02);
}
.nodeSettingContent :global(.ant-input-number-group-addon) {
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  line-height: 22px;
}
.configToggleDisabled {
  color: rgba(0, 0, 0, 0.25);
  cursor: not-allowed;
  font-size: 14px;
  line-height: 22px;
  pointer-events: none;
}
.nodeOptionsTitle {
  margin-top: 16px;
  margin-bottom: 8px;
  color: rgba(0, 0, 0, 0.88);
  font-size: 14px;
  line-height: 22px;
}
.featuresTitle {
  margin-bottom: 8px;
  color: rgba(0, 0, 0, 0.88);
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
}
.featureContent {
  margin-bottom: 14px;
}
.emptyFeature {
  display: flex;
  height: 158px;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.02);
}
.tableHeader {
  display: flex;
  width: 100%;
  height: 36px;
  box-sizing: border-box;
  padding: 8px 0;
  background: rgba(0, 0, 0, 0.02);
  box-shadow: inset 0 -1px 0 0 #e8e9ea;
  color: rgba(0, 0, 0, 0.85);
  font-size: 12px;
  font-weight: 500;
  line-height: 20px;
}
.tableHeader .tableHeaderNode {
  width: 178px;
  border-right: 1px solid rgba(0, 10, 26, 0.07);
  margin-left: 32px;
}
.tableHeader .tableHeaderService {
  width: 247px;
  box-sizing: border-box;
  padding-left: 12px;
  border-right: 1px solid rgba(0, 10, 26, 0.07);
}
.tableHeader .tableHeaderStatus {
  width: 90px;
  box-sizing: border-box;
  padding-left: 12px;
  border-right: 1px solid rgba(0, 10, 26, 0.07);
}
.tableHeader .tableHeaderOptions {
  padding-left: 12px;
}
.featuresList {
  width: 100%;
  padding: 2px 0;
  box-shadow: inset 0 -1px 0 0 #e8e9ea;
}
.featuresList .featuresListContent {
  display: flex;
  gap: 8px;
}
.featuresList .featuresListContent :global(.ant-form-item) {
  margin-bottom: 0;
}
.formToggle {
  width: 16px;
  margin-left: 8px;
}
.formNode {
  width: 168px !important;
  margin-right: 16px;
}
.formService {
  width: 222px !important;
  margin-right: 16px;
}
.tableHeaderError {
  margin-top: -6px;
  margin-left: 16px;
}
.intoHeader {
  margin-left: 24px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 12px;
  font-weight: 500;
  line-height: 20px;
}
.onlineHeader {
  display: flex;
  align-items: center;
}
.onlineHeader .features {
  margin-right: 12px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 12px;
  font-weight: 500;
  line-height: 20px;
}
.onlineHeader .checkBoxLabel {
  color: rgba(0, 0, 0, 0.85);
  font-size: 12px;
  font-weight: 500;
  line-height: 22px;
}
.featureOnline {
  width: 222px !important;
}
.featureOnlineError :global(.ant-select-selector) {
  border: 1px solid red !important;
}
.mockStyle {
  display: flex;
  align-items: center;
  gap: 8px;
}
.mockStyle :global(.ant-tag) {
  scale: 0.9;
}
