<?xml version="1.0" encoding="UTF-8"?>
<svg width="8.25px" height="10.5px" viewBox="0 0 8.25 10.5" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>1 通用/2 图标占位 Placeholder/16px</title>
    <defs>
        <path d="M8.13984375,2.63203125 L5.61796875,0.11015625 C5.54765625,0.03984375 5.45273437,0 5.353125,0 L0.375,0 C0.167578125,0 0,0.167578125 0,0.375 L0,10.125 C0,10.3324219 0.167578125,10.5 0.375,10.5 L7.875,10.5 C8.08242187,10.5 8.25,10.3324219 8.25,10.125 L8.25,2.89804687 C8.25,2.7984375 8.21015625,2.70234375 8.13984375,2.63203125 Z M7.38515625,3.0703125 L5.1796875,3.0703125 L5.1796875,0.86484375 L7.38515625,3.0703125 Z M7.40625,9.65625 L0.84375,9.65625 L0.84375,0.84375 L4.3828125,0.84375 L4.3828125,3.375 C4.3828125,3.646875 4.603125,3.8671875 4.875,3.8671875 L7.40625,3.8671875 L7.40625,9.65625 Z M6.28125,6.4921875 L1.96875,6.4921875 C1.865625,6.4921875 1.78125,6.534375 1.78125,6.5859375 L1.78125,7.1484375 C1.78125,7.2 1.865625,7.2421875 1.96875,7.2421875 L6.28125,7.2421875 C6.384375,7.2421875 6.46875,7.2 6.46875,7.1484375 L6.46875,6.5859375 C6.46875,6.534375 6.384375,6.4921875 6.28125,6.4921875 Z M1.78125,4.9921875 L1.78125,5.5546875 C1.78125,5.60625 1.8234375,5.6484375 1.875,5.6484375 L6.375,5.6484375 C6.4265625,5.6484375 6.46875,5.60625 6.46875,5.5546875 L6.46875,4.9921875 C6.46875,4.940625 6.4265625,4.8984375 6.375,4.8984375 L1.875,4.8984375 C1.8234375,4.8984375 1.78125,4.940625 1.78125,4.9921875 Z" id="path-1"></path>
    </defs>
    <g id="——-设计方案0515（定稿）" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="工作台-已有项目" transform="translate(-1188.875000, -278.750000)">
            <g id="编组-16" transform="translate(1018.000000, 126.000000)">
                <g id="编组-15备份-4" transform="translate(20.000000, 58.000000)">
                    <g id="编组-11" transform="translate(149.000000, 89.000000)">
                        <g id="Icon/01-Line/1-example" transform="translate(1.875000, 5.750000)">
                            <mask id="mask-2" fill="white">
                                <use xlink:href="#path-1"></use>
                            </mask>
                            <use id="Shape" fill-opacity="0.45" fill="#000000" xlink:href="#path-1"></use>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>