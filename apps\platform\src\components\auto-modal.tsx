import type { ModalProps } from 'antd';
import { Modal } from 'antd';
import { useEffect } from 'react';
import { useState } from 'react';

export const AutoModal = (props: ModalProps & { top?: number; minHeight?: number }) => {
  const { children, top = 50, minHeight = 500, ...rest } = props;
  const [maxHeight, setMaxHeight] = useState(600);
  useEffect(() => {
    const height = document.documentElement.clientHeight;
    setMaxHeight(height - top - 150);
  }, []);

  return (
    <Modal {...rest} style={{ top }}>
      <div style={{ maxHeight, minHeight, overflowY: 'auto', overflowX: 'hidden' }}>
        {children}
      </div>
    </Modal>
  );
};
