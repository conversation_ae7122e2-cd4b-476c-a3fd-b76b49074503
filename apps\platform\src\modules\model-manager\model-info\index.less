.featureTitle {
  margin-bottom: 8px;
  color: rgb(0 0 0 / 88%);
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
}

.tableFeatures {
  :global(.ant-table) {
    margin-inline: -8px -8px !important;
  }
}

.intoHeader {
  margin-left: 48px;
}

.tableContent {
  :global(.ant-table-content) {
    color: rgb(0 0 0 / 85%);
    font-size: 12px !important;
    font-weight: 500;
    line-height: 20px;
  }

  margin-bottom: 16px;
}

.mockStyle {
  display: flex;
  align-items: center;

  :global(.ant-tag) {
    scale: 0.8;
  }
}

.modelDesc {
  padding: 12px;
  margin-bottom: 24px;
  background: rgb(0 0 0 / 2%);

  :global(.ant-descriptions-item-label) {
    color: rgb(0 0 0 / 45%);
    font-size: 12px;
    line-height: 22px;
  }

  :global(.ant-descriptions .ant-descriptions-row > td) {
    padding-bottom: 8px !important;
  }
}

.configToggle {
  color: #9a0000;
  cursor: pointer;
}

.config {
  margin-top: 12px;

  :global {
    .ant-descriptions-item-label {
      font-size: 12px !important;
      line-height: 22px;
    }

    .ant-descriptions-item-content {
      font-size: 12px !important;
      line-height: 22px;
    }
  }

  .configLabel {
    padding-bottom: 8px;
    color: rgb(0 0 0 / 88%);
    font-size: 14px;
    line-height: 22px;
  }

  .configContent {
    height: 54px;
    box-sizing: border-box;
    padding: 16px;
    background: rgb(0 0 0 / 2%);
  }
}
