import { ShowMenuContext, Portal } from '@secretflow/dag';
import classnames from 'classnames';
import type { ReactNode } from 'react';
import { Outlet } from 'umi';

import { useModel } from '@/util/valtio-helper';

import { EdgeLayoutService } from './edge-layout.service';
import { HeaderComponent } from './header-view';
import styles from './index.less';
import { LeftView } from './left-view';
import type { MenuViewProps } from './left-view';

export const EdgeLayout = ({
  menuItems,
}: {
  menuItems: MenuViewProps['menuItems'];
}) => {
  const layoutService = useModel(EdgeLayoutService);
  const X6ReactPortalProvider = Portal.getProvider();

  return (
    <div className={classnames(styles.home)}>
      <ShowMenuContext.Provider value={false}>
        <X6ReactPortalProvider />
      </ShowMenuContext.Provider>

      <LeftView menuItems={menuItems} />

      <div className={styles.rightSide}>
        <div className={styles.header}>
          <HeaderComponent />
        </div>
        <div className={styles.content}>
          <Outlet />
        </div>
      </div>
    </div>
  );
};
