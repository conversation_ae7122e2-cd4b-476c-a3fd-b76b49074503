@import url('../../../variables.less');

.home {
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 100%;
  background-size: cover;
}

.homeBg {
  background-image: url('../../../assets/bg2.png');
}

.centerBg {
  background-image: url('../../../assets/bg2.png');
}

.header-link {
  text-decoration: none;
}

.leftSide {
  width: 220px;
  background-color: @PrimaryColor;
}

.header-items {
  display: flex;
  width: 100%;
  height: 100%;
  align-items: center;

  .left {
    display: flex;
    flex: 1;
    align-items: center;
    padding-left: 16px;
    font-size: 14px;

    .currentRoute {
      color: #000;
      font-weight: 500;
    }

    .route {
      color: rgb(0 0 0 / 40%);
    }

    .backText {
      color: @PrimaryColor;
      line-height: 21px;
    }

    .subTitle {
      margin-left: 16px;
      color: #ddbbb8;
      font-size: 20px;
      font-weight: 500;
    }

    .line {
      width: 1px;
      height: 12px;
      margin: 14px;
      margin: 0 24px;
      background-color: rgb(0 10 26 / 16%);
    }

    .myNodeTitle {
      display: flex;
      width: 188px;
      height: 32px;
      align-items: center;
      border-radius: 4px;
      background-color: #741109 !important;
      color: #a96c67;
      cursor: pointer;
      font-size: 14px;
      font-weight: 400;

      svg {
        padding-right: 4px;
        padding-left: 14px;
        border: none !important;
      }

      .nodeName {
        display: inline-block;
        overflow: hidden;
        max-width: 115px;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-right: 16px;

    .myNodeTitle {
      margin-right: 32px;
      font-size: 12px;
    }

    .avatar {
      width: 20px;
      height: 20px;
      font-size: 20px;
    }

    .community {
      display: flex;
      align-items: center;
      padding: 0 12px;
      cursor: pointer;
      font-size: 14px;

      svg {
        margin-right: 4px;
        font-size: 16px;
      }
    }

    .line {
      width: 1px;
      height: 12px;
      background-color: rgb(0 10 26 / 16%);
    }

    .loginline {
      width: 1px;
      height: 12px;
      margin: 14px;
      margin-right: 24px;
      margin-left: 12px;
      background-color: rgb(0 10 26 / 16%);
    }

    .contentHeaderRight {
      padding: 0 12px;
    }

    .help {
      display: flex;
      align-items: center;
      padding: 0 12px;
      cursor: pointer;
      font-size: 14px;

      svg {
        margin-right: 4px;
        font-size: 16px;
      }
    }
  }
}

.header {
  height: 44px;
  background: #fff;
}

.rightSide {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  flex: 1;
  background-color: #f5f5f5;
  overflow-y: auto;
}

.messageBadge {
  color: #fefcfc !important;

  :global(.ant-badge) {
    box-shadow: none !important;
  }
}

.headerDropdown {
  min-width: 300px;
  min-height: 100px;
}

.menuContainer {
  display: flex;
  height: 100%;
  flex-direction: column;
  justify-content: space-between;
  background: @PrimaryColor;
  transition: width 0.3s cubic-bezier(0.2, 0, 0, 1) 0s;

  .logoLarge {
    margin: 15px 0 20px 20px;
  }

  .logoSmall {
    margin: 15px 0 20px 10px;
  }

  :global {
    .ant-menu {
      width: 100%;
      background-color: transparent;
      border-inline-end: none;
      font-size: 14px;
    }

    .ant-menu-submenu-title {
      color: #fff;

      &:active {
        background-color: unset !important;
      }
    }

    .ant-menu-submenu-selected > .ant-menu-submenu-title {
      color: #fff;
    }

    .ant-menu-item:not(.ant-menu-item-selected):hover {
      background-color: rgb(0 0 0 / 8%) !important;
      color: #fff !important;
    }

    .ant-menu-item {
      color: #fff;

      &.ant-menu-item-selected {
        color: @PrimaryColor;
        font-weight: 700;
      }

      &.ant-menu-item-active:not(.ant-menu-item-selected) {
        color: #fff;
      }
    }

    .ant-menu-item-selected {
      background-color: #fff;
      color: @PrimaryColor;
      font-weight: 700;
    }

    .ant-menu-item-active {
      color: #fff;
    }
  }
}

.unfold {
  width: 208px;
}

.fold {
  width: 60px;
}

.collapseInfo {
  display: flex;
  justify-content: space-around;
  color: #fff;
  font-size: 12px;
}

.content {
  height: calc(100% - 44px);
  overflow-y: auto;
}

.labelWithBadge {
  display: flex;
  align-items: center;
  justify-content: space-between;

  :global(.ant-badge .ant-badge-count) {
    box-shadow: none;
  }
}
