import type { TransferProps } from 'antd';
import { Transfer } from 'antd';
import { useEffect } from 'react';

import { Model, useModel } from '@/util/valtio-helper';

import type { AuthComponentProps } from '../data-table-auth-drawer';

import style from './index.less';

import type { VisibleAuthVO } from '.';

interface IProps {
  authNodes: VisibleAuthVO[];
  nodeVoters: AuthComponentProps['nodeVoters'];
  onChange: (addNodes: string[], removeNodes: string[]) => void;
}

// 比较两个数组中哪些元素属于新增，哪些属于删除
function getDiff(
  newKeys: string[],
  oldKeys: string[],
): { add: string[]; remove: string[] } {
  const add: string[] = [];
  const remove: string[] = [];
  newKeys.forEach((item) => {
    if (!oldKeys.includes(item)) {
      add.push(item);
    }
  });
  oldKeys.forEach((item) => {
    if (!newKeys.includes(item)) {
      remove.push(item);
    }
  });
  return { add, remove };
}

export const AddVisibleAuthComponent = (props: IProps) => {
  const model = useModel(AddVisibleAuthModel);
  const { authNodes, nodeVoters, onChange } = props;

  const handleChange: TransferProps['onChange'] = (targetKeys, direction, moveKeys) => {
    console.log('targetKeys:', targetKeys);
    console.log('moveKeys:', moveKeys);
    console.log('direction:', direction);

    const { add, remove } = getDiff(
      targetKeys as string[],
      model.originTargetKeys as string[],
    );

    model.targetKeys = [...targetKeys];
    onChange(add, remove);
  };

  useEffect(() => {
    model.initList(authNodes, nodeVoters);
  }, [authNodes]);

  return (
    <div className={style.addVisibleAuthWrapper}>
      <Transfer
        dataSource={model.nodesList}
        titles={['可授权节点', '已授权节点']}
        oneWay
        listStyle={{
          width: 250,
          height: 300,
        }}
        onChange={handleChange}
        targetKeys={model.targetKeys}
        render={(item) => item.title}
      ></Transfer>
    </div>
  );
};

AddVisibleAuthComponent.displayName = 'AddVisibleAuthComponent';

export class AddVisibleAuthModel extends Model {
  nodesList: { key: string; title: string }[] = [];

  targetKeys: React.Key[] = [];
  originTargetKeys: React.Key[] = [];

  initList(authNodes: VisibleAuthVO[], nodeVoters: AuthComponentProps['nodeVoters']) {
    this.nodesList = nodeVoters.map((item) => {
      return {
        key: item.nodeId,
        title: item.nodeName,
      };
    });
    this.targetKeys = authNodes.map((node) => node.nodeId!);
    this.originTargetKeys = [...this.targetKeys];
  }
}
