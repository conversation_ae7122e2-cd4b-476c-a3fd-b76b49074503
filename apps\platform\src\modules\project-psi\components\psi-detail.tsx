import { Descriptions, Drawer, Modal, Spin, Timeline, Typography } from 'antd';
import type { DescriptionsProps, TimelineProps } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';

import LogEditor from '@/modules/log-list/components/log-editor';
import { formatLog } from '@/modules/log-list/components/status-list';
import { TaskStatusLabel } from '@/modules/project-prediction';
import { useModel } from '@/util/valtio-helper';

import { PSIModel } from '..';
import styles from '../index.less';
import type { AllocatedNode } from '../project-psi.service';

interface PSIDetailProps {
  open: boolean;
  data: API2.PSIJobInfo | null;
  ownerId: string;
  onClose: () => void;
}

const fieldMap = {
  'input/input_ds1/keys': '本地数据',
  'input/input_ds2/keys': '外部数据',
  protocol: '求交协议',
  'protocol/PROTOCOL_ECDH': 'PROTOCOL_ECDH曲线',
  sort_result: '是否重排序',
  receiver_parties: '结果接收方',
  allow_empty_result: '求交结果是否允许为空',
  join_type: '求交类型',
  'join_type/left_join/left_side': '左表数据方',
  input_ds1_keys_duplicated: '表一关联键重复',
  input_ds2_keys_duplicated: '表二关联键重复',
};

function getFieldData(data: Record<string, any>) {
  if (data.b !== undefined) {
    return data.b ? '是' : '否';
  } else if (data.s !== undefined) {
    return data.s;
  } else if (data.ss !== undefined) {
    return data.ss.join(',');
  }
}

export const LogList = ({
  logsList,
  type,
  jobId,
  nodeId,
}: {
  logsList: API2.PSILogsResponse['data'];
  type: 'psi' | 'fi';
  jobId: string;
  nodeId: string;
}) => {
  const [open, setOpen] = useState(false);
  const [logMessage, setLogMessage] = useState('');
  const model = useModel(PSIModel);

  const logs: TimelineProps['items'] =
    logsList?.map((item) => {
      if (item.logLevel === 'INFO') {
        return {
          color: '#4096ff',
          children: (
            <div className={styles.psiLogItem}>
              <span>{item.logMessage}</span>
              <span style={{ color: 'rgba(0, 0, 0, 0.40)' }}>{item.logTime}</span>
            </div>
          ),
        };
      } else {
        return {
          color: 'red',
          children: (
            <>
              <div className={styles.psiLogItem}>
                <span>{type.toLocaleUpperCase()}任务执行失败</span>
                <Typography.Link
                  onClick={() => {
                    setOpen(true);
                    model
                      .getLogDetails({
                        jobId,
                        nodeId,
                      })
                      .then((res) => {
                        const errMsg = res?.partyLogs.find(
                          (log) => log.domainId === nodeId,
                        )?.errMsg;
                        setLogMessage(formatLog(errMsg || res?.taskErrMsg || ''));
                        model.logDetailLoading = false;
                      });
                  }}
                >
                  查看错误日志
                </Typography.Link>
              </div>
            </>
          ),
        };
      }
    }) || [];

  return (
    <>
      <Timeline style={{ flex: 1 }} items={logs} />
      <Modal
        title="错误日志"
        mask={false}
        open={open}
        onCancel={() => setOpen(false)}
        footer={null}
        width={800}
      >
        <Spin spinning={model.logDetailLoading}>
          <div style={{ height: 600, overflow: 'auto' }}>
            <LogEditor text={logMessage} />
          </div>
        </Spin>
      </Modal>
    </>
  );
};

export const PSIDetails = ({
  descData,
  logsList,
  parties,
  currentNodeId,
}: {
  descData: API2.PSIJobInfo;
  logsList: API2.PSILogsResponse['data'];
  parties: AllocatedNode[];
  currentNodeId: string;
}) => {
  const { attrs, attrPaths } = JSON.parse(descData.nodeDef || '{}');

  const paramsInfos = attrPaths?.map((item: any, index: number) => {
    let info: any;
    const primaryInst = parties.find(
      (party) => party.instId === descData.initiator,
    )?.instName;
    const partnerInst = parties.find(
      (party) => party.instId !== descData.initiator,
    )?.instName;
    if (item === 'input/input_ds1/keys') {
      info = `机构：${primaryInst}\n表名：${
        descData.initiatorDatatableName
      }\nID列：${getFieldData(attrs[index])}`;
    } else if (item === 'input/input_ds2/keys') {
      info = `机构：${partnerInst}\n表名：${
        descData.partnerDatatableName
      }\nID列：${getFieldData(attrs[index])}`;
    } else {
      info = getFieldData(attrs[index]);
    }
    return {
      key: item as string,
      label: fieldMap[item as keyof typeof fieldMap],
      children: <div style={{ whiteSpace: 'pre-wrap' }}>{info}</div>,
    };
  });

  const descriptionInfos: DescriptionsProps['items'] = [
    {
      key: 'jobName',
      label: '任务名称',
      children: descData.jobName,
    },
    {
      key: 'description',
      label: '任务描述',
      children: descData.description || '-',
    },
    {
      key: 'initiatorName',
      label: '发起机构',
      children: descData.initiatorName,
    },
    {
      key: 'status',
      label: '状态',
      children: <TaskStatusLabel status={descData.status} />,
    },
    {
      key: 'createTime',
      label: '提交时间',
      children: dayjs(descData.createTime).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      key: 'startTime',
      label: '任务开始时间',
      children: descData.startTime
        ? dayjs(descData.startTime).format('YYYY-MM-DD HH:mm:ss')
        : '-',
    },
    {
      key: 'finishedTime',
      label: '任务完成时间',
      children: descData.finishedTime
        ? dayjs(descData.finishedTime).format('YYYY-MM-DD HH:mm:ss')
        : '-',
    },
  ];

  return (
    <>
      <Descriptions
        styles={{ label: { width: 100 } }}
        column={1}
        items={descriptionInfos}
      />
      <Descriptions
        style={{ marginTop: 16 }}
        styles={{ label: { width: 100 } }}
        column={1}
        layout="vertical"
      >
        <Descriptions.Item label="任务参数">
          <Descriptions
            className={styles.psiDetail}
            styles={{ label: { width: 140 } }}
            size="small"
            column={1}
            items={paramsInfos}
          />
        </Descriptions.Item>
        <Descriptions.Item label="执行流程">
          <LogList
            type="psi"
            logsList={logsList}
            jobId={descData.jobId}
            nodeId={currentNodeId}
          />
        </Descriptions.Item>
      </Descriptions>
    </>
  );
};

export const PSIDetailDrawer = (props: PSIDetailProps) => {
  const { open, data, onClose, ownerId } = props;
  const model = useModel(PSIModel);

  const [logsList, setLogsList] = useState<API2.PSILogsResponse['data']>([]);

  useEffect(() => {
    if (open && data) {
      model
        .getLogs({
          jobId: data.jobId,
          nodeId: model.parties.find((party) => party.instId === ownerId)?.nodeId || '',
        })
        .then((res) => {
          setLogsList(res);
        });
    }
  }, [open, data]);

  return (
    <Drawer title="任务详情" open={open} width={600} onClose={onClose}>
      {data && (
        <PSIDetails
          descData={data}
          parties={model.parties}
          logsList={logsList}
          currentNodeId={
            model.parties.find((party) => party.instId === ownerId)?.nodeId || ''
          }
        ></PSIDetails>
      )}
    </Drawer>
  );
};
