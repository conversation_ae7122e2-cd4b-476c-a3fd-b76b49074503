import { LoadingOutlined } from '@ant-design/icons';
import { Descriptions, Drawer, Space, Steps } from 'antd';
import type { DescriptionsProps, StepsProps } from 'antd';
import { useEffect, useState } from 'react';

import { LogList } from '@/modules/project-psi/components/psi-detail';
import { useModel } from '@/util/valtio-helper';

import { PredictionModel } from '..';
import style from '../index.less';

import { FeatureTable } from './feature-table';

interface PredictionDetailProps {
  open: boolean;
  data: API2.FIJobListVO & { currentNodeId: string };
  onClose: () => void;
}

const getCurrentStep = (status: string) => {
  if (['REJECTED', 'CANCELED'].includes(status)) {
    return {
      current: 0,
      status: 'error',
    };
  }
  if (status === 'PENDING_APPROVAL') {
    return {
      current: 0,
      status: 'loading',
    };
  }
  if (status === 'RUNNING') {
    return {
      current: 1,
      status: 'loading',
    };
  }
  if (status === 'FI_PSI_COMPLETED') {
    return {
      current: 2,
      status: 'loading',
    };
  }
  if (status === 'SUCCEED') {
    return {
      current: 3,
      status: 'finish',
    };
  }
  if (status === 'FAILED') {
    return {
      current: 2,
      status: 'error',
    };
  }
  return {
    current: 0,
    status: 'loading',
  };
};

export const PredictionDetails = ({
  descData,
}: {
  descData: API2.FIJobListVO & { currentNodeId: string };
}) => {
  const model = useModel(PredictionModel);
  const [logsList, setLogsList] = useState<API2.PSILogsResponse['data']>([]);
  const descriptionInfos: DescriptionsProps['items'] = [
    {
      key: 'jobName',
      label: '任务名称',
      children: descData.jobName,
    },
    {
      key: 'description',
      label: '描述',
      children: descData.description,
    },
    {
      key: 'startTime',
      label: '发起时间',
      children: descData.startTime,
    },
    {
      key: 'finishedTime',
      label: '完成时间',
      children: descData.finishedTime,
    },
    {
      key: 'projectName',
      label: '模型项目来源',
      children: descData.sourceProjectName,
    },
    {
      key: 'modelName',
      label: '模型名称',
      children: descData.modelName,
    },
    {
      key: 'receiverParties',
      label: '结果接收方',
      children: descData.receiverParties.join(','),
    },
    {
      key: 'resultColumns',
      label: '预测结果列名',
      children: descData.resultColumns,
    },
  ];

  const processStepItems: StepsProps['items'] = [
    { title: '数据加载' },
    { title: '隐私求交' },
    { title: '模型预测' },
    { title: '结果产出' },
  ];

  const partyConfigs = JSON.parse(descData.partyConfig);

  const initiatorData = {
    nodeId: descData.initiator,
    datatableId: descData.initiatorDatatableName,
    indexId: JSON.parse(descData.initiatorKeyColumns).join(','),
    features: partyConfigs.find((item: any) => item.nodeId === descData.initiator)
      ?.features,
  };

  const partnerData = {
    nodeId: descData.partner,
    datatableId: descData.partnerDatatableName,
    indexId: JSON.parse(descData.partnerKeyColumns).join(','),
    features: partyConfigs.find((item: any) => item.nodeId === descData.partner)
      ?.features,
  };

  const { current: stepCurrent, status: stepStatus } = getCurrentStep(descData.status);

  if (stepStatus === 'loading') {
    processStepItems[stepCurrent].icon = <LoadingOutlined />;
  } else {
    processStepItems[stepCurrent].status = stepStatus as
      | 'error'
      | 'wait'
      | 'process'
      | 'finish'
      | undefined;
  }

  useEffect(() => {
    model
      .getJobLogs({
        jobId: descData.jobId,
        nodeId: descData.currentNodeId,
      })
      .then((res) => {
        if (res) {
          setLogsList(res);
        }
      });
  }, [descData]);

  return (
    <>
      <Descriptions column={1} items={descriptionInfos} />
      <Descriptions style={{ marginTop: 16 }} layout="vertical" column={1}>
        <Descriptions.Item label="发起方数据" className={style.datasetDetail}>
          <FeatureTable editable={false} value={initiatorData}></FeatureTable>
        </Descriptions.Item>
        <Descriptions.Item label="合作方数据" className={style.datasetDetail}>
          <FeatureTable editable={false} value={partnerData}></FeatureTable>
        </Descriptions.Item>
        {/* 执行进度和日志进能在列表中的详情查看，不能在审批的详情查看 */}
        <Descriptions.Item label="执行进度" className={style.datasetDetail}>
          <Steps
            className={style.fiProcessSteps}
            size="small"
            current={stepCurrent}
            items={processStepItems}
          ></Steps>
        </Descriptions.Item>
        <Descriptions.Item label="执行日志" className={style.datasetDetail}>
          <LogList
            type="fi"
            logsList={logsList}
            jobId={descData.jobId}
            nodeId={descData.currentNodeId}
          />
        </Descriptions.Item>
      </Descriptions>
    </>
  );
};

export const PredictionDetailDrawer = (props: PredictionDetailProps) => {
  const { open, data, onClose } = props;

  return (
    <Drawer title="任务详情" open={open} width={780} onClose={onClose}>
      <PredictionDetails descData={data}></PredictionDetails>
    </Drawer>
  );
};
