.drawerTitleText {
  overflow: hidden;
  max-width: 200px;
  margin-right: 4px;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.fields {
  label {
    width: 100%;
  }
}

.fieldsTip {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;

  .tips {
    color: rgb(0 0 0 / 88%);
    font-size: 14px;
    font-weight: 500;
    line-height: 22px;
  }

  .linkTips {
    margin-right: -4px;
  }
}

.selectProject {
  display: flex;
  align-items: baseline;
  gap: 10px;
}

.emptyProject {
  height: 168px;
  padding-top: 50px;
  border-radius: 6px;
  margin-left: 0;
  background-color: #f5f5f5;
  margin-block: 0 !important;
}
