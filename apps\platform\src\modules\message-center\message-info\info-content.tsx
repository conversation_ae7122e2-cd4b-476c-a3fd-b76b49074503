import { Descriptions, Tag, Tooltip, Typography } from 'antd';
import { parse } from 'query-string';
import { useMemo, useState } from 'react';
import { useLocation } from 'umi';

import { StatusTag } from '@/components/status-tag';
import { EllipsisText } from '@/components/text-ellipsis.tsx';
import { VoteInstNodesGraph } from '@/components/vote-insts-graph';
import { formatTimestamp } from '@/modules/dag-result/utils';
import { DataTableStructure } from '@/modules/data-table-info/component/data-table-structure';
import { convertToNodeData } from '@/modules/p2p-project-detail/helper';
import { PreviewGraphComponents } from '@/modules/result-details/graph';
import { FullscreenGraphModalComponent } from '@/modules/result-details/graph-fullscreen-modal';
import { useModel } from '@/util/valtio-helper';

import type { MessageItemType } from '../component/common';
import { ListItemTitleMap, MessageTypeTag, NodeStatusList } from '../component/common';
import { MessageService, StatusEnum } from '../message.service';

import styles from './index.less';
import { NodesStatus } from './nodes-status';
import { TaskDetailModal } from './task-detail-modal';

interface IProps {
  info: Record<string, any>;
  activeTab: string;
  status: string;
}

export enum ComputedModelEnum {
  TEE = 'TEE',
  MPC = 'MPC',
}
export const ComputedModelObj = {
  [ComputedModelEnum.TEE]: '枢纽模式',
  [ComputedModelEnum.MPC]: '管道模式',
};

export enum ComputeFuncEnum {
  DAG = 'DAG',
  PSI = 'PSI',
  ALL = 'ALL',
}

export const ComputeFuncObj = {
  [ComputeFuncEnum.DAG]: '模型训练-联合建模',
  [ComputeFuncEnum.PSI]: '隐私求交',
  [ComputeFuncEnum.ALL]: '全家桶',
};

export const TeeDownloadInfo = (props: IProps) => {
  const { info } = props;
  return (
    <div>
      <Descriptions column={1}>
        <Descriptions.Item label="类型">
          <MessageTypeTag type={info.type} />
        </Descriptions.Item>
      </Descriptions>
      <Descriptions column={2}>
        <Descriptions.Item label="结果表名称">
          <EllipsisText width={110}>{info.messageName}</EllipsisText>
          <Tag className={styles.sheetTag}>表</Tag>
        </Descriptions.Item>
        <Descriptions.Item label="来源项目">
          <EllipsisText width={110}>{info?.project?.projectName}</EllipsisText>
        </Descriptions.Item>
        <Descriptions.Item label="所属训练流">
          <EllipsisText width={110}>{info.graphName}</EllipsisText>
        </Descriptions.Item>
        <Descriptions.Item label="生成时间">
          <EllipsisText width={160}>
            {info?.project?.gmtCreated
              ? formatTimestamp(info?.project?.gmtCreated)
              : ''}
          </EllipsisText>
        </Descriptions.Item>
        <Descriptions.Item label="计算模式">
          <EllipsisText>
            {
              ComputedModelObj[
                (info?.project?.computeMode as ComputedModelEnum) ||
                  ComputedModelEnum.MPC
              ]
            }
          </EllipsisText>
        </Descriptions.Item>
      </Descriptions>
      <Descriptions column={1}>
        <Descriptions.Item label="参与节点" className={styles.descNodeStatusList}>
          <NodeStatusList list={info?.partyVoteStatuses || []} />
        </Descriptions.Item>
        <Descriptions.Item label="任务ID">
          <Typography.Paragraph copyable>{info.taskID}</Typography.Paragraph>
        </Descriptions.Item>
      </Descriptions>
      <div className={styles.dagBoxContent}>
        <PreviewGraphComponents
          graph={info.graphDetailVO as API.GraphDetailVO}
          // id={'qavi-smutlwmh-node-10-output-0'}
          id={info.messageName}
          projectMode={info?.project?.computeMode || ComputedModelEnum.MPC}
        />
      </div>
      <div className={styles.sheetText}>表字段</div>
      <DataTableStructure schema={info?.tableColumns || []} />
      <FullscreenGraphModalComponent />
    </div>
  );
};

// 授权邀约
export const NodeAuthInfo = (props: IProps) => {
  const { info } = props;
  return (
    <div>
      <Descriptions column={1}>
        {info.status === StatusEnum.REJECT && (
          <Descriptions.Item label="拒绝原因">
            <EllipsisText>{info.reason}</EllipsisText>
          </Descriptions.Item>
        )}
        <Descriptions.Item label="类型">
          <MessageTypeTag type={info.type} />
        </Descriptions.Item>
        <Descriptions.Item label="发起端节点">
          <EllipsisText>{info.initiatorNodeName}</EllipsisText>
        </Descriptions.Item>
        <Descriptions.Item label="计算节点名">
          <EllipsisText>{info.nodeName}</EllipsisText>
        </Descriptions.Item>
        <Descriptions.Item label="计算节点ID">
          <EllipsisText>{info.nodeID}</EllipsisText>
        </Descriptions.Item>
        <Descriptions.Item label="节点通讯地址">
          <EllipsisText>{info.url}</EllipsisText>
        </Descriptions.Item>
        <Descriptions.Item label="授权方式">
          <EllipsisText>{info.isSingle ? '单向通信授权' : '双向通信授权'}</EllipsisText>
        </Descriptions.Item>
      </Descriptions>
    </div>
  );
};

// 项目邀约&项目归档
export const ProjectInviteInfo = (props: IProps) => {
  const { info, activeTab, status } = props;
  const { statusType, statusText } = getStatus(status);

  const { nodeData, edgeData, groupNodeIds } = convertToNodeData(info);

  const [isToggle, setIsToggle] = useState(true);
  const [graphHeight, setGraphHeight] = useState(240);

  const memoGraph = useMemo(() => {
    return (
      <VoteInstNodesGraph
        nodes={nodeData}
        edges={edgeData}
        groupNodeIds={groupNodeIds}
        setGraphHeight={setGraphHeight}
      />
    );
  }, [info]);

  return (
    <div>
      <Descriptions column={1}>
        <Descriptions.Item
          className={styles.messageStatus}
          label={activeTab === 'process' ? '本方状态' : '当前状态'}
        >
          <StatusTag type={statusType} text={statusText} />
        </Descriptions.Item>
        <Descriptions.Item label="类型">
          <EllipsisText>
            {ListItemTitleMap[info?.type as MessageItemType].tagText}
          </EllipsisText>
        </Descriptions.Item>
        <Descriptions.Item label="项目名称">
          <EllipsisText>{info?.projectName}</EllipsisText>
        </Descriptions.Item>
        <Descriptions.Item label="发起机构">
          <EllipsisText>{info?.initiatorName}</EllipsisText>
        </Descriptions.Item>
        <Descriptions.Item label="发起端节点">
          <EllipsisText>
            {info.participantNodeInstVOS
              .map((i: any) => i.initiatorNodeName)
              .join('、')}
          </EllipsisText>
        </Descriptions.Item>
        <Descriptions.Item label="创建时间">
          <EllipsisText>
            {info?.gmtCreated ? formatTimestamp(info?.gmtCreated) : ''}
          </EllipsisText>
        </Descriptions.Item>
        <Descriptions.Item label="受邀节点" className={styles.descNodeStatusList}>
          {/* <NodesStatus info={info} /> */}
          {/* <Space
            onClick={() => {
              setIsToggle(!isToggle);
            }}
            className={styles.configToggle}
          >
            <DownOutlined
              style={{
                transform: isToggle ? `rotate(180deg)` : `rotate(0)`,
              }}
            />
            {isToggle ? '收起' : '展开'}
          </Space> */}
          <div style={{ height: graphHeight, width: 500 }}>{memoGraph}</div>
        </Descriptions.Item>
        {/* <Descriptions.Item label="" style={{ display: isToggle ? 'block' : 'none' }}>

        </Descriptions.Item> */}
        <Descriptions.Item label="计算功能">
          <EllipsisText>
            {
              ComputeFuncObj[
                (info?.computeFunc as ComputeFuncEnum) || ComputeFuncEnum.DAG
              ]
            }
          </EllipsisText>
        </Descriptions.Item>
        <Descriptions.Item label="计算模式">
          <EllipsisText>
            {
              ComputedModelObj[
                (info?.computeMode as ComputedModelEnum) || ComputedModelEnum.MPC
              ]
            }
          </EllipsisText>
        </Descriptions.Item>
        <Descriptions.Item label="项目描述">
          <EllipsisText>{info?.projectDesc}</EllipsisText>
        </Descriptions.Item>
      </Descriptions>
    </div>
  );
};

const JobTypeMap: Record<string, string> = {
  PSI: '隐私求交',
  FI: '联合预测',
};

export const getStatus = (status: string) => {
  if (status === 'REVIEWING') {
    return {
      statusType: 'warning',
      statusText: '待同意',
    };
  }
  if (status === 'APPROVED') {
    return {
      statusType: 'success',
      statusText: '已同意',
    };
  }
  if (status === 'REJECTED') {
    return {
      statusType: 'failed',
      statusText: '已拒绝',
    };
  }
  return {
    statusType: 'warning',
    statusText: '待同意',
  };
};
// 任务创建
export const CommonJobCreateInfo = (props: IProps) => {
  const { info, activeTab, status } = props;
  const { statusType, statusText } = getStatus(status);
  const [showTaskDetail, setShowTaskDetail] = useState(false);
  const service = useModel(MessageService);
  const { search } = useLocation();
  const parsedSearch = parse(search);

  const { ownerId } = parsedSearch as { ownerId: string };

  const openTaskModal = async () => {
    console.log(info, 'info');
    const res = await service.getMessageJobDetail({
      jobId: info.jobId,
      projectType: info?.projectId,
      ownerId: ownerId,
    });
    if (!res) return;
    if (info?.projectId === 'PSI') {
      localStorage.setItem('currentPSIDetailItem', JSON.stringify(res));
    } else {
      localStorage.setItem('currentPredictionItem', JSON.stringify(res));
    }
    setShowTaskDetail(true);
  };

  return (
    <div>
      <Descriptions column={1}>
        <Descriptions.Item
          className={styles.messageStatus}
          label={activeTab === 'process' ? '本方状态' : '当前状态'}
        >
          <StatusTag type={statusType} text={statusText} />
        </Descriptions.Item>
        <Descriptions.Item label="类型">
          <EllipsisText>{JobTypeMap[info?.projectId]}</EllipsisText>
        </Descriptions.Item>
        <Descriptions.Item label="任务名称">
          <EllipsisText>{info?.jobName}</EllipsisText>
        </Descriptions.Item>
        <Descriptions.Item label="发起机构">
          <EllipsisText>{info?.initiatorName}</EllipsisText>
        </Descriptions.Item>
        <Descriptions.Item label="发起端节点">
          <EllipsisText>
            {info.participantNodeInstVOS
              .map((i: any) => i.initiatorNodeName)
              .join('、')}
          </EllipsisText>
        </Descriptions.Item>
        <Descriptions.Item label="创建时间">
          <EllipsisText>
            {info?.gmtCreated ? formatTimestamp(info?.gmtCreated) : ''}
          </EllipsisText>
        </Descriptions.Item>
        <Descriptions.Item label="受邀节点" className={styles.descNodeStatusList}>
          <NodesStatus info={info} />
        </Descriptions.Item>
        {/* <Descriptions.Item label="" style={{ display: isToggle ? 'block' : 'none' }}>
          <div style={{ height: graphHeight, width: 500 }}>
            <VoteInstNodesGraph
              nodes={nodeData}
              edges={edgeData}
              groupNodeIds={groupNodeIds}
              setGraphHeight={setGraphHeight}
            />
          </div>
        </Descriptions.Item> */}
        <Descriptions.Item label="任务描述">
          <EllipsisText>{info?.description}</EllipsisText>
        </Descriptions.Item>
        <Descriptions.Item label="任务详情">
          <Tooltip title={info?.isJobExists ? '' : '当前任务已删除，无法查看详情'}>
            <Typography.Link disabled={!info?.isJobExists} onClick={openTaskModal}>
              查看详情
            </Typography.Link>
          </Tooltip>
        </Descriptions.Item>
      </Descriptions>
      <TaskDetailModal
        jobId={info.jobId}
        projectType={info?.projectId}
        show={showTaskDetail}
        onClose={() => setShowTaskDetail(false)}
      />
    </div>
  );
};

// 功能配置升级
export const ConfigUpdateInfo = () => {
  return <></>;
};
