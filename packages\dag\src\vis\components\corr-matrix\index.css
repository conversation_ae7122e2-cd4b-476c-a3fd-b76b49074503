.coefficient :global .ant-btn-link:hover {
  color: rgb(0 10 26 / 68%);
}

.coefficient :global .anticon-check {
  color: #52c41a;
}

.coefficient .chartBlock .features {
  margin-bottom: 16px;
}

.coefficient .chartBlock .features .title {
  display: inline-block;
  margin-bottom: 10px;
  font-size: 14px;
}

.coefficient .chartBlock .features .tagWrapper {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
  overflow-y: hidden;
}

.coefficient .chartBlock .features .hasMaxHeight {
  max-height: 108px;
}

.coefficient .chartBlock .features :global .anticon-down,
.coefficient .chartBlock .features :global .anticon-up {
  margin-left: 4px;
  font-size: 12px;
}

.coefficient .chartBlock .features .showAll {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
}

.coefficient .chartBlock .features .openTag {
  position: absolute;
  top: 72px;
  display: block;
  width: 48px;
  margin-right: 8px;
  margin-bottom: 16px;
  background-color: #fff;
  color: rgb(0 10 26 / 47%);
  text-align: center;
}

.coefficient .chartBlock .features .closeTag {
  position: relative;
  display: block;
  width: 48px;
  margin-right: 8px;
  margin-bottom: 16px;
  background-color: #fff;
  color: rgb(0 10 26 / 47%);
  text-align: center;
}

.coefficient .chartBlock .features .tag {
  position: relative;
  display: block;
  overflow: hidden;
  width: 48px;
  border: none;
  margin-right: 8px;
  margin-bottom: 16px;
  color: rgb(0 10 26 / 26%);
  cursor: pointer;
  text-align: center;
  text-overflow: ellipsis;
  user-select: none;
  white-space: nowrap;
}

.coefficient .chartBlock .features .tag.selected {
  background-color: #e6f7ff;
  color: #4762b2;
}

.coefficient .chartBlock .features .tag.selected::after {
  position: absolute;
  top: 0;
  right: 0;
  display: inline-block;
  width: 0;
  height: 0;
  border-width: 3px;
  border-style: solid;
  border-color: #4762b2 #4762b2 transparent transparent;
  content: '';
}

.customBtn {
  border: none;
  animation: none;
  color: rgb(0 10 26 / 68%);
}

.customBtn .customBtn:active {
  animation: none;
}
