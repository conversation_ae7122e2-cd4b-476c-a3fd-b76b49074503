import type { Dayjs } from 'dayjs';
import request from 'umi-request';

export interface LogListQueryParams {
  level?: string;
  thread?: string;
  logger?: string;
  message?: string;
  operationType?: string;
  resultStatus?: string;
  clientIp?: string;
  module?: string;
  userId?: string;
  dateRange: [Dayjs, Dayjs];
}

interface PageQuery {
  page: number;
  pageSize: number;
}

export type LogListRequest = Omit<LogListQueryParams, 'dateRange'> &
  PageQuery & { dateRange: string };

export interface LogListVO {
  id: number;
  level: string;
  thread: string;
  logger: string;
  message: string;
  timestamp: string;
}

export interface LogListResponse {
  data: {
    logs: LogListVO[];
    totalCount: number;
  };
  status: API.SecretPadResponseSecretPadResponseStatus;
}

// export interface LogListResponse extends API.SecretPadResponse {
//   data: {
//     logs: LogListVO[];
//     total: number;
//   };
// }

export async function getLogList(params: LogListRequest) {
  return request.post<LogListResponse>('/api/v1alpha1/logs  ', {
    data: params,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

export async function getAdminLogList(params: LogListRequest) {
  return request.post<LogListResponse>('/api/v1alpha1/admin/logs', {
    data: params,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}
