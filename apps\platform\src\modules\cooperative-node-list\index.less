.cooperativeNodeList {
  display: flex;
  height: 100%;
  box-sizing: border-box;
  flex-direction: column;
  padding: 20px;
  border-radius: 8px;

  .nodeListHeader {
    padding: 12px 20px;
    border-radius: 8px;
    margin-bottom: 12px;
    background-color: #fff;
  }

  .popoverContent {
    color: rgb(0 0 0 / 65%);
    font-size: 12px;
    font-weight: 400;
  }

  .content {
    flex: 1;
    padding: 20px;
    border-radius: 8px;
    background-color: #fff;

    .idText {
      color: rgb(0 0 0 / 45%);
      font-size: 12px;
    }

    // :global(.ant-table-thead) {
    //   height: 54px;
    // }

    // :global(.ant-table-row) {
    //   height: 62px;
    // }
  }

  .action {
    :global {
      .ant-btn {
        padding: 4px 0;
      }
    }
  }
}

.addCooperativeNodeDrawer {
  .subTitle {
    font-size: 14px;
    font-weight: 500;
  }

  .formGroup {
    padding: 16px;
    border-radius: 8px;
    margin-top: 8px;
    margin-bottom: 24px;
    background-color: rgb(0 0 0 / 2%);

    :global(.ant-input-disabled) {
      background-color: rgb(0 0 0 / 2%);
    }

    :global(.ant-form-item) {
      margin-bottom: 16px !important;
    }

    :global(.ant-form-item:last-child) {
      margin-bottom: 0 !important;
    }
  }
}

.cooperativeNodeDetailDrawer {
  .baseTitle {
    margin-bottom: 8px;
    color: rgb(0 0 0 / 88%);
    font-size: 14px;
    font-weight: 500;
  }

  :global {
    .ant-descriptions .ant-descriptions-item-container {
      align-items: center;
    }
  }

  .baseContent {
    padding: 16px;
    padding-bottom: 0;
    border-radius: 8px;
    margin-bottom: 16px;
    background-color: rgb(0 0 0 / 2%);
  }
}

.embeddedTag {
  border: 1px solid #cdfadf;
  border-radius: 4px;
  background-color: #ecfff4;
  color: rgb(0 0 0 / 88%);
  font-weight: 400;
}

.descriptionClass {
  :global(.ant-descriptions-item) {
    padding-bottom: 14px !important;
  }
}

.verifyCodeForm {
  label {
    width: 100%;
  }
}

.verifyCodeCode {
  display: flex;
  width: 100%;
  height: 22px;
  align-items: center;
  justify-content: space-between;

  .tips {
    color: rgb(0 0 0 / 88%);
    font-size: 14px;
    line-height: 22px;
  }

  .linkTips {
    margin-right: -28px;
  }
}
