.toolbar {
  display: flex;
  height: 36px;
  align-items: center;
  justify-content: space-between;
  background: rgb(0 0 0 / 2%);
}

.isNotFullScreenBtnClick {
  position: absolute;
  top: -28px;
  right: 0;
}

.toolButton {
  color: rgb(0 0 0 / 65%);
  font-size: 12px;

  &:hover {
    color: rgb(0 0 0 / 65%) !important;
  }
}

.editor {
  background-color: #fff;

  .title {
    display: flex;
    height: 56px;
    align-items: center;
    justify-content: space-between;
    margin: 0 24px;

    .titleInput {
      color: #1d2129;
      font-size: 20px;
      font-weight: 500;
    }
  }

  .content {
    display: flex;

    .fullscreenWorkspace {
      width: calc(100vw - 275px);
      padding-left: 12px;
      background: rgb(0 0 0 / 2%);
    }

    .normalWorkspace {
      width: 100%;
    }

    .rightConfig {
      width: 280px;
      border-top: 1px solid rgb(0 0 0 / 6%);
      border-left: 1px solid rgb(0 0 0 / 6%);
      background: #fff;

      .titleText {
        padding: 16px 12px;
        color: rgb(0 0 0 / 88%);
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
      }
    }
  }

  .code {
    height: 320px;
  }

  .fullscreenCode {
    height: calc(100vh - 140px);
  }
}

.footer {
  padding: 12px 20px 8px;
  background: #fff;
  box-shadow: inset 0 1px 0 0 rgb(0 0 0 / 6%);
}
