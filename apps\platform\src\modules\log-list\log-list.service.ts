import type { LogListRequest } from '@/services/secretpad/LogController';
import { getLogList, getAdminLogList } from '@/services/secretpad/LogController';
import { Model } from '@/util/valtio-helper';

export class LogListService extends Model {
  logListLoading = false;

  getSysLogList = async (params: LogListRequest) => {
    this.logListLoading = true;
    const result = await getLogList(params);
    this.logListLoading = false;
    return result;
  };

  getAdminLogList = async (params: LogListRequest) => {
    this.logListLoading = true;
    const result = await getAdminLogList(params);
    this.logListLoading = false;
    return result;
  };
}
