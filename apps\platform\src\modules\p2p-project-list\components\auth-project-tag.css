.content .nodeName {
  display: flex;
  overflow: hidden;
  height: 22px;
  align-items: center;
  color: rgba(0, 0, 0, 0.85);
  font-size: 12px;
  font-weight: 400;
  gap: 4px;
  letter-spacing: 0;
  line-height: 22px;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.content :global(.ant-tag) {
  line-height: 18px;
  padding-inline: 3px;
}
.content .tagApply {
  width: 28px;
  height: 20px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  border-radius: 4px;
  background-image: linear-gradient(180deg, #fafdff 0%, #9a0000 100%);
  font-size: 10px;
}
.content .applyContent {
  margin-bottom: 8px;
  font-size: 12px;
}
.content .processContent {
  margin-bottom: 8px;
  font-size: 12px;
}
.content .processContent .agree {
  color: #0068fa;
  cursor: pointer;
}
.content .processContent .agreeTag {
  border: solid 1px #23b65f;
  margin-right: 0;
  background-color: #ecfff4;
  background-image: none;
  color: #23b65f;
}
.content .processContent .reviewingTag {
  border: solid 1px #9a0000;
  margin-right: 0;
  background-color: #f0f5ff;
  background-image: none;
  color: #9a0000;
}
.content .processContent .rejectedTag {
  border: solid 1px red;
  margin-right: 0;
  background-color: #fff0f0;
  background-image: none;
  color: red;
}
.content .processContent .reject {
  color: red;
  cursor: pointer;
}
.content .processContent .tagProcess {
  width: 28px;
  height: 20px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  border-radius: 4px;
  background-image: linear-gradient(180deg, #fff 0%, #eee 100%);
  font-size: 10px;
}
