@import url('@/variables.less');

.myNode {
  height: 100%;
  box-sizing: border-box;
  padding: 20px;

  .box {
    padding: 12px 20px;
    border-radius: 8px;
    margin-bottom: 16px;
    background: #fff;
  }

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .title {
      padding: 0;
      margin: 0;
      margin-right: 4px;
      font-size: 16px;
    }

    .statusText {
      font-weight: 400;
    }
  }

  .card {
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 12px;
    background: #fff;

    .cardTitle {
      margin-bottom: 16px;
      font-size: 16px;
      font-weight: 700;
      line-height: 24px;
    }

    .address {
      :global(.anticon) {
        margin-left: 8px;
        color: rgb(0 0 0 / 45%);
      }

      :global(.anticon):hover {
        color: @PrimaryColor;
        cursor: pointer;
      }
    }

    :global(.ant-card-head) {
      border-bottom: 0;
    }

    :global(.ant-card-body) {
      padding: 0 24px;
      padding-bottom: 24px;
    }

    .titleinstance {
      display: flex;
      align-items: baseline;
      margin-bottom: 16px;

      h5 {
        margin: 0;
      }

      span {
        flex: 1;
        margin-left: 8px;
        color: rgb(0 0 0 / 47%);
        font-size: 12px;
      }
    }

    .instanceCount {
      margin-bottom: 16px;
      color: rgb(0 0 0 / 60%);

      span {
        color: @PrimaryColor;
      }
    }

    .box {
      height: 98px;
      padding: 16px 0 0 24px;
      margin-bottom: 16px;
      background: rgb(0 0 0 / 2%);

      .topBox {
        margin-bottom: 12px;
        color: rgb(0 10 26 / 89%);
        font-size: 14px;
        font-weight: 400;
      }

      .bottomImg {
        display: flex;
        gap: 8px;
      }
    }

    .spanClick {
      color: @PrimaryColor;
      cursor: pointer;
    }
  }

  .cardBottom {
    width: 100%;
    height: 24px;
  }
}

.memorySize {
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-top: 16px;
  margin-left: -8px;
  color: rgb(0 0 0 / 88%);
  font-size: 12px;
  font-weight: 500;
  text-align: center;
}

.divider {
  position: relative;
  margin: 0;

  .bg {
    position: absolute;
    right: 18px;
    bottom: 0;
    overflow: hidden;
    height: 144px;

    img {
      width: 164px;
    }
  }
}

.passwordModel {
  .name {
    margin-top: 20px;
    margin-bottom: 16px;
  }

  :global {
    .ant-modal-body {
      margin: 20px 0;
    }
  }
}
