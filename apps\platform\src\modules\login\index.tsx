import { message } from 'antd';
import React, { useEffect, useState } from 'react';
import { history } from 'umi';

import { ReactComponent as Logo } from '@/assets/logo-new.svg';
import { Platform } from '@/components/platform-wrapper';
import { DefaultComponentInterpreterService } from '@/modules/component-interpreter/component-interpreter-service';
import { DefaultModalManager } from '@/modules/dag-modal-manager';
import platformConfig from '@/platform.config';
import { getModel, Model, useModel } from '@/util/valtio-helper';

import { LoginForm } from './component/login-form';
import type { UserInfo } from './component/login-form';
import styles from './index.less';
import type { User } from './login.service';
import { LoginService } from './login.service';

export const LoginComponent: React.FC = () => {
  const loginModel = useModel(LoginModel);

  const windowWidth = window.innerWidth;
  const [percent, setPercent] = useState(Math.floor((windowWidth / 1920) * 100));

  useEffect(() => {
    const observer = new ResizeObserver((entries) => {
      setPercent(Math.floor((entries[0].contentRect.width / 1920) * 100));
    });
    observer.observe(document.body);
  }, []);

  return (
    <div className={styles.content}>
      <div className={styles.left}>
        <div className={styles.logo}>
          <Logo />
        </div>
        <div className={styles.introduce}>
          <div className={styles.title}>
            燕云<span className={styles.highlight}>隐私计算</span>平台
          </div>
          <div className={styles.description}>
            服务万千行业数据安全，促进数据价值流通
          </div>
          <img
            style={{
              width: percent + '%',
            }}
            src={require('@/assets/login-img.png')}
            alt="platform-introduce"
          />
        </div>
      </div>
      <div className={styles.right}>
        <LoginForm onConfirm={loginModel.loginConfirm} />
      </div>
    </div>
  );
};

export class LoginModel extends Model {
  token = '';
  loginService = getModel(LoginService);
  interpreterService = getModel(DefaultComponentInterpreterService);
  modalManager = getModel(DefaultModalManager);

  onViewMount(): void {
    this.modalManager.closeAllModals();
  }

  loginConfirm = async (loginFields: UserInfo) => {
    const { status, data } = await this.loginService.login({
      name: loginFields.name,
      password: loginFields.password,
    });

    const notFirstTimeIn = localStorage.getItem('notFirstTimeIn');
    this.token = data?.token || '';
    this.loginService.userInfo = data as User;
    if (status?.code === 0) {
      localStorage.setItem('User-Token', this.token);
      // P2P 模式跳转
      if (this.loginService.userInfo.platformType === Platform.AUTONOMY) {
        if (this.loginService.userInfo.ownerId) {
          localStorage.setItem('neverLogined', 'true');
          history.push(`/edge/home?ownerId=${this.loginService.userInfo.ownerId}`);
          message.success('登录成功');
          // 防止token失效后,直接刷新页面，重新登陆接口未重新调用
          this.interpreterService.getComponentI18n();
          return;
        }
      }

      // CENTER 和 EDGE 模式跳转
      if (this.loginService.userInfo.platformType === 'EDGE') {
        if (this.loginService.userInfo.ownerId) {
          localStorage.setItem('neverLogined', 'true');
          history.push(`/node?ownerId=${this.loginService.userInfo.ownerId}`);
        }
      } else {
        localStorage.setItem('neverLogined', 'true');
        if (notFirstTimeIn || !platformConfig.guide) {
          history.push('/center/node');
        } else {
          // edge 账号登陆center平台不需要跳转到guide页面
          if (
            this.loginService.userInfo.platformType === 'CENTER' &&
            this.loginService.userInfo?.ownerType === 'EDGE'
          ) {
            history.push('/center/node');
          } else {
            history.push('/guide');
          }
          localStorage.setItem('notFirstTimeIn', 'true');
        }
      }
      message.success('登录成功');
      // 防止token失效后,直接刷新页面，重新登陆接口未重新调用
      this.interpreterService.getComponentI18n();
    } else {
      message.error(status?.msg || '登录失败，请检查用户名或密码');
    }
  };
}
