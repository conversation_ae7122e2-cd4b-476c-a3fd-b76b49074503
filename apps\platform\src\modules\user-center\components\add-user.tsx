import type { FormInstance } from 'antd';
import { Drawer, Button, Space, Flex } from 'antd';
import { useRef } from 'react';

import FormBuilder from '@/components/form-builder/form';

import { AddUserFormConfig } from '../constant';

interface AddUserComponentProps {
  open: boolean;
  onClose?: () => void;
  onSubmit?: () => void;
}
const AddUserComponent = (props: AddUserComponentProps) => {
  const { open, onClose, onSubmit } = props;
  const formRef = useRef<{ form: FormInstance }>();

  const onCancel = () => {
    formRef.current?.form.resetFields();
    onClose?.();
  };
  return (
    <Drawer
      title="添加用户"
      width={500}
      open={open}
      onClose={onCancel}
      footer={
        <Flex justify="end">
          <Space>
            <Button onClick={onCancel}>取消</Button>
            <Button type="primary" onClick={onSubmit}>
              提交
            </Button>
          </Space>
        </Flex>
      }
    >
      <FormBuilder
        ref={formRef}
        config={AddUserFormConfig}
        formConfig={{ layout: 'vertical' }}
      />
    </Drawer>
  );
};

export default AddUserComponent;
