@import url('@/variables.less');

:global {
  .project-list-tour {
    width: 331px;
    height: 146px;
  }

  .project-list-tour-content {
    height: 100%;
  }

  .project-list-tour-inner {
    display: flex;
    height: 100%;
    flex-direction: column;
  }

  .project-list-tour-description {
    flex: 1;
    color: rgb(255 255 255 / 88%);
    font-size: 14px;
    font-weight: 400;
  }
}

.projectList {
  position: relative;
  height: 100%;
  padding: 16px 24px;

  .projectListHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .headerText {
      font-size: 16px;
      font-weight: 500;
    }
  }

  .spin {
    position: absolute;
    top: 150px !important;
  }

  .content {
    display: grid;
    // height: calc(100% - 70px);
    align-items: flex-start;
    justify-content: space-between;
    padding-top: 16px;
    grid-gap: 16px;
    // grid-template-columns: repeat(auto-fill, 371px);
    grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
    grid-template-rows: repeat(auto-fill, 210px);

    .computeMode {
      background: #fff;
    }

    & > i {
      // width: 22%;
      min-width: 360px; // 371px;
      box-sizing: border-box;
      padding: 0 16px;
      border-radius: 8px;
      margin-bottom: 20px;
    }

    .projectBox {
      min-width: 360px;
      min-height: 210px;
      box-sizing: border-box;
      border: 1px solid rgb(0 0 0 / 8%);
      border-radius: 4px;
      background-color: #fff;

      .listBox {
        position: relative;
        overflow: hidden;
        padding: 16px;

        .archiveTag {
          position: absolute;
          top: 0;
          right: 12px;
          display: flex;
          width: 40px;
          height: 32px;
          align-items: center;
          justify-content: center;
          background-image: url('../../assets/archive-bg.svg');
          color: rgb(0 0 0 / 40%);
          font-size: 12px;
        }
      }

      .header {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .bar {
          position: absolute;
          top: 6px;
          left: -16px;
          width: 3px;
          height: 14px;
          border-radius: 0 2px 2px 0;
          background-color: @PrimaryColor;
        }

        :global(.ant-typography) {
          overflow: hidden;
          max-width: 178px;
          margin: 0;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        @media only screen and (min-width: 1441px) {
          :global(.ant-typography) {
            max-width: 188px;
          }
        }

        @media only screen and (min-width: 2560px) {
          :global(.ant-typography) {
            max-width: 220px;
          }
        }

        :global(.anticon-edit):hover {
          cursor: pointer;
        }
      }

      .rows {
        display: flex;
        align-items: center;
        margin-top: 8px;
        color: rgb(0 0 0 / 60%);
        font-size: 12px;

        .nodeName {
          width: 100px;
        }

        :global {
          .ant-space {
            font-size: 12px;
          }
        }

        .line {
          width: 1px;
          height: 12px;
          margin: 0 8px;
          background: rgb(0 10 26 / 16%);
        }
      }

      .projects {
        display: flex;
        align-items: center;
        padding: 20px 0;
        border-top: 1px solid rgb(0 0 0 / 4%);
        // border-bottom: 1px solid rgb(0 0 0 / 4%);

        .titleName {
          color: rgb(0 0 0 / 40%);
          font-size: 14px;
          font-weight: 400;
          line-height: 20px;
        }

        .count {
          color: rgb(0 0 0 / 88%);
          font-size: 16px;
          font-weight: 500;
          line-height: 24px;
        }

        .count:hover {
          color: @PrimaryColor;
          cursor: pointer;
        }

        .task {
          display: flex;
          flex: 1;
          flex-direction: column;
          align-items: center;
          justify-content: center;
        }

        .line {
          width: 1px;
          height: 30px;
          background: rgb(0 0 0 / 8%);
        }
      }

      .bottom {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .time {
          color: rgb(0 0 0 / 40%);
          font-size: 12px;
          font-weight: 400;
          letter-spacing: 0;
          line-height: 18px;
        }
      }
    }

    .editButton {
      display: none;
    }

    .projectBox:hover {
      z-index: 99;
      box-shadow: 0 4px 10px 0 rgb(71 98 178 / 20%);

      .editButton {
        display: block;
        color: @PrimaryColor;
      }
    }
  }
}

.jobItemID {
  box-sizing: border-box;
  padding: 5px 0 5px 15px;
  color: #aaa;
  font-size: 12px;
}

.ellipsisName {
  :global(.ant-typography) {
    color: rgb(0 0 0 / 85%);
    font-size: 14px;
    font-weight: 600;
    letter-spacing: 0;
    line-height: 22px;
  }
}

.ellipsisDesc {
  margin-top: 12px;
  margin-bottom: 20px;
  color: rgb(0 0 0 / 45%);
  font-size: 12px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 18px;
}
