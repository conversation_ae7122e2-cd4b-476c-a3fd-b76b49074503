.formLabelItem {
  font-weight: 400;
}

.formBoldLabelItem {
  font-weight: 500;
}

.projectTipsOptions {
  color: rgb(0 0 0 / 45%);
}

.buttonDisable {
  cursor: not-allowed !important;
  opacity: 0.4;
  pointer-events: none;
}

.nodeVotersContent {
  position: relative;
  width: 100%;
  padding: 16px 12px;
  padding-bottom: 0;
  border-radius: 2px;
  margin-bottom: 16px;
  background: #f6f6f6;

  :global(.ant-form-item) {
    margin-bottom: 8px;
  }

  .addTag {
    justify-content: flex-start !important;
    padding: 0;
    padding-top: 0 !important;
    padding-left: 0 !important;
    background: transparent;
  }
}

.nodeInfoformLabel {
  margin-bottom: 16px;
  font-weight: 400;
}

.deleteBtn {
  position: absolute;
  top: 24px;
  right: 16px;
  color: rgb(0 0 0 / 45%);

  &:hover {
    color: #9a0000;
  }
}
