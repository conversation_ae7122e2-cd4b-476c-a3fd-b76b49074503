import { Tabs, Button } from 'antd';

import styles from './index.less';

interface TabSearchBoxProps {
  tabs: {
    label: string;
    value: string;
  }[];
  children: React.ReactNode;
  onTabChange?: (value: string) => void;
  showReset?: boolean;
  onReset?: () => void;
}

export const TabSearchBox = (props: TabSearchBoxProps) => {
  const { tabs, onTabChange, showReset, onReset, children } = props;
  return (
    <div className={styles.tabSearchBoxWrapper}>
      <Tabs
        items={tabs.map((tab) => ({
          label: tab.label,
          key: tab.value,
        }))}
        onChange={onTabChange}
      />
      <div className={styles.searchBoxWrapper}>
        <div className={styles.searchBox}>{children}</div>
        {showReset && (
          <div className={styles.resetBtnWrapper}>
            <Button color="primary" variant="outlined" onClick={onReset}>
              重置
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};
