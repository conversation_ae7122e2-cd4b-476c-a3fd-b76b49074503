import { UpOutlined, DownOutlined } from '@ant-design/icons';
import { Space } from 'antd';
import { useState } from 'react';

import styles from '../detail.less';

export const DetailBox = (props: {
  title: string;
  collapseHeight?: number;
  showCollapse?: boolean;
  children: React.ReactNode;
}) => {
  const { title, showCollapse, children, collapseHeight = 22 } = props;
  const [collapse, setCollapse] = useState(false);

  return (
    <div className={styles.detailBox}>
      <div className={styles.header}>
        <div className={styles.title}>{title}</div>
        {showCollapse && (
          <div className={styles.collapse} onClick={() => setCollapse(!collapse)}>
            {!collapse ? (
              <Space>
                收起 <UpOutlined />
              </Space>
            ) : (
              <Space>
                展开 <DownOutlined />
              </Space>
            )}
          </div>
        )}
      </div>
      <div
        className={styles.body}
        style={{ height: collapse ? collapseHeight : 'auto' }}
      >
        {children}
      </div>
    </div>
  );
};
