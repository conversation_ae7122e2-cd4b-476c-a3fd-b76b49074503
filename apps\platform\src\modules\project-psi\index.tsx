import { QuestionCircleFilled, SearchOutlined } from '@ant-design/icons';
import type { TableColumnsType } from 'antd';
import {
  Button,
  Flex,
  Input,
  message,
  Popconfirm,
  Popover,
  Select,
  Space,
  Typography,
} from 'antd';
import dayjs from 'dayjs';
import { debounce } from 'lodash';
import { parse, stringify } from 'query-string';
import { useEffect } from 'react';
import type { ChangeEvent } from 'react';
import { history, useLocation } from 'umi';

import CommonTable from '@/components/common-table';
import { TabSearchBox } from '@/components/tab-search-box';
import { getModel, Model, useModel } from '@/util/valtio-helper';

import { getCurrentNodeId, TaskStatusLabel } from '../project-prediction';

import { CreatePSIModal } from './components/create-psi';
import { PSIDetailDrawer } from './components/psi-detail';
import style from './index.less';
import type { AllocatedNode } from './project-psi.service';
import { ProjectPSIService } from './project-psi.service';

const SelectOptions = [
  { label: '全部状态', value: 'all' },
  { label: '运行中', value: 'RUNNING' },
  { label: '成功', value: 'SUCCEED' },
  { label: '失败', value: 'FAILED' },
  { label: '待审批', value: 'PENDING_APPROVAL' },
  { label: '已拒绝', value: 'REJECTED' },
  { label: '已取消', value: 'CANCELED' },
];

const tabs = [
  { label: '全部', value: 'all' },
  { label: '我发起', value: 'apply' },
  { label: '我受邀', value: 'invited' },
];

// 这里在需要更换psi版本的时候修改
const psiNodeDef = {
  name: 'psi',
  domain: 'data_prep',
  version: '1.0.0',
};

const fieldMap = {
  dataset1: 'input/input_ds1/keys',
  dataset2: 'input/input_ds2/keys',
  protocol: 'protocol',
  curveType: 'protocol/PROTOCOL_ECDH',
  resort: 'sort_result',
  receivers: 'receiver_parties',
  allowEmpty: 'allow_empty_result',
  joinType: 'join_type',
  leftJoin: 'join_type/left_join/left_side',
  firstKeyDuplicated: 'input_ds1_keys_duplicated',
  secondKeyDuplicated: 'input_ds2_keys_duplicated',
};

const formatSubmitData = (data: Record<string, any>) => {
  const attr_paths: string[] = [];
  const attrs = [];
  for (const key in fieldMap) {
    if (!data[key]) continue;
    if (key === 'dataset1' || key === 'dataset2') {
      attrs.push({
        ss: data[key].colName,
        is_na: false,
      });
    } else if (key === 'leftJoin') {
      attrs.push({
        ss: [data[key]],
        is_na: false,
      });
    } else if (typeof data[key] === 'boolean') {
      attrs.push({
        b: data[key],
        is_na: false,
      });
    } else if (typeof data[key] === 'string') {
      attrs.push({
        s: data[key],
        is_na: false,
      });
    } else if (
      data[key] instanceof Array &&
      data[key].length &&
      typeof data[key][0] === 'string'
    ) {
      attrs.push({
        ss: data[key],
        is_na: false,
      });
    }
    attr_paths.push(fieldMap[key as keyof typeof fieldMap]);
  }

  return {
    attrs,
    attr_paths,
  };
};

const canDeleteStatus = ['FAILED', 'SUCCEED', 'CANCELED', 'REJECTED'];
const checkCanCancel = (record: API2.PSIJobInfo, ownerId: string) => {
  return (
    (record.status === 'PENDING_APPROVAL' && ownerId === record.initiator) ||
    ['PAUSED', 'RUNNING'].includes(record.status)
  );
};
const checkCurrentNodeId = (record: API2.PSIJobInfo) => {
  return record.nodes.find((node: any) => node.nodeType === 'primary')?.nodeId || '';
};
let timer: NodeJS.Timeout | null = null;

const PSIComponent = () => {
  const model = useModel(PSIModel);
  const { search } = useLocation();
  const parsedSearch = parse(search);

  const { ownerId } = parsedSearch as { tab?: string; ownerId?: string };
  const toMessageCenter = () => {
    history.push({
      pathname: '/edge/messages',
      search: stringify({
        ownerId,
        active: 'process',
      }),
    });
  };
  const columns: TableColumnsType = [
    {
      title: '任务名称',
      dataIndex: 'jobName',
      key: 'jobName',
    },
    {
      title: '发起机构',
      dataIndex: 'initiatorName',
      key: 'initiatorName',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 160,
      render: (status, record) => (
        <Space>
          <TaskStatusLabel status={status} />
          {status === 'REJECTED' && (
            <Popover
              title="拒绝原因"
              content={
                record.partyVoteInfos.find((item: any) => item.action === 'REJECTED')
                  ?.reason || '无'
              }
            >
              <QuestionCircleFilled />
            </Popover>
          )}
        </Space>
      ),
    },
    {
      title: '提交时间',
      dataIndex: 'createTime',
      key: 'createTime',
      render: (text) => {
        if (text) {
          return dayjs(text).add(8, 'h').format('YYYY-MM-DD HH:mm:ss');
        }
        return '-';
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space size="middle">
          <Typography.Link
            onClick={() => {
              model.currentTask = record as API2.PSIJobInfo;
              localStorage.setItem('currentPSIDetailItem', JSON.stringify(record));
              // model.showDetailDrawer = true;
              history.push(`/edge/psi/${record.jobId}?ownerId=${ownerId}`);
            }}
          >
            详情
          </Typography.Link>
          {/* 审批 */}
          {record.status === 'PENDING_APPROVAL' &&
            record.partyVoteInfos.find((item: any) => item.action === 'REVIEWING')
              ?.partyId === ownerId && (
              <Typography.Link onClick={() => toMessageCenter()}>
                去审批
              </Typography.Link>
            )}
          {checkCanCancel(record as API2.PSIJobInfo, ownerId as string) && (
            <Typography.Link
              onClick={() =>
                model.cancelJob({
                  jobId: record.jobId,
                  nodeId: checkCurrentNodeId(record as API2.PSIJobInfo),
                })
              }
            >
              取消
            </Typography.Link>
          )}
          {/* 结果下载 */}
          {record.status === 'SUCCEED' &&
            JSON.parse(record.receiverParties).includes(
              getCurrentNodeId(model.parties, ownerId as string),
            ) && (
              <Typography.Link
                onClick={() =>
                  model.download(record.jobId, record.domainDataId, ownerId as string)
                }
              >
                结果下载
              </Typography.Link>
            )}
          {/* 删除 */}
          {canDeleteStatus.includes(record.status) && record.initiator === ownerId && (
            <Popconfirm
              title="提示"
              description="确定删除任务吗？"
              onConfirm={() => {
                model.deleteJob({
                  jobId: record.jobId,
                  nodeId: checkCurrentNodeId(record as API2.PSIJobInfo),
                });
              }}
              okText="确定"
              cancelText="取消"
            >
              <Typography.Link>删除</Typography.Link>
            </Popconfirm>
          )}
          {record.status === 'FAILED' && record.initiator === ownerId && (
            <Typography.Link
              onClick={() =>
                model.restartJob({
                  jobId: record.jobId,
                  nodeId: checkCurrentNodeId(record as API2.PSIJobInfo),
                })
              }
            >
              重启
            </Typography.Link>
          )}
        </Space>
      ),
    },
  ];

  useEffect(() => {
    model.getTable();
  }, []);

  useEffect(() => {
    if (model.tableList.some((item) => item.status === 'RUNNING') && !timer) {
      timer = setInterval(() => {
        if (!model.tableList.some((item) => item.status === 'RUNNING')) {
          clearInterval(timer as NodeJS.Timeout);
          timer = null;
          return;
        }
        model.getJobStatus({
          jobIds: model.tableList
            .filter((item) => item.status === 'RUNNING')
            .map((item) => item.jobId),
        });
      }, 5000);

      return () => {
        clearInterval(timer as NodeJS.Timeout);
        timer = null;
      };
    }
  }, [model.tableList]);

  return (
    <div className={style.psiWrapper}>
      <TabSearchBox tabs={tabs} onTabChange={(value) => model.onRadioChange(value)}>
        <Space size="middle">
          <span>任务名称</span>
          <Input
            placeholder="搜索任务"
            onChange={(e) => model.onInputSearch(e)}
            style={{ width: 200 }}
            value={model.searchText}
            suffix={
              <SearchOutlined
                style={{
                  color: '#aaa',
                }}
              />
            }
          />
          {/* <Radio.Group
            options={RadioOptions}
            value={model.searchType}
            optionType="button"
            onChange={(e) => model.onRadioChange(e)}
          /> */}
          <span>状态</span>
          <Select
            style={{ width: 200 }}
            value={model.searchStatus}
            options={SelectOptions}
            onChange={(e) => model.onSelectChange(e)}
          />
        </Space>
      </TabSearchBox>
      <div className={style.content}>
        <Flex justify="end" style={{ marginBottom: 16 }}>
          <Button type="primary" onClick={() => (model.showCreateDrawer = true)}>
            新建任务
          </Button>
        </Flex>
        <CommonTable
          columns={columns}
          dataSource={model.tableList}
          xScroll
          rowKey="jobId"
          loading={model.loading}
          pagination={{
            total: model.total,
            current: model.pageNum,
            pageSize: model.pageSize,
            showTotal: (total: number) => `共 ${total} 条`,
            showSizeChanger: true,
          }}
          onChange={(pagination) => {
            model.pageNum = pagination.current || 1;
            model.pageSize = pagination.pageSize || 10;
            model.getTable();
          }}
        />
      </div>
      <CreatePSIModal
        open={model.showCreateDrawer}
        onClose={() => (model.showCreateDrawer = false)}
        onConfirm={(data) => model.createJob(data)}
      />
      <PSIDetailDrawer
        open={model.showDetailDrawer}
        data={model.currentTask}
        ownerId={ownerId || ''}
        onClose={() => (model.showDetailDrawer = false)}
      />
    </div>
  );
};

export class PSIModel extends Model {
  searchText = '';
  searchType = 'all';
  searchStatus = 'all';
  showCreateDrawer = false;
  showDetailDrawer = false;
  currentTask: API2.PSIJobInfo | null = null;
  loading = false;
  createLoading = false;

  pageNum = 1;
  pageSize = 10;
  total = 0;

  tableList: API2.PSIJobInfo[] = [];
  parties: AllocatedNode[] = [];
  service = getModel(ProjectPSIService);
  logDetailLoading = false;

  constructor() {
    super();
  }

  resetPagination() {
    this.pageNum = 1;
  }

  getParties = async () => {
    const res = await this.service.getAllocatedNodes();
    this.parties = res;
  };

  async getTable() {
    this.loading = true;
    const res = await this.service.getPSIJobList({
      page: this.pageNum,
      pageSize: this.pageSize,
      taskName: this.searchText,
      status: this.searchStatus === 'all' ? '' : this.searchStatus,
      partner: this.searchType === 'invited' ? 1 : 0,
      initiator: this.searchType === 'apply' ? 1 : 0,
    });
    if (res) {
      this.total = res.total || 0;
      this.tableList = res.psiJobs || [];
    }
    this.loading = false;
  }

  onInputSearch(e: ChangeEvent<HTMLInputElement>) {
    this.searchText = e.target.value;
    console.log(this.searchText);
    this.debouceSearch();
  }

  debouceSearch = debounce(this.getTable, 300);

  onRadioChange(value: string) {
    this.searchType = value;
    this.pageNum = 1;
    this.getTable();
  }

  onSelectChange(value: string) {
    this.searchStatus = value;
    this.pageNum = 1;
    this.getTable();
  }

  async createApproval(data: { jobId: string; parties: AllocatedNode[] }) {
    const parties = data.parties;
    const initiatorId = parties[0].instId;
    const partnerId = parties[1].instId;
    await this.service.createPSIApproval({
      initiatorId,
      voteType: 'COMMON_JOB_CREATE',
      voteConfig: {
        projectId: 'PSI',
        jobId: data.jobId,
        participants: [initiatorId, partnerId],
        participantNodeInstVOS: [
          {
            initiatorNodeId: data.parties[0].nodeId,
            invitees: [
              {
                inviteeId: data.parties[1].nodeId,
              },
            ],
          },
        ],
      },
    });
  }

  async createJob(data: Record<string, any>) {
    this.createLoading = true;
    console.log(data, 'data');
    const { attrs, attr_paths } = formatSubmitData(data);
    console.log(attrs, attr_paths, 'attrs');
    const initiatorNodeId = data.dataset1.nodeId;
    const partnerNodeId = data.dataset2.nodeId;
    const initiatorDatatableId = data.dataset1.datatableId;
    const partnerDatatableId = data.dataset2.datatableId;
    const res = await this.service.createPSIJob({
      name: data.name,
      description: data.description,
      initiatorNodeId,
      partnerNodeId,
      initiatorDatatableId,
      partnerDatatableId,
      receiverParties: data.receivers,
      nodeInfo: {
        codeName: 'data_prep/psi',
        nodeDef: {
          attrPaths: attr_paths,
          attrs,
          ...psiNodeDef,
        },
      } as API2.PSINodeInfo,
    });
    if (res.status?.code === 0) {
      this.createApproval({
        jobId: res.data?.jobId || '',
        parties: [
          this.parties.find((item) => item.nodeId === data.dataset1.nodeId)!,
          this.parties.find((item) => item.nodeId === data.dataset2.nodeId)!,
        ],
      });
      this.showCreateDrawer = false;
      this.getTable();
    } else {
      message.error(`任务新建失败: ${res.status?.msg}`);
    }
    this.createLoading = false;
  }

  async deleteJob(data: { jobId: string; nodeId: string }) {
    await this.service.deletePSIJob(data);
    this.getTable();
  }

  async cancelJob(data: { jobId: string; nodeId: string }) {
    await this.service.cancelPSIJob(data);
    this.getTable();
  }

  async pauseJob(data: { jobId: string; nodeId: string }) {
    await this.service.pausePSIJob(data);
    this.getTable();
  }

  async restartJob(data: { jobId: string; nodeId: string }) {
    await this.service.restartPSIJob(data);
    this.getTable();
  }

  async getJobStatus(data: { jobIds: string[] }) {
    const res = await this.service.getPSIJobStatus(data);
    if (res.status?.code === 0) {
      console.log(res.data, 'res');
      const statusList = res.data?.taskStatusList || [];
      const tempTableList = [...this.tableList];
      statusList.forEach((item) => {
        const job = tempTableList.find((table) => table.jobId === item.jobId);
        if (job) {
          job.status = item.status;
          job.domainDataId = item.domainDataId;
        }
      });
      this.tableList = tempTableList;
    }
  }

  async download(jobId: string, domainDataId: string, ownerId: string) {
    const nodeId = this.parties.find((party) => party.instId === ownerId)?.nodeId || '';
    await this.service.download({
      jobId,
      nodeId,
      domainDataId,
      filename: `psi/${nodeId}.csv`,
    });
  }

  async getLogs(data: { jobId: string; nodeId: string }) {
    const res = await this.service.getPSILogs(data);
    if (res.status?.code === 0) {
      return res.data;
    }
    return [];
  }

  async getLogDetails(data: { jobId: string; nodeId: string }) {
    this.logDetailLoading = true;
    const res = await this.service.getPSILogDetails(data);
    if (res.status?.code === 0) {
      return res.data;
    } else return undefined;
  }
}

export default PSIComponent;
