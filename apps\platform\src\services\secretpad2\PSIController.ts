import request from 'umi-request';

export async function createPSIJob(
  body: API2.PSIJobCreateRequest,
  options?: { [key: string]: any },
) {
  return request<API2.PSIJobCreateResponse>('/api/v1alpha1/psi/job/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function createPSIApproval(
  body: API2.PSIApprovalCreateRequest,
  options?: { [key: string]: any },
) {
  return request<API2.PSIApprovalCreateResponse>('/api/v1alpha1/psi/approval/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function getPSIJobList(
  body: API2.PSIJobListRequest,
  options?: { [key: string]: any },
) {
  return request<API2.PSIJobListResponse>('/api/v1alpha1/psi/job/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function getPSIDatatables(
  body: {
    nodeId: string;
  },
  options?: { [key: string]: any },
) {
  return request<API2.PSIJobDatatablesResponse>('/api/v1alpha1/psi/job/datatables', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

export async function deletePSIJob(
  body: {
    jobId: string;
    nodeId: string;
  },
  options?: { [key: string]: any },
) {
  return request<API2.SecretPadResponse>('/api/v1alpha1/psi/job/delete', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

// 停止
export async function stopPSIJob(
  body: {
    jobId: string;
    nodeId: string;
  },
  options?: { [key: string]: any },
) {
  return request<API2.SecretPadResponse>('/api/v1alpha1/psi/job/stop', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

// 重启
export async function restartPSIJob(
  body: {
    jobId: string;
    nodeId: string;
  },
  options?: { [key: string]: any },
) {
  return request<API2.SecretPadResponse>('/api/v1alpha1/psi/job/restart', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

// 取消
export async function cancelPSIJob(
  body: {
    jobId: string;
    nodeId: string;
  },
  options?: { [key: string]: any },
) {
  return request<API2.SecretPadResponse>('/api/v1alpha1/psi/job/cancel', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

// 批量查询任务状态
export async function getPSIJobStatus(
  body: {
    jobIds: string[];
  },
  options?: { [key: string]: any },
) {
  return request<API2.PSITaskStatusResponse>('/api/v1alpha1/psi/job/status', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

export async function getPSILogs(
  body: {
    jobId: string;
    nodeId: string;
  },
  options?: { [key: string]: any },
) {
  return request<API2.PSILogsResponse>('/api/v1alpha1/psi/job/logs', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

export async function getPSILogDetails(
  body: {
    jobId: string;
    nodeId: string;
  },
  options?: { [key: string]: any },
) {
  return request<API2.PSILogDetailsResponse>('/api/v1alpha1/psi/job/detail-logs', {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}
