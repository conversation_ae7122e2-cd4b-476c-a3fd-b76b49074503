// .messageInfDrawer {
//   background-color: #fff;
// }

.descNodeStatusList {
  :global(.ant-descriptions-item-content) {
    display: block !important;
    height: auto !important;
  }
}

//   .configToggle {
//     color: #9a0000;
//     cursor: pointer;
//   }
// }

.dagBoxContent {
  width: 100%;
  height: 240px;
}

.sheetText {
  width: 42px;
  height: 22px;
  margin: 8px 0;
  color: rgb(0 0 0 / 88%);
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
}

.sheetTag {
  border-radius: 10px;
  background-color: rgb(0 104 250 / 8%);
  color: rgb(0 0 0 / 65%);
}

.nodesStatusWrapper {
  display: flex;
  padding: 8px;
  border-radius: 4px;
  background-color: #f6f6f6;

  .nodeBoxWrapper {
    display: flex;
    align-items: center;
  }

  .nodeBox {
    width: 204px;
    padding: 8px;
    border-radius: 4px;
    background-color: #fff;
  }

  .nodeBoxTitle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 12px;
  }

  .nodeBoxContentItem {
    font-size: 14px;

    .nodeId {
      font-weight: 700;
    }

    .myNode {
      color: rgb(0 0 0 / 40%);
    }
  }

  .instBox {
    padding: 2px 4px;
    background-color: #f6f6f6;
    color: rgb(0 0 0 / 40%);
  }
}

.messageStatus {
  :global(.ant-descriptions-item-container) {
    align-items: center;
  }
}
