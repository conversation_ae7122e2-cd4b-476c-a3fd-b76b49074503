import type { RefSelectProps, SelectProps } from 'antd';
import { Select } from 'antd';
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';

export interface CustomFormItemProps<T = string> {
  id?: string;
  value?: T;
  onChange?: (value: T) => void;
}

interface AsyncOptionsSelectProps extends CustomFormItemProps<string | number> {
  fetchOptions: () => Promise<
    { label: string; value: AsyncOptionsSelectProps['value'] }[]
  >;
}

export const AsyncOptionsSelect = (props: AsyncOptionsSelectProps) => {
  const { id, value, onChange, fetchOptions } = props;

  const [options, setOptions] = useState<Awaited<ReturnType<typeof fetchOptions>>>([]);

  useEffect(() => {
    fetchOptions().then((result) => {
      setOptions(result);
    });
  }, []);

  return (
    <Select
      id={id}
      value={value}
      options={options}
      onChange={(val) => onChange?.(val)}
    />
  );
};

export const ControlledSelect = forwardRef(
  (props: CustomFormItemProps<string | number> & SelectProps, ref) => {
    const { id, value, onChange } = props;

    const [options, setOptions] = useState<{ label: string; value: string | number }[]>(
      [],
    );
    const selectRef = useRef<RefSelectProps>(null);

    useImperativeHandle(ref, () => ({
      options,
      setOptions,
      ...selectRef.current,
    }));

    return (
      <Select
        ref={selectRef}
        id={id}
        value={value}
        options={options}
        onChange={(val) => onChange?.(val)}
        {...props}
      />
    );
  },
);

ControlledSelect.displayName = 'ControlledSelect';
