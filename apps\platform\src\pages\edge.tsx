import Icon from '@ant-design/icons';
import { Badge } from 'antd';
import { parse } from 'query-string';
import type { CSSProperties } from 'react';
import { useEffect } from 'react';
import { useLocation } from 'umi';

// import { ReactComponent as DataSource } from '@/assets/data-source.svg';
import { ReactComponent as DataSource } from '@/assets/icon-data.svg';
import { ReactComponent as Workbench } from '@/assets/icon-home.svg';
import { ReactComponent as LogCenter } from '@/assets/icon-log.svg';
import { ReactComponent as MessageCenter } from '@/assets/icon-message.svg';
import { ReactComponent as CooperativeNode } from '@/assets/icon-node.svg';
import { ReactComponent as ProjectCenter } from '@/assets/icon-project.svg';
// import { ReactComponent as ResultManager } from '@/assets/resultmanager.svg';
import { hasAccess, Platform } from '@/components/platform-wrapper';
// import { DataSourceListComponent } from '@/modules/data-source-list';
import { EdgeLayout } from '@/modules/layout/edge-layout';
import { LabelWithBadge } from '@/modules/layout/edge-layout/left-view';
// import { CooperativeNodeListComponent } from '@/modules/cooperative-node-list';
// import DataManagerComponent from '@/modules/data-manager/data-manager.view';
// import { DataPoolComponent } from '@/modules/data-pool';
import { HomeLayout } from '@/modules/layout/home-layout';
import { HomeLayoutService } from '@/modules/layout/home-layout/home-layout.service';
import { ManagementLayoutComponent } from '@/modules/layout/management-layout';
// import { LogListComponent } from '@/modules/log-list';
// import { MessageComponent } from '@/modules/message-center';
import { MessageService } from '@/modules/message-center/message.service';
import { NodeService } from '@/modules/node';
// import P2pProjectListComponent from '@/modules/p2p-project-list';
// import P2PWorkbenchComponent from '@/modules/p2p-workbench/workbench.view';
// import { PredictionComponent } from '@/modules/project-prediction';
// import { PSIComponent } from '@/modules/project-psi';
// import { ResultManagerComponent } from '@/modules/result-manager/result-manager.view';
// import UserCenterComponent from '@/modules/user-center';
import { useModel } from '@/util/valtio-helper';

interface MenuItem {
  label: React.ReactNode;
  icon?: React.ReactNode;
  component?: React.ReactNode;
  key: string;
  children?: MenuItem[];
}

const IconStyle: CSSProperties = {
  width: 20,
  height: 20,
  fontSize: 20,
};

const EdgePage = () => {
  const { search } = useLocation();
  const { ownerId } = parse(search);
  const homeLayoutService = useModel(HomeLayoutService);
  const messageService = useModel(MessageService);
  const nodeService = useModel(NodeService);

  const menuItems: MenuItem[] = [
    {
      label: '首页',
      icon: <Icon style={IconStyle} component={Workbench} />,
      key: 'home',
    },
    {
      label: '数据源管理',
      icon: <Icon style={IconStyle} component={DataSource} />,
      key: 'data-source',
    },
    {
      label: '数据管理',
      icon: <Icon style={IconStyle} component={DataSource} />,
      key: 'data-management-folder',
      children: [
        {
          label: '我的数据',
          key: 'data-manager',
        },
        {
          label: '外部数据',
          key: 'data-pool',
        },
      ],
    },
    {
      label: '项目中心',
      icon: <Icon style={IconStyle} component={ProjectCenter} />,
      // component: <P2pProjectListComponent />,
      key: 'project-center',
      children: [
        {
          label: '联合建模',
          key: 'my-project',
        },
        {
          label: '联合预测',
          key: 'prediction',
        },
        {
          label: '隐私求交',
          key: 'psi',
        },
      ],
    },
    {
      label: '合作节点',
      icon: <Icon style={IconStyle} component={CooperativeNode} />,
      key: 'cooperative-node',
    },
    {
      label: (
        <LabelWithBadge label="消息中心" count={homeLayoutService.messageCount || 0} />
      ),
      icon: <Icon style={IconStyle} component={MessageCenter} />,
      key: 'messages',
    },
    {
      label: '日志中心',
      icon: <Icon style={IconStyle} component={LogCenter} />,
      key: 'logs',
    },
    // {
    //   label: '用户中心',
    //   icon: <UserOutlined />,
    //   component: <UserCenterComponent />,
    //   key: 'user-center',
    // },

    // {
    //   label: '结果管理',
    //   icon: <Icon component={ResultManager} />,
    //   component: <ResultManagerComponent />,
    //   key: 'result',
    // },
  ];

  const isAutonomyMode = hasAccess({ type: [Platform.AUTONOMY] });

  useEffect(() => {
    const getNodeList = async () => {
      const nodeList = await nodeService.listNode();
      if (ownerId) {
        const node = nodeList.find((n) => ownerId === n.nodeId);
        if (node) nodeService.setCurrentNode(node);
      }
    };
    const getMessageTotal = async () => {
      if (ownerId) {
        const res = await messageService.getMessageCount(ownerId as string);
        if (res.status) {
          homeLayoutService.setMessageCount(res?.data || 0);
        }
      }
    };
    homeLayoutService.setSubTitle('Edge');
    if (!isAutonomyMode) {
      getNodeList();
    }
    // 获取未处理消息数量
    getMessageTotal();
  }, []);
  return <EdgeLayout menuItems={menuItems}></EdgeLayout>;
};

export default EdgePage;
