.nodes {
  display: flex;
  flex-wrap: wrap;
}

.node {
  width: 349px;
  height: 136px;
  box-sizing: border-box;
  padding: 12px;
  border: 1px solid rgb(0 0 0 / 6%);
  border-radius: 8px;
  margin-right: 16px;
  background-color: #fff;
}

.node:hover {
  border: 1px solid #9a0000;
  transition: border 0.8s;
}

.nodeTitle {
  display: flex;
  align-items: center;
  justify-content: space-between;

  :global(.ant-tag) {
    border: 0;
  }

  .nodeTitleText {
    color: rgb(0 0 0 / 85%);
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0;
    line-height: 22px;
  }
}

.authNodeText {
  color: rgb(0 0 0 / 65%);
  font-size: 12px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 22px;
}

.nodeStatusIcon {
  display: flex;
  width: 20px;
  height: 20px;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  background-color: #d8fbe7;
}

.authNodeNum {
  color: #9a0000 !important;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 22px;
}

.nodeIdText {
  width: 220px;
  margin-left: 28px;
  color: rgb(0 0 0 / 45%);
  font-size: 12px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 20px;
}

.nodeDivider {
  margin: 8px !important;
}

.nodePopoverList {
  overflow: auto;
  max-height: 300px;
  border-radius: 0;
  border-top: 1px solid rgb(5 5 5 / 6%);
  border-right: none !important;
  border-bottom: 1px solid rgb(5 5 5 / 6%);
  border-left: none !important;

  :global(.ant-list-item) {
    padding: 4px 8px !important;
  }
}

.dataSheetText {
  margin-left: 28px;
  color: rgb(0 0 0 / 60%);
  font-size: 12px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 22px;
}

.dataText {
  color: rgb(0 0 0 / 60%);
  font-size: 12px;
  font-weight: 400;
  letter-spacing: 0;
}

.dataSheetContent {
  display: inline-block;
  width: 200px;
  margin-left: 4px;
}

.nullDataSheet {
  margin-left: 28px;
  color: rgb(0 0 0 / 60%);
  font-size: 12px;
  font-weight: 400;
  letter-spacing: 0;
}
