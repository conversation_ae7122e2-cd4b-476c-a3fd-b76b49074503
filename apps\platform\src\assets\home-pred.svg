<svg width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M38 62C42.2614 62 46.1948 62.1883 49.3643 62.5H57V64.5C57 65.8807 48.4934 67 38 67C27.5066 67 19 65.8807 19 64.5V62.5H26.6357C29.8052 62.1883 33.7386 62 38 62Z" fill="#5AADEE"/>
<g filter="url(#filter0_i_1581_3633)">
<ellipse cx="38" cy="62.5" rx="19" ry="2.5" fill="url(#paint0_linear_1581_3633)"/>
</g>
<ellipse cx="37" cy="30" rx="26" ry="27" fill="url(#paint1_linear_1581_3633)"/>
<ellipse cx="34" cy="30" rx="26" ry="27" fill="url(#paint2_linear_1581_3633)"/>
<ellipse cx="33" cy="30" rx="20" ry="22" stroke="#60CCEA" stroke-opacity="0.4"/>
<ellipse cx="32.5" cy="30" rx="10.5" ry="13" stroke="#60CCEA" stroke-opacity="0.4"/>
<g filter="url(#filter1_di_1581_3633)">
<path d="M49.9509 19.0245L30.5532 28.7234C30.2685 28.8658 30.1875 29.2344 30.3864 29.483L31.7367 31.1708C31.8888 31.361 32.1558 31.4139 32.3689 31.2961L50.9382 21.0341C50.9792 21.0115 51.0168 20.9832 51.0499 20.9501L52.7236 19.2764C53.0821 18.9179 52.7407 18.3148 52.2487 18.4378L50.0532 18.9867C50.0179 18.9955 49.9835 19.0082 49.9509 19.0245Z" fill="#CBF4FF"/>
</g>
<g filter="url(#filter2_di_1581_3633)">
<ellipse cx="31.5" cy="30" rx="4.5" ry="5" fill="#CBF4FF"/>
</g>
<defs>
<filter id="filter0_i_1581_3633" x="19" y="60" width="38" height="5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1581_3633"/>
</filter>
<filter id="filter1_di_1581_3633" x="28.2766" y="16.4215" width="28.5957" height="18.937" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.224146 0 0 0 0 0.604424 0 0 0 0 0.876052 0 0 0 0.7 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1581_3633"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1581_3633" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.79 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1581_3633"/>
</filter>
<filter id="filter2_di_1581_3633" x="25" y="23" width="15" height="16" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.224146 0 0 0 0 0.604424 0 0 0 0 0.876052 0 0 0 0.7 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1581_3633"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1581_3633" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.79 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1581_3633"/>
</filter>
<linearGradient id="paint0_linear_1581_3633" x1="38" y1="60" x2="38" y2="65" gradientUnits="userSpaceOnUse">
<stop stop-color="#5AADEE"/>
<stop offset="1" stop-color="#86D7FF"/>
</linearGradient>
<linearGradient id="paint1_linear_1581_3633" x1="53.8519" y1="5.41071" x2="44.1967" y2="54.1021" gradientUnits="userSpaceOnUse">
<stop offset="0.231765" stop-color="#CBF4FF"/>
<stop offset="0.255402" stop-color="#CBF4FF"/>
<stop offset="0.444329" stop-color="#5AADEE"/>
<stop offset="1" stop-color="#5AADEE"/>
</linearGradient>
<linearGradient id="paint2_linear_1581_3633" x1="34.1354" y1="2.55414" x2="34.1354" y2="57" gradientUnits="userSpaceOnUse">
<stop stop-color="#AAEDFF"/>
<stop offset="1" stop-color="#74E1FF"/>
</linearGradient>
</defs>
</svg>
