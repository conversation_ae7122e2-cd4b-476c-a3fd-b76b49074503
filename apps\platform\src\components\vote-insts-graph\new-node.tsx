import type { Node } from '@antv/x6';
import { register } from '@antv/x6-react-shape';
import { Badge, Space } from 'antd';

import { ReactComponent as InstIcon } from '@/assets/inst.icon.svg';
import { getStatus } from '@/modules/message-center/message-info/info-content';

import { StatusTag } from '../status-tag';

import styles from './index.less';

const VoteNode = ({ node }: { node: Node }) => {
  const data = node.getData();
  const { action, isInitiator, instName, nodeName, isOurNode, instId } = data;

  const { statusType, statusText } = getStatus(action);
  return (
    <div className={styles.nodeBoxWrapper} key={instId}>
      <div className={styles.nodeBox}>
        <div className={styles.nodeBoxTitle}>
          <Space style={{ color: '#4762B2' }}>
            <Badge color="#4762B2" />
            <span>{isInitiator ? '发起方' : '受邀方'}</span>
          </Space>
          <StatusTag type={statusType} text={statusText} />
        </div>
        <div className={styles.nodeBoxContent}>
          <div className={styles.nodeBoxContentItem}>
            <span className={styles.nodeId}>{nodeName}</span>
            {isOurNode && <span className={styles.myNode}>(我的)</span>}
          </div>
          <div className={styles.instBox}>
            <Space>
              <InstIcon />
              <span>{instName}</span>
            </Space>
          </div>
        </div>
      </div>
    </div>
  );
};

register({
  shape: 'custom-vote-node',
  width: 204,
  height: 70,
  component: VoteNode,
  effect: ['data'],
  inherit: 'react-shape',
});
