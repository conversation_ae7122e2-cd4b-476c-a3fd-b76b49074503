.createModal {
  :global(.ant-modal-close) {
    top: 24px !important;
  }

  :global(.ant-modal-content) {
    width: 668px;
    height: 100%;
  }
}

.createModalMax {
  :global(.ant-modal-close) {
    top: 24px !important;
  }

  :global(.ant-modal-content) {
    width: 1008px;
    height: 100%;
  }
}

.nodeItem {
  display: flex;
  gap: 20px;
}

.okText {
  display: block;
  width: 294px;
  height: 48px;
  border: 1.88px solid rgb(0 0 0 / 0%);
  border-radius: 6px;
  margin: 0 auto;
  background-color: #9a0000;
  box-shadow: 0 4px 0 0 rgb(5 145 255 / 10%);

  span {
    color: #fff;
    font-size: 20px;
    font-weight: 400;
    letter-spacing: 0;
  }
}

.formLabelItem {
  font-weight: 400;
}

.formBoldLabelItem {
  font-weight: 500;
}

.buttonDisable {
  cursor: not-allowed !important;
  opacity: 0.4;
  pointer-events: none;
}
