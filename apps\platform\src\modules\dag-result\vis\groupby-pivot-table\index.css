.tableTab {
  margin-top: 24px;
}
.tabsTable {
  position: relative;
}
.tabsTable :global .ant-tabs-nav::before {
  border-bottom: none !important;
}
.tabsTable :global .ant-table-container {
  border-radius: 0;
}
.tabsTable :global(.ant-tabs-tab) {
  padding: 0 0 8px !important;
}
.tabsTable :global(.ant-tabs-nav) {
  width: calc(100% - 110px);
  margin-bottom: 8px !important;
}
.palette-legend {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-bottom: 8px;
}
.palette-color {
  width: 12px;
  height: 12px;
}
.palette-limit {
  color: #5e5e5e;
  font-size: 12px;
}
.palette-color + .palette-limit {
  margin-left: 5px;
}
.palette-limit + .palette-color {
  margin-left: 5px;
}
.pivotHeader :global .s2-header-content {
  padding-top: 0;
}
.customBtn {
  color: rgba(0, 10, 26, 0.68);
}
.customBtn:hover {
  color: #9a0000 !important;
}
.switcherButton :global(.antv-s2-switcher-entry-button.ant-btn) {
  position: absolute;
  top: 5px;
}
.switcherPopover :global(.antv-s2-switcher-dimension-items) {
  padding: 0;
  margin: 8px;
}
