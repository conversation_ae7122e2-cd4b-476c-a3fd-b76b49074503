class EventBus {
  // 存储事件和回调的映射
  private events: Map<string, Map<string, Set<(...args: any[]) => void>>>;
  private static instance: EventBus;

  // 私有构造函数，防止外部直接实例化
  private constructor() {
    this.events = new Map();
  }

  // 获取单例实例
  public static getInstance(): EventBus {
    if (!EventBus.instance) {
      EventBus.instance = new EventBus();
    }
    return EventBus.instance;
  }

  public getEvents(): Map<string, Map<string, Set<(...args: any[]) => void>>> {
    return this.events;
  }

  // 订阅事件
  public subscribe(
    cur: string,
    target: string,
    callback: (...args: any[]) => void,
  ): void {
    if (!this.events.has(target)) {
      const map = new Map();
      const set = new Set();
      set.add(callback);
      map.set(cur, set);
      this.events.set(target, map);
    } else if (!this.events.get(target)?.get(cur)) {
      const set2 = new Set<(...args: any[]) => void>();
      set2.add(callback);
      this.events.get(target)?.set(cur, set2);
    } else {
      this.events.get(target)?.get(cur)?.add(callback);
    }
  }

  // 取消订阅事件
  public unsubscribe(target: string, cur: string): void {
    const subscribers = this.events.get(target);
    if (subscribers) {
      subscribers.delete(cur);
      if (subscribers.size === 0) {
        this.events.delete(target);
      }
    }
  }

  // 发布事件
  public publish(target: string, ...args: any[]): void {
    const subscribers = this.events.get(target);
    if (subscribers) {
      const keys = subscribers.keys();
      for (const key of keys) {
        const callbacks = subscribers.get(key);
        if (!callbacks) continue;
        for (const callback of callbacks) {
          callback(...args);
        }
      }
    }
  }
}

export default EventBus;
