{"private": true, "scripts": {"preinstall": "npx only-allow pnpm", "postinstall": "is-ci || npx husky install", "setup": "nx run-many --target=setup", "dev": "nx run-many --target=dev --parallel=100 --exclude demo-*", "lint": "nx run-many --target=lint", "test": "nx run-many --target=test", "ci": "nx affected --target=lint:js,lint:css,lint:format --nx-bail=true", "build": "nx run-many --target=build --nx-bail=true", "serve": "nx run-many --target=serve", "fix": "nx run-many --target=lint:js,lint:css -- --fix; nx run-many --target=lint:format -- --write", "format-all": "prettier --write '**/src/**/*.{html,js,jsx,ts,tsx,css,less,json}' **/*.md", "bootstrap": "pnpm install && pnpm run setup", "clean": "pnpm -s dlx rimraf -g './node_modules' '**/node_modules' './apps/*/dist' './packages/*/dist'"}, "devDependencies": {"@commitlint/cli": "^17.4.4", "@commitlint/config-conventional": "^17.4.4", "@secretflow/config-eslint": "workspace:^", "@secretflow/config-stylelint": "workspace:^", "@umijs/plugins": "^4.3.27", "dotenv-cli": "^7.1.0", "eslint": "^8.35.0", "is-ci": "^3.0.1", "jest": "^29.5.0", "lint-staged": "^13.1.2", "nx": "^15.8.6", "prettier": "^2.8.4", "stylelint": "^15.2.0", "tsup": "^6.7.0", "typescript": "^4.9.5"}, "engines": {"node": ">=16.14.0"}, "packageManager": "pnpm@8.8.0", "dependencies": {"@ant-design/icons": "^5.0.1", "jschardet": "^3.0.0", "papaparse": "^5.4.1", "react-csv": "^2.2.2", "umi-request": "^1.4.0"}}