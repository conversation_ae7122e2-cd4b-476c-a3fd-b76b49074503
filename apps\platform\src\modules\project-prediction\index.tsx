import { SearchOutlined } from '@ant-design/icons';
import type { TableColumnsType } from 'antd';
import {
  Button,
  Flex,
  Input,
  message,
  Popconfirm,
  Select,
  Space,
  Typography,
} from 'antd';
import { debounce } from 'lodash';
import { parse, stringify } from 'query-string';
import { useEffect, type ChangeEvent } from 'react';
import { history, useLocation } from 'umi';

import CommonTable from '@/components/common-table';
import { StatusTag } from '@/components/status-tag';
import { TabSearchBox } from '@/components/tab-search-box';
import {
  modelPackDetail,
  modelPackPage,
} from '@/services/secretpad/ModelManagementController';
import { listP2PProject } from '@/services/secretpad/P2PProjectController';
import { getModel, Model, useModel } from '@/util/valtio-helper';

import type { AllocatedNode } from '../project-psi/project-psi.service';

import { CreatePredictionModal } from './components/create-prediction';
import { PredictionDetailDrawer } from './components/prediction-detail';
import style from './index.less';
import { ProjectPredictionService } from './project-prediction.service';

const SelectOptions = [
  { label: '全部状态', value: 'all' },
  { label: '运行中', value: 'RUNNING' },
  { label: '成功', value: 'SUCCEED' },
  { label: '失败', value: 'FAILED' },
  { label: '待审批', value: 'PENDING_APPROVAL' },
  { label: '已拒绝', value: 'REJECTED' },
  { label: '已取消', value: 'CANCELED' },
];

export const TaskStatusLabel = (props: { status: string }) => {
  switch (props.status.toLocaleLowerCase()) {
    case 'succeed':
      return <StatusTag type="success" text="成功" />;
    case 'failed':
      return <StatusTag type="failed" text="失败" />;
    case 'pending_approval':
      return <StatusTag type="warning" text="待审批" />;
    case 'rejected':
      return <StatusTag type="failed" text="已拒绝" />;
    case 'canceled':
      return <StatusTag type="failed" text="已取消" />;
    case 'paused':
      return <StatusTag type="warning" text="已暂停" />;
    case 'running':
      return <StatusTag type="primary" text="运行中" />;
    case 'fi_psi_completed':
      return <StatusTag type="primary" text="隐私求交完成" />;
    default:
      return <StatusTag type="primary" text="运行中" />;
  }
};

const canDeleteStatus = ['FAILED', 'SUCCEED', 'CANCELED', 'REJECTED'];
const checkCanCancel = (
  record: API2.FIJobListVO,
  ownerId: string,
  parties: AllocatedNode[],
) => {
  return (
    (record.status === 'PENDING_APPROVAL' &&
      ownerId === parties.find((item) => item.nodeId === record.initiator)?.instId) ||
    ['PAUSED', 'RUNNING'].includes(record.status)
  );
};

export const getCurrentNodeId = (parties: AllocatedNode[], ownerId: string) => {
  return parties.find((item) => item.instId === ownerId)?.nodeId;
};

let timer: NodeJS.Timeout | undefined = undefined;

const PredictionComponent = () => {
  const model = useModel(PredictionModel);
  const { search } = useLocation();
  const parsedSearch = parse(search);

  const { ownerId } = parsedSearch as { tab?: string; ownerId?: string };
  const toMessageCenter = () => {
    history.push({
      pathname: '/edge/messages',
      search: stringify({
        ownerId,
        active: 'process',
      }),
    });
    // history.push({
    //   pathname,
    //   search: stringify({
    //     ownerId,
    //     tab: 'message-center',
    //     active: 'process',
    //   }),
    // });
  };
  const columns: TableColumnsType = [
    {
      title: '任务名称',
      dataIndex: 'jobName',
      key: 'jobName',
      width: 200,
    },
    {
      title: '模型来源',
      dataIndex: 'sourceProjectName',
      key: 'sourceProjectName',
      width: 160,
    },
    {
      title: '模型名称',
      dataIndex: 'modelName',
      key: 'modelName',
      width: 160,
    },
    {
      title: '发起机构',
      dataIndex: 'initiator',
      key: 'initiator',
      width: 160,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 160,
      render: (status) => <TaskStatusLabel status={status} />,
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 200,
      render: (_, record) => (
        <Space size="middle">
          {record.status === 'PENDING_APPROVAL' &&
            record.partyVoteInfos.find((item: any) => item.action === 'REVIEWING')
              ?.partyId === ownerId && (
              <Typography.Link onClick={() => toMessageCenter()}>
                去审批
              </Typography.Link>
            )}
          <Typography.Link
            onClick={() => {
              model.currentPrediction = {
                ...record,
                currentNodeId: getCurrentNodeId(model.parties, ownerId as string),
              } as API2.FIJobListVO & { currentNodeId: string };
              localStorage.setItem(
                'currentPredictionItem',
                JSON.stringify(model.currentPrediction),
              );
              // model.showDetailDrawer = true;
              history.push(`/edge/prediction/${record.jobId}?ownerId=${ownerId}`);
            }}
          >
            详情
          </Typography.Link>
          {checkCanCancel(
            record as API2.FIJobListVO,
            ownerId as string,
            model.parties,
          ) && (
            <Popconfirm
              title="提示"
              description="确定取消任务吗？"
              onConfirm={() => {
                model.cancelJob({ jobId: record.jobId, nodeId: record.initiator });
              }}
              okText="确定"
              cancelText="取消"
            >
              <Typography.Link>取消</Typography.Link>
            </Popconfirm>
          )}
          {/* 只有发起方才能下载 */}
          {record.status === 'SUCCEED' &&
            getCurrentNodeId(model.parties, ownerId as string) === record.initiator && (
              <Typography.Link
                onClick={() =>
                  model.download(record.jobId, record.domainDataId, ownerId as string)
                }
              >
                结果下载
              </Typography.Link>
            )}
          {canDeleteStatus.includes(record.status) &&
            getCurrentNodeId(model.parties, ownerId || '') === record.initiator && (
              <Popconfirm
                title="提示"
                description="确定删除任务吗？"
                onConfirm={() => {
                  model.deleteJob({ jobId: record.jobId, nodeId: record.initiator });
                }}
                okText="确定"
                cancelText="取消"
              >
                <Typography.Link>删除</Typography.Link>
              </Popconfirm>
            )}
        </Space>
      ),
    },
  ];

  const tabs = [
    { label: '全部', value: 'all' },
    { label: '我发起', value: 'apply' },
    { label: '我受邀', value: 'invited' },
  ];

  useEffect(() => {
    model.getParties().then(() => {
      getCurrentNodeId(model.parties, ownerId as string);
    });
    model.getTable();
  }, []);

  useEffect(() => {
    if (
      model.tableList.some(
        (item) => item.status === 'RUNNING' || item.status === 'FI_PSI_COMPLETED',
      ) &&
      !timer
    ) {
      timer = setInterval(() => {
        if (
          !model.tableList.some(
            (item) => item.status === 'RUNNING' || item.status === 'FI_PSI_COMPLETED',
          )
        ) {
          clearInterval(timer);
          timer = undefined;
          return;
        }
        model.getJobStatus({
          jobIds: model.tableList
            .filter(
              (item) => item.status === 'RUNNING' || item.status === 'FI_PSI_COMPLETED',
            )
            .map((item) => item.jobId),
        });
      }, 5000);

      return () => {
        clearInterval(timer);
        timer = undefined;
      };
    }
  }, [model.tableList]);

  return (
    <div className={style.predictionWrapper}>
      <TabSearchBox tabs={tabs} onTabChange={(value) => model.onRadioChange(value)}>
        <Space size="middle">
          <span>任务名称</span>
          <Input
            placeholder="搜索任务"
            onChange={(e) => model.onInputSearch(e)}
            style={{ width: 200 }}
            value={model.searchText}
            suffix={
              <SearchOutlined
                style={{
                  color: '#aaa',
                }}
              />
            }
          />
          {/* <Radio.Group
            options={RadioOptions}
            value={model.searchType}
            optionType="button"
            onChange={(e) => model.onRadioChange(e)}
          /> */}
          <span>状态</span>
          <Select
            style={{ width: 180 }}
            value={model.searchStatus}
            options={SelectOptions}
            onChange={(e) => model.onSelectChange(e)}
          />
        </Space>
      </TabSearchBox>
      <div className={style.content}>
        <Flex justify="end" style={{ marginBottom: 16 }}>
          <Button type="primary" onClick={() => (model.showCreateDrawer = true)}>
            新建任务
          </Button>
        </Flex>
        <CommonTable
          columns={columns}
          dataSource={model.tableList}
          loading={model.tableLoading}
          xScroll
          rowKey="jobId"
          pagination={{
            total: model.total,
            current: model.pageNum,
            pageSize: model.pageSize,
            showTotal: (total: number) => `共 ${total} 条`,
            showSizeChanger: true,
          }}
          onChange={(pagination) => {
            model.pageNum = pagination.current || 1;
            model.pageSize = pagination.pageSize || 10;
            model.getTable();
          }}
        />
      </div>
      <CreatePredictionModal
        open={model.showCreateDrawer}
        onClose={() => (model.showCreateDrawer = false)}
        onConfirm={(data) => {
          model.createJob(data);
        }}
      />
      <PredictionDetailDrawer
        open={model.showDetailDrawer}
        data={model.currentPrediction as API2.FIJobListVO & { currentNodeId: string }}
        onClose={() => (model.showDetailDrawer = false)}
      />
    </div>
  );
};

const defaultResourceConfig = [
  {
    minCPU: '0',
    maxCPU: '0',
    minMemory: '0Gi',
    maxMemory: '0Gi',
  },
];

export class PredictionModel extends Model {
  searchText = '';
  searchType = 'all';
  searchStatus = 'all';
  showCreateDrawer = false;
  showDetailDrawer = false;
  currentPrediction: API2.FIJobListVO | null = null;
  tableLoading = false;
  pageNum = 1;
  pageSize = 10;
  total = 0;

  parties: AllocatedNode[] = [];
  service = getModel(ProjectPredictionService);
  tableList: API2.FIJobListVO[] = [];

  modelList: API.ModelPackVO[] = [];
  modelDetail: API.ModelPackDetailVO | undefined;
  createLoading = false;
  datasetDisabled = true;

  constructor() {
    super();
  }

  resetPagination() {
    this.pageNum = 1;
  }

  async getTable() {
    this.tableLoading = true;
    try {
      const res = await this.service.getPredictionList({
        page: this.pageNum,
        pageSize: this.pageSize,
        taskName: this.searchText,
        status: this.searchStatus === 'all' ? '' : this.searchStatus,
        partner: this.searchType === 'invited' ? 1 : 0,
        initiator: this.searchType === 'apply' ? 1 : 0,
      });
      if (res) {
        this.total = res.total || 0;
        this.tableList = res.fiJobs || [];
      }
    } finally {
      this.tableLoading = false;
    }
  }

  onInputSearch(e: ChangeEvent<HTMLInputElement>) {
    this.searchText = e.target.value;
    console.log(this.searchText);
    this.debouceSearch();
  }

  debouceSearch = debounce(this.getTable, 300);

  onRadioChange(value: string) {
    this.searchType = value;
    this.pageNum = 1;
    this.getTable();
  }

  onSelectChange(value: string) {
    this.searchStatus = value;
    this.pageNum = 1;
    this.getTable();
  }

  async getParties() {
    const res = await this.service.getAllocatedNodes();
    this.parties = res;
  }

  async getProjectList() {
    const res = await listP2PProject();
    return res.data?.filter((item) => item.status === 'APPROVED') || [];
  }

  async getProjectModelList(projectId: string) {
    const res = await modelPackPage({
      projectId,
      page: 1,
      size: 1000,
    });
    this.modelList = res.data?.modelPacks || [];
  }

  async getModelDetail(modelId: string, projectId: string) {
    const res = await modelPackDetail({
      modelId,
      projectId,
    });
    this.modelDetail = res.data;
    return res.data;
  }

  async createApproval(data: { jobId: string; parties: AllocatedNode[] }) {
    const parties = data.parties;
    const initiatorId = parties[0].instId;
    const partnerId = parties[1].instId;
    await this.service.createFIApproval({
      initiatorId,
      voteType: 'COMMON_JOB_CREATE',
      voteConfig: {
        projectId: 'FI',
        jobId: data.jobId,
        participants: [initiatorId, partnerId],
        participantNodeInstVOS: [
          {
            initiatorNodeId: data.parties[0].nodeId,
            invitees: [
              {
                inviteeId: data.parties[1].nodeId,
              },
            ],
          },
        ],
      },
    });
  }

  async createJob(data: Record<string, any>) {
    this.createLoading = true;
    const partyConfigs: API2.CreateFiProjectJobRequest['partyConfigs'] = [
      {
        nodeId: data.datasets1.nodeId,
        features: data.datasets1.features,
        joinId: data.datasets1.indexId,
        resources: defaultResourceConfig,
      },
      {
        nodeId: data.datasets2.nodeId,
        features: data.datasets2.features,
        joinId: data.datasets2.indexId,
        resources: defaultResourceConfig,
      },
    ];

    const requestData: API2.CreateFiProjectJobRequest = {
      name: data.name,
      description: data.description,
      modelId: data.modelId,
      projectId: data.projectId,
      initiatorNodeId: data.datasets1.nodeId,
      initiatorTableId: data.datasets1.datatableId,
      initiatorModel: data.modelId,
      partnerNodeId: data.datasets2.nodeId,
      partnerTableId: data.datasets2.datatableId,
      sourceProjectId: data.projectId,
      initiatorKeyColumns: [data.datasets1.indexId],
      partnerKeyColumns: [data.datasets2.indexId],
      partyConfigs,
      receiverParties: [data.datasets1.nodeId, data.datasets2.nodeId],
      resultColumns: data.resultColumns,
    };
    const res = await this.service.createFIJob(requestData);
    if (res.status?.code === 0) {
      await this.createApproval({
        jobId: res.data?.jobId || '',
        parties: [
          this.parties.find((item) => item.nodeId === data.datasets1.nodeId)!,
          this.parties.find((item) => item.nodeId === data.datasets2.nodeId)!,
        ],
      });
      this.getTable();
      this.showCreateDrawer = false;
    } else {
      message.error(res.status?.msg || '联合预测任务创建失败');
    }
    this.createLoading = false;
  }

  async getJobStatus(data: { jobIds: string[] }) {
    const res = await this.service.checkPredictionJobStatus(data);
    if (res.status?.code === 0) {
      console.log(res.data, 'res');
      const statusList = res.data?.taskStatusList || [];
      const tempTableList = [...this.tableList];
      statusList.forEach((item) => {
        const job = tempTableList.find((table) => table.jobId === item.jobId);
        if (job) {
          job.status = item.status;
          job.domainDataId = item.domainDataId;
        }
      });
      this.tableList = tempTableList;
    }
  }

  async deleteJob(data: { jobId: string; nodeId: string }) {
    const res = await this.service.deletePredictionJob(data);
    if (res) {
      this.getTable();
    }
  }

  async cancelJob(data: { jobId: string; nodeId: string }) {
    const res = await this.service.cancelPredictionJob(data);
    if (res) {
      this.getTable();
    }
  }

  async getJobLogs(data: { jobId: string; nodeId: string }) {
    const res = await this.service.getPredictionJobLogs(data);
    return res;
  }

  async download(jobId: string, domainDataId: string, ownerId: string) {
    const nodeId = this.parties.find((party) => party.instId === ownerId)?.nodeId || '';
    await this.service.download({
      jobId,
      nodeId,
      domainDataId,
    });
  }
}

export default PredictionComponent;
