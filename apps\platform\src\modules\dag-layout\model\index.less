.wrap {
  overflow: hidden;
  width: 100%;
  height: 100%;
}

.content {
  display: flex;
  width: 100%;
  height: calc(100% - 49px);
  box-sizing: border-box;

  .center {
    position: relative;
    width: 100%;
    height: 100%;

    .header {
      display: flex;
      width: 100%;
      height: 50px;
      box-sizing: border-box;
      align-items: center;
      justify-content: space-between;
      padding: 0 16px;
      background-color: #f6f8fa;

      .right {
        display: flex;
        height: 100%;
        align-items: center;
        padding-left: 320px;

        :global(.ant-btn-link) {
          color: rgb(0 0 0 / 88%);
          font-size: 12px;
        }
      }

      .left {
        display: flex;
        align-items: center;
        color: rgb(0 0 0 / 85%);
        font-size: 14px;
      }
    }

    .graph {
      width: 100%;
      height: calc(100% - 42px);
    }

    .graphContent {
      width: calc(100% - 360px) !important;
    }

    .toolbutton {
      position: absolute;
      right: 20px;
      bottom: 36px;
    }
  }

  .alert {
    height: 26px;
    margin-left: 12px;
    font-size: 12px;
  }
}
