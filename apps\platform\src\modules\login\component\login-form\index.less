@import url('@/variables.less');

.loginForm {
  width: 380px;
  box-sizing: border-box;
  padding: 48px 30px;
  border-radius: 16px;
  margin: auto;
  background: #f8faff;
  box-shadow: -1px 8px 16px 0 rgb(71 98 178 / 20%);

  .title {
    margin-top: 0;
    margin-bottom: 56px;
    color: @PrimaryColor;
    font-size: 24px;
  }

  .loginInput {
    max-width: 400px;

    :global(.ant-input-prefix > *:not(:last-child)) {
      margin-inline-end: 0;
    }
  }

  :global(.ant-form-item .ant-form-item-additional) {
    text-align: left;
  }

  .loginBtnItem {
    max-width: 400px;
    margin-top: 56px;
  }

  .loginBtn {
    width: 100%;
  }
}
