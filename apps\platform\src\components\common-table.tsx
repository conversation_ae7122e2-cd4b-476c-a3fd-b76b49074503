import type { TableProps } from 'antd';
import { Table } from 'antd';
import { useEffect, useMemo, useRef, useState } from 'react';

const CommonTable = (
  props: TableProps & { xScroll?: boolean; hideScroll?: boolean },
) => {
  const tableWrapperRef = useRef<HTMLDivElement>(null);

  const [height, setHeight] = useState(0);
  const { xScroll, hideScroll } = props;
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (tableWrapperRef.current) {
      timer = setTimeout(() => {
        const rect = tableWrapperRef.current!.getBoundingClientRect();
        const bodyHeight = document.body.clientHeight;
        const paginationHeight = props.pagination ? 60 : 0;
        // 减掉header高度和pagination高度
        setHeight(bodyHeight - rect.top - 60 - paginationHeight);
      }, 10);
    }
    return () => {
      if (timer) {
        clearTimeout(timer);
      }
    };
  }, []);

  const scroll = useMemo(() => {
    if (hideScroll) return undefined;
    return {
      x: xScroll ? 'max-content' : undefined,
      y: height,
    };
  }, [height, xScroll]);
  return (
    <div ref={tableWrapperRef}>
      {height !== 0 && <Table size="middle" scroll={scroll} {...props} />}
    </div>
  );
};

export default CommonTable;
