.predictionWrapper {
  display: flex;
  height: 100%;
  box-sizing: border-box;
  flex-direction: column;
  padding: 20px;
}

.content {
  box-sizing: border-box;
  flex: 1;
  padding: 20px 20px 0;
  border-radius: 8px;
  background-color: #fff;
}

.predictionDataset {
  position: relative;
  padding: 16px;
  border-radius: 4px;
  margin-bottom: 16px;
  background-color: #f6f6f6;
}

.datasetItem {
  display: flex;
  align-items: center;
  margin-bottom: 10px;

  .datasetItemLabel {
    width: 60px;
    flex-shrink: 0;
    margin-right: 8px;
  }
}

.datasetDeleteIcon {
  position: absolute;
  top: 10px;
  right: 10px;
  color: #ff4d4f;
  cursor: pointer;
}

.datasetDetail,
.logDetail {
  :global(.ant-descriptions-item-content) {
    display: block !important;
  }
}

.fiProcessSteps {
  :global(.ant-steps-item-finish .ant-steps-item-icon) {
    border-color: #e6f4ff;
    background-color: #e6f4ff;
  }
}

.datasetPreviewItem {
  display: flex;
  align-items: center;
}

.dataCell {
  display: flex;
  flex: 1;
  margin-bottom: 12px;

  .cellTitle {
    margin-right: 8px;
  }
}

.logWrapper {
  position: relative;

  .toggle {
    position: absolute;
    top: -40px;
    left: 80px;
    color: #9a0000;
    cursor: pointer;
  }
}

.fieldBar {
  height: 32px;
  background-color: #e6e6e6;
  color: rgb(0 0 0 / 65%);
  cursor: pointer;
  line-height: 32px;
  text-align: center;
}

.featureTableHeader {
  display: flex;
  width: 100%;
  height: 36px;
  box-sizing: border-box;
  padding: 8px 0;
  background: rgb(0 0 0 / 2%);
  box-shadow: inset 0 -1px 0 0 #e8e9ea;
  color: rgb(0 0 0 / 85%);
  font-size: 12px;
  font-weight: 500;
  line-height: 20px;

  .headerNode {
    width: 28%;
    border-right: 1px solid #e8e9ea;
    margin-left: 32px;
  }

  .headerDatatable {
    width: 32%;
    padding-left: 12px;
    border-right: 1px solid #e8e9ea;
  }

  .headerIndex {
    width: 28%;
    padding-left: 12px;
    border-right: 1px solid #e8e9ea;
  }

  .headerOptions {
    width: 12%;
    padding-left: 12px;
  }
}

.featureTableBody {
  display: flex;
  width: 100%;
  align-items: center;
  padding: 8px 0;

  .bodyNode {
    width: 25%;
    margin-left: 12px;
  }

  .bodyDatatable {
    width: 30%;
    margin-left: 12px;
  }

  .bodyIndex {
    width: 25%;
    margin: 0 12px;
  }
}
