import {
  BulbFilled,
  CodeFilled,
  DatabaseFilled,
  FundFilled,
  LayoutFilled,
  PieChartFilled,
} from '@ant-design/icons';

import { ReactComponent as DefaultIcon } from '@/assets/model-data.svg';
import { ReactComponent as EvalIcon } from '@/assets/model-eval.svg';
import { ReactComponent as FeatureIcon } from '@/assets/model-feature.svg';
import { ReactComponent as FilterIcon } from '@/assets/model-filter.svg';
import { ReactComponent as PreprocessingIcon } from '@/assets/model-pre.svg';
import { ReactComponent as StatsIcon } from '@/assets/model-stats.svg';
import { ReactComponent as TrainIcon } from '@/assets/model-train.svg';
import { ReactComponent as PredictIcon } from '@/assets/model.svg';

export const ComponentIcons: Record<string, React.ReactElement> = {
  default: <DefaultIcon style={{ color: '#A1AABC' }} />,
  preprocessing: <FeatureIcon style={{ color: '#A1AABC' }} />,
  data_filter: <FilterIcon style={{ color: '#A1AABC' }} />,
  stats: <StatsIcon style={{ color: '#A1AABC' }} />,
  'ml.predict': <PreprocessingIcon style={{ color: '#A1AABC' }} />,
  feature: <FeatureIcon style={{ color: '#A1AABC' }} />,
  control: <CodeFilled style={{ color: '#A1AABC' }} />,
  'ml.train': <TrainIcon style={{ color: '#A1AABC' }} />,
  'ml.eval': <EvalIcon style={{ color: '#A1AABC' }} />,
};
