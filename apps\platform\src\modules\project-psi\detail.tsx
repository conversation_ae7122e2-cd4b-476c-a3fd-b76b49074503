import type { DescriptionsProps } from 'antd';
import { Descriptions } from 'antd';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { parse } from 'query-string';
import { useEffect, useState } from 'react';
import { useLocation } from 'umi';

import { useModel } from '@/util/valtio-helper';

import { TaskStatusLabel } from '../project-prediction';
import { DetailBox } from '../project-prediction/components/detail-box';

import { LogList } from './components/psi-detail';
import styles from './detail.less';

import { PSIModel } from '.';

const fieldMap = {
  'input/input_ds1/keys': '发起方数据',
  'input/input_ds2/keys': '合作方数据',
  protocol: '求交协议',
  'protocol/PROTOCOL_ECDH': 'PROTOCOL_ECDH曲线',
  sort_result: '是否重排序',
  receiver_parties: '结果接收方',
  allow_empty_result: '求交结果是否允许为空',
  join_type: '求交类型',
  'join_type/left_join/left_side': '左表数据方',
  input_ds1_keys_duplicated: '表一关联键重复',
  input_ds2_keys_duplicated: '表二关联键重复',
};

function getFieldData(data: Record<string, any>) {
  if (data.b !== undefined) {
    return data.b ? '是' : '否';
  } else if (data.s !== undefined) {
    return data.s;
  } else if (data.ss !== undefined) {
    return data.ss.join(',');
  }
}

const PredictionDetail = () => {
  const model = useModel(PSIModel);
  const descData =
    model.currentTask ||
    JSON.parse(localStorage.getItem('currentPSIDetailItem') || '{}');

  const { attrs, attrPaths } = JSON.parse(descData.nodeDef || '{}');

  const paramsInfos = attrPaths?.map((item: any, index: number) => {
    let info: any;
    const primaryInst = descData.insts.find(
      (item: any) => item.instId === descData.initiator,
    )?.instName;
    const partnerInst = descData.insts.find(
      (item: any) => item.instId !== descData.initiator,
    )?.instName;
    if (item === 'input/input_ds1/keys') {
      info = (
        <div>
          <div>
            <span className={styles.label}>机构：</span>
            {primaryInst}
          </div>
          <div>
            <span className={styles.label}>表名：</span>
            {descData.initiatorDatatableName}
          </div>
          <div>
            <span className={styles.label}>ID列：</span>
            {getFieldData(attrs[index])}
          </div>
        </div>
      );
    } else if (item === 'input/input_ds2/keys') {
      info = (
        <div>
          <div>
            <span className={styles.label}>机构：</span>
            {partnerInst}
          </div>
          <div>
            <span className={styles.label}>表名：</span>
            {descData.partnerDatatableName}
          </div>
          <div>
            <span className={styles.label}>ID列：</span>
            {getFieldData(attrs[index])}
          </div>
        </div>
      );
    } else {
      info = getFieldData(attrs[index]);
    }
    if (item === 'input/input_ds1/keys' || item === 'input/input_ds2/keys') {
      return {
        key: item as string,
        label: fieldMap[item as keyof typeof fieldMap],
        children: <div className={styles.paramsWrapper}>{info}</div>,
      };
    }
    return {
      key: item as string,
      label: fieldMap[item as keyof typeof fieldMap],
      children: <div style={{ whiteSpace: 'pre-wrap' }}>{info}</div>,
    };
  });

  const descriptionInfos: DescriptionsProps['items'] = [
    {
      key: 'jobName',
      label: '任务名称',
      children: descData.jobName,
    },
    {
      key: 'description',
      label: '任务描述',
      children: descData.description || '-',
    },
    {
      key: 'initiatorName',
      label: '发起机构',
      children: descData.initiatorName,
    },
    {
      key: 'status',
      label: '状态',
      className: styles.taskStatus,
      children: <TaskStatusLabel status={descData.status} />,
    },
    {
      key: 'createTime',
      label: '提交时间',
      children: dayjs(descData.createTime).add(8, 'hour').format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      key: 'startTime',
      label: '任务开始时间',
      children: descData.startTime
        ? dayjs(descData.startTime).format('YYYY-MM-DD HH:mm:ss')
        : '-',
    },
    {
      key: 'finishedTime',
      label: '任务完成时间',
      children: descData.finishedTime
        ? dayjs(descData.finishedTime).format('YYYY-MM-DD HH:mm:ss')
        : '-',
    },
  ];

  const { search, pathname } = useLocation();
  const isMessageCenter = pathname.includes('/edge/messages');
  const parsedSearch = parse(search);

  const { ownerId } = parsedSearch as { ownerId: string };

  const [logsList, setLogsList] = useState<API2.PSILogsResponse['data']>([]);
  useEffect(() => {
    if (!isMessageCenter) {
      model
        .getLogs({
          jobId: descData.jobId,
          nodeId: model.parties.find((party) => party.instId === ownerId)?.nodeId || '',
        })
        .then((res) => {
          setLogsList(res);
        });
    }
  }, []);

  return (
    <div
      className={classNames(styles.psiDetail, {
        [styles.detailInMessage]: isMessageCenter,
      })}
    >
      <DetailBox title="基本信息">
        <Descriptions column={3} items={descriptionInfos} />
      </DetailBox>
      <DetailBox title="任务参数" showCollapse collapseHeight={0}>
        <Descriptions
          styles={{ label: { width: 120 } }}
          column={1}
          items={paramsInfos}
        />
      </DetailBox>
      {!isMessageCenter && (
        <DetailBox title="执行流程">
          <div className={styles.logWrapper}>
            <LogList
              type="psi"
              logsList={logsList}
              jobId={descData.jobId}
              nodeId={
                model.parties.find((party) => party.instId === ownerId)?.nodeId || ''
              }
            />
          </div>
        </DetailBox>
      )}
    </div>
  );
};

export default PredictionDetail;
