.formLabelItem {
  font-weight: 400;
}
.formBoldLabelItem {
  font-weight: 500;
}
.projectTipsOptions {
  color: rgba(0, 0, 0, 0.45);
}
.buttonDisable {
  cursor: not-allowed !important;
  opacity: 0.4;
  pointer-events: none;
}
.nodeVotersContent {
  position: relative;
  width: 620px;
  padding: 16px 12px;
  padding-bottom: 0;
  border-radius: 2px;
  margin-bottom: 16px;
  background: #00000005;
}
.nodeVotersContent :global(.ant-form-item) {
  margin-bottom: 8px;
}
.nodeVotersContent .addTag {
  justify-content: flex-start !important;
  padding: 0;
  padding-top: 0 !important;
  padding-left: 0 !important;
  background: transparent;
}
.nodeInfoformLabel {
  margin-bottom: 16px;
  font-weight: 400;
}
.deleteBtn {
  position: absolute;
  top: 24px;
  right: 16px;
  color: rgba(0, 0, 0, 0.45);
}
.deleteBtn:hover {
  color: #9a0000;
}
