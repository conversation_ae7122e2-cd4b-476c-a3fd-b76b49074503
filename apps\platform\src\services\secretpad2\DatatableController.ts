/* eslint-disable */
import request from 'umi-request';

/** 此处后端没有提供注释 POST /api/v1alpha1/datatable/create */
export async function createDataTable(
  body: API2.CreateDatatableRequest,
  options?: { [key: string]: any },
) {
  return request<API2.SecretPadResponseCreateDatatableVO>(
    '/api/v1alpha1/datatable/create',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 此处后端没有提供注释 POST /api/v1alpha1/datatable/delete */
export async function deleteDatatable(
  body: API2.DeleteDatatableRequest,
  options?: { [key: string]: any },
) {
  return request<API2.SecretPadResponse>('/api/v1alpha1/datatable/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /api/v1alpha1/datatable/get */
export async function getDatatable(
  body: API2.GetDatatableRequest,
  options?: { [key: string]: any },
) {
  return request<API2.SecretPadResponseDatatableNodeVO>('/api/v1alpha1/datatable/get', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询被授权的数据表列表 查询当前节点被其他节点授权的PSI数据表列表 POST /api/v1alpha1/datatable/granted/list */
export async function listGrantedDatatables(
  body: API2.ListGrantedDatatableRequest,
  options?: { [key: string]: any },
) {
  return request<API2.SecretPadPageResponseGrantedDatatableList>(
    '/api/v1alpha1/datatable/granted/list',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 此处后端没有提供注释 POST /api/v1alpha1/datatable/list */
export async function listDatatables(
  body: API2.ListDatatableRequest,
  options?: { [key: string]: any },
) {
  return request<API2.SecretPadResponseAllDatatableListVO>(
    '/api/v1alpha1/datatable/list',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}
