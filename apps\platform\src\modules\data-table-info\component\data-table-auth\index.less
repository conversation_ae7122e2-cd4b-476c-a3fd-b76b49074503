.authLarge {
  :global {
    .ant-descriptions {
      padding: 8px 3px !important;
    }
  }

  .authListItem {
    padding: 8px 0 !important;
  }
}

.auth {
  :global {
    a {
      user-select: none;
    }

    .ant-descriptions-view {
      padding: 3px;
      margin-bottom: 16px;
      background-color: #fafafa;
    }

    .ant-tag {
      background-color: white;
    }

    .ant-descriptions .ant-descriptions-row > th {
      padding: 0;
      padding-bottom: 0;
    }

    .ant-descriptions-item {
      padding: 0 !important;
    }

    .ant-descriptions-item-content {
      display: flex;
      align-items: center;
      color: rgb(0 0 0 / 88%);
      font-size: 12px;

      .ant-divider {
        margin-right: 10px;
      }
    }
  }

  .authList {
    :global {
      .ant-space {
        padding: 8px 0;
      }
    }
  }

  .authListItem {
    padding: 3px 0;
    border-bottom: 1px solid #eee;
  }

  .authItem {
    display: flex;
    overflow: hidden;
    box-sizing: border-box;
    align-items: center;
    padding: 0 10px;
    text-overflow: ellipsis;
    white-space: nowrap;

    :global {
      .ant-typography {
        font-size: 12px;
      }

      .ant-select-selection-placeholder {
        font-size: 12px;
      }
    }
  }

  .authItemText {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.popup {
  width: 240px !important;

  :global {
    .ant-tag {
      background-color: white;
    }
  }

  .padModeWrapper {
    padding: 5px 0;

    :global {
      .ant-select-item-option-content {
        white-space: break-spaces;
      }
    }
  }
}

.labelKey {
  :global(.ant-descriptions-item-content) {
    align-items: center !important;
  }
}
