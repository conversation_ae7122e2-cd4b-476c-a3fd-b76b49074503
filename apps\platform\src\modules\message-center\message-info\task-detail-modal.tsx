import { AutoModal } from '@/components/auto-modal';
import PredictionDetail from '@/modules/project-prediction/detail';
import PSIProjectDetail from '@/modules/project-psi/detail';

const DetailComponent = (props: { projectType: string }) => {
  if (props.projectType === 'PSI') {
    return <PSIProjectDetail />;
  } else {
    return <PredictionDetail />;
  }
};

export const TaskDetailModal = (props: {
  jobId: string;
  projectType: string;
  show: boolean;
  onClose: () => void;
}) => {
  return (
    <AutoModal
      title="任务详情"
      width={1000}
      mask={false}
      open={props.show}
      onCancel={props.onClose}
      footer={null}
    >
      <DetailComponent projectType={props.projectType} />
    </AutoModal>
  );
};
