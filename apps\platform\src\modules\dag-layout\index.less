.wrap {
  overflow: hidden;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 20px;

  .contentWrapper {
    overflow: hidden;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    border: 1px solid rgb(0 0 0 / 8%);
    border-radius: 8px;
  }

  .header {
    position: relative;
    display: flex;
    height: 40px;
    align-items: center;
    justify-content: space-between;
    padding: 0 12px;
    border-radius: 8px 8px 0 0;
    margin-bottom: 9px;
    background-color: #fff;

    .projectName {
      font-size: 16px;
      font-weight: 700;
    }

    .headerTabs {
      position: absolute;
      left: 50%;
      display: flex;
      align-items: center;
      transform: translateX(-50%);

      .divMenu {
        position: relative;
        padding: 10px 0;
        margin: 0 30px;
        cursor: pointer;
        line-height: 20px;

        &.active {
          color: #4762b2;
          font-weight: 700;

          &::after {
            position: absolute;
            bottom: 0;
            left: 50%;
            display: block;
            width: 20px;
            height: 2px;
            background-color: #4762b2;
            content: '';
            transform: translateX(-50%);
          }
        }

        &.menuDisabled {
          cursor: not-allowed;
          opacity: 0.5;
        }
      }
    }

    .headerRight {
      display: flex;
      align-items: center;
    }
  }
}

.content {
  position: relative;
  display: flex;
  width: 100%;
  height: calc(100% - 50px);
  box-sizing: border-box;

  .left {
    display: flex;
    width: 240px;
    height: 100%;
    flex-direction: column;
    border-radius: 8px;
    background-color: #fff;

    &.hide {
      display: none;
    }

    .leftTop {
      width: 100%;
      height: 305px;
    }

    .leftBottom {
      display: flex;
      overflow: hidden;
      flex: 1;
      justify-content: center;
    }

    :global {
      .ant-tabs-nav {
        display: block;
        width: 240px;
        margin: 0;

        .ant-tabs-ink-bar-animated {
          transition: none;
        }
      }

      .ant-tabs-nav-wrap {
        height: 40px;
        box-sizing: content-box;
        border-bottom: 1.5px solid #eaebed;
        margin: 0 8px;
      }

      .ant-tabs-content {
        height: 100%;
      }

      .ant-tabs-tabpane {
        height: 100%;
      }

      .ant-tabs {
        height: 100%;
      }

      .ant-tabs-tab {
        border: none;
        margin: 0 12px;
        color: rgb(0 0 0 / 65%);
        font-family: 'Helvetica Neue', helvetica, arial, sans-serif;
        font-feature-settings: 'tnum', 'tnum';
        font-size: 14px;
        font-variant: tabular-nums;
      }

      .ant-tabs-tab-active {
        .ant-tab-title {
          box-sizing: content-box;
          padding: 8.5px 0;
          border-bottom: 2px solid #9a0000;
          color: #9a0000;
        }

        .ant-tabs-tab-btn {
          text-shadow: none;
        }
      }
    }
  }

  .center {
    position: relative;
    width: calc(100% - 240px);
    height: 100%;
    outline: none;

    &.hide {
      width: 100%;
    }

    .toolbarWrapper {
      display: flex;
      justify-content: center;
    }

    .toolbar {
      top: 12px;
      display: inline-flex;
      height: 34px;
      box-sizing: border-box;
      align-items: center;
      padding: 4px 12px;
      margin: 0 auto;
      background-color: #fff;
      box-shadow: 0 2px 6px 0 rgb(0 0 0 / 10%);
    }

    .graph {
      width: 100%;
      height: calc(100% - 42px);
    }

    .toolbutton {
      position: absolute;
      right: 20px;
      bottom: 36px;
    }
  }
}

@left-panel-fold: '@/assets/left-panel-fold.svg';
@left-panel-spread: '@/assets/left-panel-spread.svg';

.anchor {
  position: absolute;
  z-index: 2;
  top: 50%;
  left: 240px;
  width: 12px;
  height: 66px;
  background-image: url(@left-panel-fold);
  cursor: pointer;
  transform: translateY(-33px);

  &.hide {
    left: 0;
    background-image: url(@left-panel-spread);
  }
}
