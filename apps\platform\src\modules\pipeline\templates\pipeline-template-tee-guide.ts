import templateImg from '@/assets/tee-preview-template.jpg';
import { Model } from '@/util/valtio-helper';

import type { PipelineTemplateContribution } from '../pipeline-protocol';
import { PipelineTemplateType } from '../pipeline-protocol';

export class TemplateGuidTEE extends Model implements PipelineTemplateContribution {
  type: PipelineTemplateType = PipelineTemplateType.TEE_GUIDE;
  name = `二分类建模`;
  argsFilled = true;
  computeMode = ['TEE'];

  minimap = templateImg;
  content = (graphId: string) => {
    return {
      edges: [
        {
          edgeId: `${graphId}-node-1-output-0__${graphId}-node-3-input-0`,
          source: `${graphId}-node-1`,
          sourceAnchor: `${graphId}-node-1-output-0`,
          target: `${graphId}-node-3`,
          targetAnchor: `${graphId}-node-3-input-0`,
        },
        {
          edgeId: `${graphId}-node-2-output-0__${graphId}-node-3-input-1`,
          source: `${graphId}-node-2`,
          sourceAnchor: `${graphId}-node-2-output-0`,
          target: `${graphId}-node-3`,
          targetAnchor: `${graphId}-node-3-input-1`,
        },
        {
          edgeId: `${graphId}-node-3-output-0__${graphId}-node-4-input-0`,
          source: `${graphId}-node-3`,
          sourceAnchor: `${graphId}-node-3-output-0`,
          target: `${graphId}-node-4`,
          targetAnchor: `${graphId}-node-4-input-0`,
        },
        {
          edgeId: `${graphId}-node-3-output-0__${graphId}-node-5-input-0`,
          source: `${graphId}-node-3`,
          sourceAnchor: `${graphId}-node-3-output-0`,
          target: `${graphId}-node-5`,
          targetAnchor: `${graphId}-node-5-input-0`,
        },
        {
          edgeId: `${graphId}-node-5-output-0__${graphId}-node-6-input-0`,
          source: `${graphId}-node-5`,
          sourceAnchor: `${graphId}-node-5-output-0`,
          target: `${graphId}-node-6`,
          targetAnchor: `${graphId}-node-6-input-0`,
        },
        {
          edgeId: `${graphId}-node-5-output-0__${graphId}-node-7-input-0`,
          source: `${graphId}-node-5`,
          sourceAnchor: `${graphId}-node-5-output-0`,
          target: `${graphId}-node-7`,
          targetAnchor: `${graphId}-node-7-input-0`,
        },
        {
          edgeId: `${graphId}-node-6-output-0__${graphId}-node-7-input-1`,
          source: `${graphId}-node-6`,
          sourceAnchor: `${graphId}-node-6-output-0`,
          target: `${graphId}-node-7`,
          targetAnchor: `${graphId}-node-7-input-1`,
        },
        {
          edgeId: `${graphId}-node-7-output-0__${graphId}-node-8-input-0`,
          source: `${graphId}-node-7`,
          sourceAnchor: `${graphId}-node-7-output-0`,
          target: `${graphId}-node-8`,
          targetAnchor: `${graphId}-node-8-input-0`,
        },
        {
          edgeId: `${graphId}-node-7-output-0__${graphId}-node-9-input-0`,
          source: `${graphId}-node-7`,
          sourceAnchor: `${graphId}-node-7-output-0`,
          target: `${graphId}-node-9`,
          targetAnchor: `${graphId}-node-9-input-0`,
        },
        {
          edgeId: `${graphId}-node-7-output-0__${graphId}-node-10-input-0`,
          source: `${graphId}-node-7`,
          sourceAnchor: `${graphId}-node-7-output-0`,
          target: `${graphId}-node-10`,
          targetAnchor: `${graphId}-node-10-input-0`,
        },
        {
          edgeId: `${graphId}-node-10-output-0__${graphId}-node-11-input-1`,
          source: `${graphId}-node-10`,
          sourceAnchor: `${graphId}-node-10-output-0`,
          target: `${graphId}-node-11`,
          targetAnchor: `${graphId}-node-11-input-1`,
        },
        {
          edgeId: `${graphId}-node-6-output-0__${graphId}-node-12-input-1`,
          source: `${graphId}-node-6`,
          sourceAnchor: `${graphId}-node-6-output-0`,
          target: `${graphId}-node-12`,
          targetAnchor: `${graphId}-node-12-input-1`,
        },
        {
          edgeId: `${graphId}-node-5-output-1__${graphId}-node-12-input-0`,
          source: `${graphId}-node-5`,
          sourceAnchor: `${graphId}-node-5-output-1`,
          target: `${graphId}-node-12`,
          targetAnchor: `${graphId}-node-12-input-0`,
        },
        {
          edgeId: `${graphId}-node-12-output-0__${graphId}-node-11-input-0`,
          source: `${graphId}-node-12`,
          sourceAnchor: `${graphId}-node-12-output-0`,
          target: `${graphId}-node-11`,
          targetAnchor: `${graphId}-node-11-input-0`,
        },
        {
          edgeId: `${graphId}-node-11-output-0__${graphId}-node-13-input-0`,
          source: `${graphId}-node-11`,
          sourceAnchor: `${graphId}-node-11-output-0`,
          target: `${graphId}-node-13`,
          targetAnchor: `${graphId}-node-13-input-0`,
        },
        {
          edgeId: `${graphId}-node-11-output-0__${graphId}-node-14-input-0`,
          source: `${graphId}-node-11`,
          sourceAnchor: `${graphId}-node-11-output-0`,
          target: `${graphId}-node-14`,
          targetAnchor: `${graphId}-node-14-input-0`,
        },
      ],
      nodes: [
        {
          codeName: 'read_data/datatable',
          graphNodeId: `${graphId}-node-1`,
          label: '样本表',
          x: -380,
          y: -180,
          inputs: [],
          outputs: [`${graphId}-node-1-output-0`],
          nodeDef: {
            attrPaths: ['datatable_selected'],
            attrs: [{ is_na: false, s: 'alice-table' }],
            domain: 'read_data',
            name: 'datatable',
            version: '0.0.1',
          },
          status: 'STAGING',
        },

        {
          codeName: 'ml.train/lr_train',
          graphNodeId: `${graphId}-node-10`,
          label: 'LR训练',
          x: -60,
          y: 320,
          inputs: [`${graphId}-node-7-output-0`],
          outputs: [`${graphId}-node-10-output-0`],
          nodeDef: {
            domain: 'ml.train',
            name: 'lr_train',
            version: '0.0.1',
            attrPaths: [
              'input/train_dataset/ids',
              'input/train_dataset/label',
              'max_iter',
              'reg_type',
              'l2_norm',
              'tol',
              'penalty',
            ],
            attrs: [
              {
                is_na: false,
                ss: ['id2', 'id1'],
              },
              {
                is_na: false,
                ss: ['y'],
              },
              {
                i64: 10,
                is_na: false,
              },
              {
                is_na: false,
                s: 'logistic',
              },
              {
                f: 1,
                is_na: false,
              },
              {
                f: 0.0001,
                is_na: false,
              },
              {
                is_na: false,
                s: 'l2',
              },
            ],
          },
          status: 'STAGING',
        },
        {
          codeName: 'ml.predict/lr_predict',
          graphNodeId: `${graphId}-node-11`,
          label: 'LR预测',
          x: -40,
          y: 390,
          inputs: [`${graphId}-node-12-output-0`, `${graphId}-node-10-output-0`],
          outputs: [`${graphId}-node-11-output-0`],
          nodeDef: {
            domain: 'ml.predict',
            name: 'lr_predict',
            version: '0.0.1',
            attrPaths: [
              'input/feature_dataset/ids',
              'input/feature_dataset/label',
              'pred_name',
              'save_label',
              'label_name',
              'save_id',
              'id_name',
              'col_names',
            ],
            attrs: [
              {
                is_na: false,
                ss: ['id1'],
              },
              {
                is_na: false,
                ss: ['y'],
              },
              {
                is_na: false,
                s: 'pred',
              },
              {
                b: true,
                is_na: false,
              },
              {
                is_na: false,
                s: 'label',
              },
              {
                b: true,
                is_na: false,
              },
              {
                is_na: false,
                s: 'id',
              },
              {
                is_na: true,
                ss: [],
              },
            ],
          },
          status: 'STAGING',
        },
        {
          codeName: 'feature/vert_woe_substitution',
          graphNodeId: `${graphId}-node-12`,
          label: 'WOE转换',
          x: -60,
          y: 200,
          inputs: [`${graphId}-node-5-output-1`, `${graphId}-node-6-output-0`],
          outputs: [`${graphId}-node-12-output-0`],
          nodeDef: {
            domain: 'feature',
            name: 'vert_woe_substitution',
            version: '0.0.1',
          },
          status: 'STAGING',
        },
        {
          codeName: 'ml.eval/biclassification_eval',
          graphNodeId: `${graphId}-node-13`,
          label: '二分类评估',
          x: -40,
          y: 490,
          inputs: [`${graphId}-node-11-output-0`],
          outputs: [`${graphId}-node-13-output-0`],
          nodeDef: {
            attrPaths: [
              'input/predictions/label',
              'input/predictions/score',
              'bucket_num',
              'min_item_cnt_per_bucket',
            ],
            attrs: [
              {
                is_na: false,
                ss: ['label'],
              },
              {
                is_na: false,
                ss: ['pred'],
              },
              {
                i64: 10,
                is_na: false,
              },
              {
                i64: 2,
                is_na: false,
              },
            ],
            domain: 'ml.eval',
            name: 'biclassification_eval',
            version: '0.0.1',
          },
          status: 'STAGING',
        },
        {
          codeName: 'ml.eval/prediction_bias_eval',
          graphNodeId: `${graphId}-node-14`,
          label: '预测偏差评估',
          x: -270,
          y: 490,
          inputs: [`${graphId}-node-11-output-0`],
          outputs: [`${graphId}-node-14-output-0`],
          nodeDef: {
            attrPaths: [
              'input/predictions/label',
              'input/predictions/score',
              'bucket_num',
              'min_item_cnt_per_bucket',
              'bucket_method',
            ],
            attrs: [
              {
                is_na: false,
                ss: ['label'],
              },
              {
                is_na: false,
                ss: ['pred'],
              },
              {
                i64: 10,
                is_na: false,
              },
              {
                i64: 2,
                is_na: false,
              },
              {
                is_na: false,
                s: 'equal_width',
              },
            ],
            domain: 'ml.eval',
            name: 'prediction_bias_eval',
            version: '0.0.1',
          },
          status: 'STAGING',
        },
        {
          codeName: 'read_data/datatable',
          graphNodeId: `${graphId}-node-2`,
          label: '样本表',
          x: -160,
          y: -180,
          inputs: [],
          outputs: [`${graphId}-node-2-output-0`],
          nodeDef: {
            attrPaths: ['datatable_selected'],
            attrs: [
              {
                is_na: false,
                s: 'bob-table',
              },
            ],
            domain: 'read_data',
            name: 'datatable',
            version: '0.0.1',
          },
          status: 'STAGING',
        },
        {
          codeName: 'preprocessing/psi',
          graphNodeId: `${graphId}-node-3`,
          label: '隐私求交',
          x: -270,
          y: -90,
          inputs: [`${graphId}-node-1-output-0`, `${graphId}-node-2-output-0`],
          outputs: [`${graphId}-node-3-output-0`],
          nodeDef: {
            domain: 'preprocessing',
            name: 'psi',
            version: '0.0.1',
            attrPaths: ['input/input1/key', 'input/input2/key'],
            attrs: [
              {
                is_na: false,
                ss: ['id1'],
              },
              {
                is_na: false,
                ss: ['id2'],
              },
            ],
          },
          status: 'STAGING',
        },
        {
          codeName: 'stats/table_statistics',
          graphNodeId: `${graphId}-node-4`,
          label: '全表统计',
          x: -470,
          y: 10,
          inputs: [`${graphId}-node-3-output-0`],
          outputs: [`${graphId}-node-4-output-0`],
          nodeDef: {
            domain: 'stats',
            name: 'table_statistics',
            version: '0.0.1',
          },
          status: 'STAGING',
        },
        {
          codeName: 'preprocessing/train_test_split',
          graphNodeId: `${graphId}-node-5`,
          label: '随机分割',
          x: -160,
          y: 10,
          inputs: [`${graphId}-node-3-output-0`],
          outputs: [`${graphId}-node-5-output-0`, `${graphId}-node-5-output-1`],
          nodeDef: {
            domain: 'preprocessing',
            name: 'train_test_split',
            version: '0.0.1',
          },
          status: 'STAGING',
        },
        {
          codeName: 'feature/vert_woe_binning',
          graphNodeId: `${graphId}-node-6`,
          label: 'WOE分箱',
          x: -140,
          y: 120,
          inputs: [`${graphId}-node-5-output-0`],
          outputs: [`${graphId}-node-6-output-0`],
          nodeDef: {
            attrPaths: [
              'input/input_data/feature_selects',
              'input/input_data/label',
              'binning_method',
              'positive_label',
              'bin_num',
            ],
            attrs: [
              {
                is_na: false,
                ss: [
                  'contact_cellular',
                  'contact_telephone',
                  'contact_unknown',
                  'month_apr',
                  'month_aug',
                  'month_dec',
                  'month_feb',
                  'month_jan',
                  'month_jul',
                  'month_jun',
                  'month_mar',
                  'month_may',
                  'month_nov',
                  'month_oct',
                  'month_sep',
                  'poutcome_failure',
                  'poutcome_other',
                  'poutcome_success',
                  'poutcome_unknown',
                  'age',
                  'education',
                  'default',
                  'balance',
                  'housing',
                  'loan',
                  'day',
                  'duration',
                  'campaign',
                  'pdays',
                  'previous',
                  'job_blue-collar',
                  'job_entrepreneur',
                  'job_housemaid',
                  'job_management',
                  'job_retired',
                  'job_self-employed',
                  'job_services',
                  'job_student',
                  'job_technician',
                  'job_unemployed',
                  'marital_divorced',
                  'marital_married',
                  'marital_single',
                ],
              },
              {
                is_na: false,
                ss: ['y'],
              },
              {
                is_na: false,
                s: 'quantile',
              },
              {
                is_na: false,
                s: '1',
              },
              {
                i64: 10,
                is_na: false,
              },
            ],
            domain: 'feature',
            name: 'vert_woe_binning',
            version: '0.0.1',
          },
          status: 'STAGING',
        },
        {
          codeName: 'feature/vert_woe_substitution',
          graphNodeId: `${graphId}-node-7`,
          label: 'WOE转换',
          x: -410,
          y: 200,
          inputs: [`${graphId}-node-5-output-0`, `${graphId}-node-6-output-0`],
          outputs: [`${graphId}-node-7-output-0`],
          nodeDef: {
            domain: 'feature',
            name: 'vert_woe_substitution',
            version: '0.0.1',
          },
          status: 'STAGING',
        },
        {
          codeName: 'stats/pearsonr',
          graphNodeId: `${graphId}-node-8`,
          label: '相关系数矩阵',
          x: -540,
          y: 320,
          inputs: [`${graphId}-node-7-output-0`],
          outputs: [`${graphId}-node-8-output-0`],
          nodeDef: {
            domain: 'stats',
            name: 'pearsonr',
            version: '0.0.1',
          },
          status: 'STAGING',
        },
        {
          codeName: 'stats/vif',
          graphNodeId: `${graphId}-node-9`,
          label: 'VIF指标计算',
          x: -280,
          y: 320,
          inputs: [`${graphId}-node-7-output-0`],
          outputs: [`${graphId}-node-9-output-0`],
          nodeDef: {
            domain: 'stats',
            name: 'vif',
            version: '0.0.1',
          },
          status: 'STAGING',
        },
      ],
    };
  };
}
