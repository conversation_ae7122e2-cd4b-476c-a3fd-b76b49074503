import { MenuUnfoldOutlined, MenuFoldOutlined } from '@ant-design/icons';
import { useKeyPress } from 'ahooks';
import { Badge, Menu } from 'antd';
import classnames from 'classnames';
import { parse, stringify } from 'query-string';
import { useEffect, useState } from 'react';
import { history, useLocation } from 'umi';

import { ReactComponent as Logo } from '@/assets/logo-new.svg';
import { ReactComponent as LogoSmall } from '@/assets/logo-small.svg';
import { DefaultModalManager } from '@/modules/dag-modal-manager';
import { isWindows } from '@/util/platform';
import { useModel } from '@/util/valtio-helper';

import { EdgeLayoutService } from './edge-layout.service';
import styles from './index.less';

export interface MenuViewProps {
  menuItems: {
    label: React.ReactNode;
    icon?: React.ReactNode;
    component?: React.ReactNode;
    key: string;
    children?: MenuViewProps['menuItems'];
  }[];
  defaultTabKey?: string;
}

export const LabelWithBadge = (props: { label: string; count: number }) => {
  return (
    <div className={styles.labelWithBadge}>
      <span>{props.label}</span>
      {props.count !== 0 && <Badge color="#e04d66" count={props.count} />}
    </div>
  );
};

const foldHotKey = {
  key: isWindows ? 'ctrl.uparrow' : 'meta.ctrl.uparrow',
  text: isWindows ? 'Ctrl + ↑' : '⌘ + ctrl + ↑ ',
};

const SecondLevelMenuItems = [
  { secondKey: 'taskDetail', currentKey: 'prediction' },
  { secondKey: 'taskDetail', currentKey: 'psi' },
  { secondKey: 'dag', currentKey: 'my-project' },
  { secondKey: 'dataAuth', currentKey: 'data-manager' },
];

const getParentMenuKey = (menuItems: MenuViewProps['menuItems'], pathname: string) => {
  let parentKey = '',
    currentKey = '',
    secondLevelMenuKey = '';

  const pathNames: string[] = pathname.split('/');
  if (pathNames.length > 3) {
    currentKey = pathNames.at(-2) as string;
    secondLevelMenuKey =
      SecondLevelMenuItems.find((item) => item.currentKey === currentKey)?.secondKey ||
      '';
  } else {
    currentKey = pathNames.at(-1) as string;
  }

  for (const item of menuItems) {
    if (item.children?.find((child) => child.key === currentKey)) {
      parentKey = item.key;
    }
  }

  return {
    parentKey,
    currentKey,
    secondLevelMenuKey,
  };
};

export const LeftView = (props: MenuViewProps) => {
  const modalManager = useModel(DefaultModalManager);
  const layoutService = useModel(EdgeLayoutService);

  const { menuItems } = props;
  const [collapsed, setCollapsed] = useState(false);

  const { pathname, search } = useLocation();
  const parsedSearch = parse(search);

  const { ownerId } = parsedSearch as { ownerId?: string };
  const [tabKey, setTabKey] = useState<string>();
  // const componentsMap = getComponents(menuItems);

  const [defaultOpenKey] = useState([
    getParentMenuKey(menuItems, pathname).parentKey || '',
  ]);

  const [openKeys, setOpenKeys] = useState<string[]>([]);
  // useEffect(() => {
  //   history.push({
  //     pathname: pathname === '/' ? '/home' : pathname,
  //     search: stringify(
  //       ownerId
  //         ? {
  //             ...parsedSearch,
  //             ownerId,
  //             tab: tab || defaultTabKey,
  //           }
  //         : { tab: tab || defaultTabKey },
  //     ),
  //   });
  // }, [defaultTabKey, tab]);
  useEffect(() => {
    console.log(pathname, 'path');
    const tabName = pathname.split('/').at(-1);
    console.log(tabName, 'tabName');

    const { parentKey, currentKey, secondLevelMenuKey } = getParentMenuKey(
      menuItems,
      pathname,
    );
    console.log(parentKey, currentKey, 'parentKey, currentKey');
    layoutService.routeKeys = [];
    if (parentKey) {
      layoutService.routeKeys.push(parentKey);
      setOpenKeys([parentKey]);
    }
    setTabKey(currentKey);
    layoutService.routeKeys.push(currentKey);
    if (secondLevelMenuKey) {
      layoutService.routeKeys.push(secondLevelMenuKey);
    }
  }, [pathname]);

  // useEffect(() => {
  //   setTabKey(tab || defaultTabKey);
  // }, [tab]);

  const [collapseInfo, setCollapsedInfo] = useState(`收起/展开 ${foldHotKey.text}`);
  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };

  useEffect(() => {
    if (!collapsed) {
      setTimeout(() => setCollapsedInfo(`收起/展开 ${foldHotKey.text}`), 250);
    } else {
      setCollapsedInfo(``);
    }
  }, [collapsed]);

  useKeyPress([foldHotKey.key], (event) => {
    event.preventDefault();
    event.stopPropagation();
    toggleCollapsed();
  });

  const closeAllModal = () => {
    modalManager.closeAllModals();
  };
  return (
    <div
      className={classnames(
        styles.menuContainer,
        collapsed ? styles.fold : styles.unfold,
      )}
    >
      <div>
        <div className={collapsed ? styles.logoSmall : styles.logoLarge}>
          {collapsed ? (
            <LogoSmall style={{ fill: '#144a9b' }} />
          ) : (
            <Logo style={{ fill: '#144a9b' }} />
          )}
        </div>
        <Menu
          selectedKeys={[tabKey as string]}
          defaultOpenKeys={defaultOpenKey}
          openKeys={openKeys}
          mode="inline"
          inlineCollapsed={collapsed}
          items={menuItems}
          onOpenChange={(keys) => {
            setOpenKeys(keys);
          }}
          onSelect={({ key }) => {
            closeAllModal();
            history.push({
              pathname: `/edge/${key}`,
              search: stringify({ ownerId }),
            });
          }}
        />
      </div>

      <div className={styles.collapseInfo}>
        <div className={styles.collapseIcon} onClick={toggleCollapsed}>
          {collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
        </div>
        <div className={classnames(styles.collapseText)}>{collapseInfo}</div>
      </div>
    </div>
  );
};
