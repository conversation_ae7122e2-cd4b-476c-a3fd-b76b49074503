.coefficient :global .ant-btn-link:hover {
  color: rgba(0, 10, 26, 0.68);
}
.coefficient :global .anticon-check {
  color: #52c41a;
}
.coefficient .chartBlock .features {
  margin-bottom: 16px;
}
.coefficient .chartBlock .features .titleHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.coefficient .chartBlock .features .titleHeader .title {
  margin-bottom: 10px;
  color: rgba(0, 10, 26, 0.89);
  font-size: 14px;
  font-weight: 500;
}
.coefficient .chartBlock .features .tagWrapper {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
  overflow-y: hidden;
}
.coefficient .chartBlock .features .hasMaxHeight {
  max-height: 108px;
}
.coefficient .chartBlock .features :global .anticon-down,
.coefficient .chartBlock .features :global .anticon-up {
  margin-left: 4px;
  font-size: 12px;
}
.coefficient .chartBlock .features .showAll {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
}
.coefficient .chartBlock .features .openTag {
  position: absolute;
  top: 74px;
  display: block;
  width: 48px;
  padding-bottom: 16px;
  margin-right: 8px;
  background-color: #fff;
  color: rgba(0, 10, 26, 0.47);
  text-align: center;
}
.coefficient .chartBlock .features .closeTag {
  position: relative;
  display: block;
  width: 48px;
  margin-right: 8px;
  margin-bottom: 16px;
  background-color: #fff;
  color: rgba(0, 10, 26, 0.47);
  text-align: center;
}
.coefficient .chartBlock .features .tag {
  position: relative;
  display: block;
  overflow: hidden;
  width: 48px;
  border: none;
  margin-right: 8px;
  margin-bottom: 16px;
  color: rgba(0, 10, 26, 0.26);
  cursor: pointer;
  text-align: center;
  text-overflow: ellipsis;
  user-select: none;
  white-space: nowrap;
}
.coefficient .chartBlock .features .tag.selected {
  background-color: #faf3f3;
  color: #9a0000;
}
.coefficient .chartBlock .features .tag.selected::after {
  position: absolute;
  top: 0;
  right: 0;
  display: inline-block;
  width: 0;
  height: 0;
  border-width: 3px;
  border-style: solid;
  border-color: #9a0000 #9a0000 transparent transparent;
  content: '';
}
.customBtn {
  color: rgba(0, 10, 26, 0.68);
}
.customBtn:hover {
  color: #9a0000 !important;
}
.fullScreenContentPage {
  overflow: auto;
  padding-bottom: 24px;
  background-color: #fff;
}
.fullScreenContentPage .fullScreenHeader {
  display: flex;
  height: 56px;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  border: 1px solid #eee;
  margin-bottom: 16px;
}
.fullScreenContentPage .fullScreenHeader .title {
  color: #1d2129;
  font-size: 20px;
  font-weight: 500;
  letter-spacing: 0;
}
.fullScreenContentPage .fullScreenHeader .exit {
  color: rgba(0, 10, 26, 0.68);
  cursor: pointer;
  font-size: 14px;
  font-weight: 400;
}
.fullScreenContentPage .fullScreenHeader .exit:hover {
  color: #9a0000;
}
.fullScreenContentPage .fullScreenContentWrap {
  padding: 0 24px;
}
