.dagLogHeader {
  display: flex;
  height: 34px;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid #f5f5f5;
  background-color: #fff;
}

.logDrawerRoot {
  position: absolute;
  overflow: hidden;
}

.activeTab {
  background-color: #f5f5f5;
}

.center {
  color: gold;
}

.dagLogHeaderLeft {
  height: 34px;
}

.logLabelTitle {
  display: 'flex';
  padding-left: 16px;
}

.isUnfoldTitle {
  height: 32px;
  border-top: 2px solid rgb(0 104 250 / 100%);
  background-color: #fff;
}

.spaceText {
  display: flex;
  height: 32px;
  align-items: center;

  :global(.ant-progress) {
    margin: 0;
  }
}

.logText {
  color: rgb(0 0 0 / 90%);
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 20px;
}

.logTextDisabled {
  color: rgb(0 0 0 / 25%);
  cursor: not-allowed;
}

.runningText {
  color: rgb(0 0 0 / 65%);
  font-size: 12px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 22px;
}

.logHeaderIcon {
  padding: 0 16px;
  cursor: pointer;

  .open {
    transform: rotate(90deg);
  }

  .close {
    transform: rotate(-90deg);
  }
}

.dagLogContent {
  height: calc(100% - 36px);
  padding-top: 8px;
  font-size: 12px;

  .monaco-editor-background {
    background: #fff !important;
  }

  .monaco-editor .margin {
    background: #fff;
  }
}

.sliderResize {
  position: absolute;
  z-index: 99;
  display: flex;
  width: 100%;
  height: 1px;
  justify-content: center;
}

.sliderResize:hover {
  background-color: rgb(22 100 255 / 100%);
  cursor: row-resize;
}

.logDrawer {
  :global(.ant-drawer-content-wrapper) {
    transition: none !important;
  }
}

@log-handler: '@/assets/log-handler.svg';
@log-handler-hover: '@/assets/log-handler-hover.svg';

.anchor {
  position: absolute;
  top: -7px;
  display: block;
  width: 72px;
  height: 8px;
  background-image: url(@log-handler);

  &:hover {
    background-image: url(@log-handler-hover);
  }
}
