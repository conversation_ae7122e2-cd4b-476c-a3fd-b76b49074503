.font {
  color: rgb(0 0 0 / 45%);
  font-size: 12px;
}

.effectiveContent {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .text {
    width: 72px;
    color: rgb(0 0 0 / 45%);
    font-size: 12px;
    font-weight: 400;
    letter-spacing: 0;
    line-height: 20px;
  }

  .input {
    width: 100px !important;
  }
}

.subStrContent {
  .text {
    margin-bottom: 4px;
    color: rgb(0 0 0 / 85%);
    color: rgb(0 0 0 / 45%);
    font-size: 12px;
    font-weight: 400;
    letter-spacing: 0;
    line-height: 20px;
  }

  .subStrStart {
    margin-top: 12px;

    .tip {
      color: rgb(0 0 0 / 45%);

      div {
        white-space: nowrap !important;
      }
    }
  }
}

.LogContent {
  .content {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    gap: 8px;
  }
}

.UnaryContent {
  .content {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    gap: 8px;
  }

  .select {
    width: 70px;
    margin-bottom: 8px;
  }
}

.disableBtn {
  width: 40px;
  height: 28px;
  box-sizing: border-box;
  flex-shrink: 0;
  padding: 4px 8px;
  border-radius: 2px;
  background-color: #e5e5e5;
  color: rgb(0 0 0 / 45%);
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
}

.RangeContent {
  .text {
    margin-bottom: 4px;
    color: rgb(0 0 0 / 45%);
    font-size: 12px;
  }

  .content {
    margin-bottom: 12px;
  }
}
