.featureTitle {
  margin-bottom: 8px;
  color: rgba(0, 0, 0, 0.88);
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
}
.tableFeatures :global(.ant-table) {
  margin-inline: -8px -8px !important;
}
.intoHeader {
  margin-left: 48px;
}
.tableContent {
  margin-bottom: 16px;
}
.tableContent :global(.ant-table-content) {
  color: rgba(0, 0, 0, 0.85);
  font-size: 12px !important;
  font-weight: 500;
  line-height: 20px;
}
.mockStyle {
  display: flex;
  align-items: center;
}
.mockStyle :global(.ant-tag) {
  scale: 0.8;
}
.modelDesc {
  padding: 12px;
  margin-bottom: 24px;
  background: rgba(0, 0, 0, 0.02);
}
.modelDesc :global(.ant-descriptions-item-label) {
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  line-height: 22px;
}
.modelDesc :global(.ant-descriptions .ant-descriptions-row > td) {
  padding-bottom: 8px !important;
}
.configToggle {
  color: #9a0000;
  cursor: pointer;
}
.config {
  margin-top: 12px;
}
.config :global .ant-descriptions-item-label {
  font-size: 12px !important;
  line-height: 22px;
}
.config :global .ant-descriptions-item-content {
  font-size: 12px !important;
  line-height: 22px;
}
.config .configLabel {
  padding-bottom: 8px;
  color: rgba(0, 0, 0, 0.88);
  font-size: 14px;
  line-height: 22px;
}
.config .configContent {
  height: 54px;
  box-sizing: border-box;
  padding: 16px;
  background: rgba(0, 0, 0, 0.02);
}
