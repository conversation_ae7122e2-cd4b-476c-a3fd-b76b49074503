import { QuestionCircleOutlined } from '@ant-design/icons';
import type { FormInstance } from 'antd';
import { Drawer, Space, Flex, Button, Switch, Tooltip } from 'antd';
import { useEffect, useMemo, useRef, useState } from 'react';

import { AutoModal } from '@/components/auto-modal';
import FormBuilder from '@/components/form-builder/form';
import type { FormItemConfig } from '@/components/form-builder/search-form';
import { FormItemType } from '@/components/form-builder/search-form';
import { useModel } from '@/util/valtio-helper';

import { PSIModel } from '..';
import style from '../index.less';

import { PSIDataset } from './datasets';

interface IProps {
  open: boolean;
  onClose: () => void;
  onConfirm?: (data: Record<string, any>) => void;
}

const LabelWithTip = (props: { label: string; tip: string }) => {
  return (
    <Space>
      <span>{props.label}</span>
      <Tooltip title={props.tip}>
        <QuestionCircleOutlined />
      </Tooltip>
    </Space>
  );
};

const initialAdvanceConfig: FormItemConfig[] = [
  {
    name: 'allowEmpty',
    type: FormItemType.SELECT,
    itemProps: {
      label: (
        <LabelWithTip
          label="求交结果是否允许为空"
          tip="是否允许结果为空，如果允许，将保存一个空文件，如果不允许，将报错。"
        />
      ),
      initialValue: 1,
    },
    props: {
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 },
      ],
    },
  },
  {
    name: 'firstKeyDuplicated',
    type: FormItemType.SELECT,
    itemProps: {
      label: (
        <LabelWithTip
          label="表一关联键重复"
          tip="关联建是否重复（如果关联键重复，默认求交结果会膨胀，如果不重复，跳过重复检查，如果不确定，请选择是）。"
        />
      ),
      initialValue: 1,
    },
    props: {
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 },
      ],
    },
  },
  {
    name: 'secondKeyDuplicated',
    type: FormItemType.SELECT,
    itemProps: {
      label: (
        <LabelWithTip
          label="表二关联键重复"
          tip="关联建是否重复（如果关联键重复，默认求交结果会膨胀，如果不重复，跳过重复检查，如果不确定，请选择是）。"
        />
      ),
      initialValue: 1,
    },
    props: {
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 },
      ],
    },
  },
  {
    name: 'resort',
    type: FormItemType.SELECT,
    itemProps: {
      label: (
        <LabelWithTip
          label="是否重排序"
          tip="如果置为否，输出不保证对齐。警告：禁用此选项可能会导致后续组件出错。如果要附加其他组件，请不要关闭。"
        />
      ),
      initialValue: 1,
    },
    props: {
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 },
      ],
    },
  },
];

const defaultAdvanceData = {
  allowEmpty: 0,
  firstKeyDuplicated: 1,
  secondKeyDuplicated: 1,
  resort: 1,
};

function formatAdvanceData(data: Record<string, number>) {
  return Object.keys(data).reduce((acc, key) => {
    acc[key] = data[key] === 1 ? true : false;
    return acc;
  }, {} as Record<string, boolean>);
}

export const CreatePSIModal = (props: IProps) => {
  const { open, onClose, onConfirm } = props;
  const formRef = useRef<{ form: FormInstance }>(null);
  const advanceFormRef = useRef<{ form: FormInstance }>(null);
  const instance = useModel(PSIModel);
  const listModel = useModel(PSIModel);

  const initialConfig: FormItemConfig[] = [
    {
      name: 'name',
      type: FormItemType.INPUT,
      itemProps: {
        label: '任务名称',
        rules: [
          { required: true, message: '请输入任务名称' },
          {
            pattern: /^[a-zA-Z0-9_\u4e00-\u9fa5-]+$/,
            message: '请输入中文、大小写英文、数字、下划线、中划线',
          },
          { max: 32, message: '请输入32个字符以内' },
        ],
      },
      props: {
        placeholder: '请输入中文、大小写英文、数字、下划线、中划线，32个字符以内',
      },
    },
    {
      name: 'dataset1',
      type: FormItemType.CUSTOM,
      itemProps: {
        required: true,
        label: '本地数据',
        validateStatus: 'success',
        rules: [
          {
            validator(rule, value) {
              if (!value) return Promise.reject(new Error('请选择数据集'));
              if (Object.values(value).some((v) => v === undefined)) {
                return Promise.reject(new Error('请填写完整的数据集信息'));
              }
              return Promise.resolve();
            },
          },
        ],
      },
      customNode: () => (
        <PSIDataset type="local" name="dataset1" parties={instance.parties} />
      ),
    },
    {
      name: 'dataset2',
      type: FormItemType.CUSTOM,
      itemProps: {
        required: true,
        label: '外部数据',
        validateStatus: 'success',
        rules: [
          {
            validator(rule, value) {
              if (!value) return Promise.reject(new Error('请选择数据集'));
              if (Object.values(value).some((v) => v === undefined)) {
                return Promise.reject(new Error('请填写完整的数据集信息'));
              }
              return Promise.resolve();
            },
          },
        ],
      },
      customNode: () => (
        <PSIDataset type="remote" name="dataset2" parties={instance.parties} />
      ),
    },
    {
      name: 'protocol',
      type: FormItemType.SELECT,
      itemProps: {
        label: (
          <LabelWithTip
            label="求交协议"
            tip="求交协议（ECDH比较通用和稳定，适合低带宽场景。KKRT适合高带宽场景。RR22是最新的隐私求交协议，性能最佳。）"
          />
        ),
        rules: [{ required: true, message: '请选择求交协议' }],
        initialValue: 'PROTOCOL_RR22',
      },
      props: {
        placeholder: '请选择求交协议',
        options: [
          { label: 'PROTOCOL_ECDH', value: 'PROTOCOL_ECDH' },
          { label: 'PROTOCOL_RR22', value: 'PROTOCOL_RR22' },
          { label: 'PROTOCOL_KKRT', value: 'PROTOCOL_KKRT' },
        ],
      },
      watchFields: ['protocol'],
      watchHooks: {
        protocol: (value) => {
          console.log(value);
          const curve = initialConfig.find((item) => item.name === 'curveType');
          if (curve) {
            const newValue = value === 'PROTOCOL_ECDH' ? false : true;
            if (curve.hidden !== newValue) {
              curve.hidden = newValue;
              setConfig([...initialConfig]);
            }
          }
        },
      },
    },
    {
      name: 'curveType',
      type: FormItemType.SELECT,
      hidden: true,
      itemProps: {
        label: (
          <LabelWithTip
            label="PROTOCOL_ECDH"
            tip="求交协议（ECDH比较通用和稳定，适合低带宽场景。KKRT适合高带宽场景。RR22是最新的隐私求交协议，性能最佳。）"
          />
        ),
        rules: [{ required: true, message: '请选择求交协议' }],
        initialValue: 'CURVE_25519',
      },
      props: {
        placeholder: '请选择求交协议',
        options: [
          { label: 'CURVE_25519', value: 'CURVE_25519' },
          { label: 'CURVE_FOURQ', value: 'CURVE_FOURQ' },
          { label: 'CURVE_SM2', value: 'CURVE_SM2' },
          { label: 'CURVE_SECP256K1', value: 'CURVE_SECP256K1' },
        ],
      },
    },
    {
      name: 'joinType',
      type: FormItemType.SELECT,
      itemProps: {
        label: <LabelWithTip label="求交类型" tip="求交类型，默认为inner join" />,
        rules: [{ required: true, message: '请选择求交类型' }],
        initialValue: 'inner_join',
      },
      props: {
        placeholder: '请选择求交协议',
        options: [
          { label: 'inner_join', value: 'inner_join' },
          { label: 'full_join', value: 'full_join' },
          { label: 'Difference', value: 'Difference' },
          { label: 'left_join', value: 'left_join' },
        ],
      },
      watchFields: ['joinType'],
      watchHooks: {
        joinType: (value, form) => {
          console.log(value);
          const leftJoin = initialConfig.find((item) => item.name === 'leftJoin');
          if (leftJoin) {
            const newValue = value === 'left_join' ? false : true;
            if (leftJoin.hidden !== newValue) {
              leftJoin.hidden = newValue;
              setConfig([...initialConfig]);
              console.log(form.getFieldInstance('leftJoin'), 'form');
            }
          }
        },
      },
    },
    {
      name: 'leftJoin',
      hidden: true,
      type: FormItemType.ASYNC_SELECT,
      itemProps: {
        label: <LabelWithTip label="左表数据方" tip="左表数据方" />,
        rules: [{ required: true, message: '请选择左表数据方' }],
      },
      props: {
        placeholder: '请选择左表数据方',
        fetchOptions: () =>
          Promise.resolve(
            instance.parties.map((item) => ({
              value: item.nodeId,
              label: item.nodeName,
            })),
          ),
      },
    },
    {
      name: 'receivers',
      type: FormItemType.CONTROLLED_SELECT,
      itemProps: {
        label: (
          <LabelWithTip
            label="结果接收方"
            tip="选择结果接收方，默认选择全部数据参与方为结果接收方；如接收方为一方，则产出结果为单边样本表，不可连接输入需为联合表的组件。"
          />
        ),
        rules: [{ required: true, message: '请选择' }],
      },
      props: {
        placeholder: '请选择',
        mode: 'multiple',
        allowClear: true,
        disabled: true,
      },
      watchFields: ['dataset2'],
      watchHooks: {
        dataset2: (value, form) => {
          console.log(value, 'dataset2');
          const receivers = initialConfig.find((item) => item.name === 'receivers');
          const dataset1 = form.getFieldValue('dataset1');
          if (receivers) {
            const newValue = !(value.nodeId && dataset1.nodeId);
            if (receivers.props!.disabled !== newValue) {
              receivers.props = {
                ...receivers.props,
                disabled: newValue,
              };
              setConfig([...initialConfig]);
            }
          }
          if (dataset1 && value) {
            const nodeIds = [value.nodeId, dataset1.nodeId];
            const options = instance.parties
              .filter((item) => nodeIds.includes(item.nodeId))
              .map((item) => ({
                value: item.nodeId,
                label: item.nodeName,
              }));

            form.getFieldInstance('receivers')?.setOptions(options);
          }
        },
      },
    },
    {
      name: 'description',
      type: FormItemType.TEXTAREA,
      itemProps: {
        label: '任务描述',
      },
      props: {
        placeholder: '请输入任务描述',
      },
    },
  ];

  const [advanceConfig] = useState<FormItemConfig[]>(initialAdvanceConfig);

  const [config, setConfig] = useState(initialConfig);
  const [showAdvancedConfig, setShowAdvancedConfig] = useState(false);

  useEffect(() => {
    instance.getParties();
  }, []);

  useEffect(() => {
    if (open && instance.parties.length) {
      formRef.current?.form.getFieldInstance('receivers')?.setOptions(
        instance.parties.map((item) => ({
          value: item.nodeId,
          label: item.nodeName,
        })),
      );
    }
  }, [open]);

  const memoFormBuilder = useMemo(() => {
    return (
      <FormBuilder
        ref={formRef}
        config={config}
        formConfig={{
          layout: 'horizontal',
          initialValues: {
            dataset1: {
              nodeId: instance.parties[0]?.nodeId,
            },
          },
        }}
      />
    );
  }, [config]);

  const submit = () => {
    formRef.current?.form.validateFields().then((values) => {
      console.log(showAdvancedConfig, values, 'showAdvancedConfig');
      if (showAdvancedConfig) {
        advanceFormRef.current?.form.validateFields().then((advanceValues) => {
          onConfirm?.({
            ...values,
            ...formatAdvanceData(advanceValues),
            parties: instance.parties,
          });
          console.log({ ...values, ...formatAdvanceData(advanceValues) }, 'values');
        });
      } else {
        const confirmData = {
          ...values,
          ...formatAdvanceData(defaultAdvanceData),
          parties: instance.parties,
        };
        console.log(confirmData, 'confirmData');
        onConfirm?.(confirmData);
      }
    });
  };

  const cancelDrawer = () => {
    formRef.current?.form.resetFields();
    setConfig([...initialConfig]);
    onClose();
  };
  return (
    <AutoModal
      title="新建任务"
      width={700}
      open={open}
      onCancel={cancelDrawer}
      maskClosable={false}
      footer={
        <Flex justify="end">
          <Space>
            <Button onClick={cancelDrawer}>取消</Button>
            <Button type="primary" loading={listModel.createLoading} onClick={submit}>
              提交
            </Button>
          </Space>
        </Flex>
      }
    >
      {memoFormBuilder}
      <div className={style.advanceConfigBar}>
        <span>高级配置</span>
        <Switch
          checked={showAdvancedConfig}
          onChange={(checked) => setShowAdvancedConfig(checked)}
        />
      </div>
      <div style={{ display: showAdvancedConfig ? 'block' : 'none' }}>
        <FormBuilder
          ref={advanceFormRef}
          config={advanceConfig}
          formConfig={{ layout: 'horizontal' }}
        />
      </div>
    </AutoModal>
  );
};

// export class CreatePSIModel extends Model {
//   parties: AllocatedNode[] = [];
//   service = getModel(ProjectPSIService);

//   getParties = async () => {
//     const res = await this.service.getAllocatedNodes();
//     this.parties = res;
//   };
// }
