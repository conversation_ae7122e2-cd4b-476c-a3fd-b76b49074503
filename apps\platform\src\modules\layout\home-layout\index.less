.home {
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 100%;
  flex-direction: column;
  background-size: cover;
}

.homeBg {
  background-image: url('../../../assets/bg2.png');
}

.centerBg {
  background-image: url('../../../assets/bg2.png');
}

.header-link {
  text-decoration: none;
}

.header-items {
  display: flex;
  width: 100%;
  height: 56px;
  align-items: center;
  color: #fefcfc;

  .left {
    display: flex;
    flex: 1;
    align-items: center;
    padding-left: 16px;

    svg {
      padding-right: 16px;
      border-right: none;
    }

    .logo {
      cursor: pointer;
      height: 44px;
    }

    .subTitle {
      margin-left: 16px;
      color: #ddbbb8;
      font-size: 20px;
      font-weight: 500;
    }

    .line {
      width: 1px;
      height: 12px;
      margin: 14px;
      margin: 0 24px;
      background-color: rgb(0 10 26 / 16%);
    }

    .myNodeTitle {
      display: flex;
      width: 188px;
      height: 32px;
      align-items: center;
      border-radius: 4px;
      background-color: #741109 !important;
      color: #a96c67;
      cursor: pointer;
      font-size: 14px;
      font-weight: 400;

      svg {
        padding-right: 4px;
        padding-left: 14px;
        border: none !important;
      }

      .nodeName {
        display: inline-block;
        overflow: hidden;
        max-width: 115px;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .p2pLeft {
    svg {
      padding-right: 16px;
      border: none;
    }
  }

  .right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-right: 16px;
    color: #fefcfc;

    .community {
      display: flex;
      align-items: center;
      padding: 0 12px;
      cursor: pointer;
      font-size: 14px;

      svg {
        margin-right: 4px;
        font-size: 16px;
      }
    }

    .line {
      width: 1px;
      height: 12px;
      background-color: rgb(0 10 26 / 16%);
    }

    .loginline {
      width: 1px;
      height: 12px;
      margin: 14px;
      margin-right: 24px;
      margin-left: 12px;
      background-color: rgb(0 10 26 / 16%);
    }

    .contentHeaderRight {
      padding: 0 12px;
    }

    .help {
      display: flex;
      align-items: center;
      padding: 0 12px;
      cursor: pointer;
      font-size: 14px;

      svg {
        margin-right: 4px;
        font-size: 16px;
      }
    }
  }
}

.header {
  height: 56px;
  background: #9a0000;
}

.content {
  width: 100%;
  height: 500px;
  box-sizing: border-box;
  flex: 1;
  padding: 0 4px 12px;
  overflow-y: auto;
}

.messageBadge {
  color: #fefcfc !important;
  :global(.ant-badge) {
    box-shadow: none !important;
  }
}

.headerDropdown {
  min-width: 300px;
  min-height: 100px;
}
