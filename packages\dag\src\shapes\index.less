.dag-node {
  display: flex;
  width: 100%;
  height: 100%;
  align-items: center;
  border: 1px solid #c2c8d5;
  border-radius: 4px;
  border-left: 4px solid #4762b2;
  background-color: #fff;
  box-shadow: 0 2px 5px 1px rgb(0 0 0 / 6%);
  transition: background 0.5s;

  .icon {
    flex-shrink: 0;
    margin-left: 8px;
  }

  .label {
    display: inline-block;
    overflow: hidden;
    width: 122px;
    flex-shrink: 0;
    margin-left: 8px;
    color: #666;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .status {
    flex-shrink: 0;
  }

  .message {
    margin-top: -44px;
    margin-left: 22px;
    cursor: pointer;
  }

  &.success {
    border: 1px solid rgb(43 181 170 / 30%);
    border-left: 4px solid #bfe9e5;

    .message {
      margin-left: 5px;
    }
  }

  &.failed {
    border: 1px solid rgb(224 77 102 / 30%);
    border-left: 4px solid #f6cad1;

    .message {
      margin-left: 5px;
    }
  }

  &.stopped {
    border-left: 4px solid rgb(252 117 116 / 100%);

    .message {
      margin-left: 5px;
    }
  }

  &.pending {
    border-top: 1px dashed #4762b2;
    border-right: 1px dashed #4762b2;
    border-bottom: 1px dashed #4762b2;
  }

  &.running {
    .message {
      margin-left: 5px;
    }
  }

  &.disabled {
    cursor: not-allowed;
    filter: opacity(0.5);

    .label {
      cursor: not-allowed;
    }
  }

  &.unfinished {
    border-left: 4px solid #d9d9d9;

    .message {
      margin-left: 5px;
    }
  }

  &.opaque {
    opacity: 0.25;
  }

  &.hightlight {
    border-color: #2bb5aa;
    border-radius: 2px;
    box-shadow: 0 0 0 4px rgb(35 182 95 / 12%);
  }
}

.x6-node-selected {
  .dag-node {
    border-color: #4762b2;
    border-radius: 2px;
    box-shadow: 0 0 0 4px #faf3f3;
  }

  .dag-node.pending {
    box-shadow: 0 0 0 4px #faf3f3;
  }

  .dag-node.unfinished {
    border-color: #d9d9d9;
    border-radius: 2px;
    box-shadow: 0 0 0 4px rgb(194 200 213 / 20%);
  }

  .dag-node.success {
    border-color: #2bb5aa;
    border-radius: 2px;
    box-shadow: 0 0 0 4px rgb(35 182 95 / 12%);
  }

  .dag-node.failed {
    border-color: #e04d66;
    border-radius: 2px;
    box-shadow: 0 0 0 4px rgb(252 117 116 / 12%);
  }

  .dag-node.stopped {
    border-color: rgb(252 117 116 / 100%);
    border-radius: 2px;
    box-shadow: 0 0 0 4px rgb(252 117 116 / 12%);
  }
}

.x6-edge:hover {
  path:nth-child(2) {
    stroke: #c96962;
    stroke-width: 1px;
  }
}

.x6-edge-selected {
  path:nth-child(2) {
    stroke: #c96962;
    stroke-width: 1.5px !important;
  }
}

.x6-widget-selection-inner {
  padding-right: 12px;
  padding-bottom: 8px;
  border: 1px solid #c96962;
  margin-top: -4px;
  margin-left: -4px;
}

.x6-widget-selection-box {
  opacity: 0;
}

.x6-widget-selection-rubberband {
  border: none;
  background-color: #faf3f3;
}

@keyframes ant-line {
  to {
    stroke-dashoffset: -1000;
  }
}

.description {
  position: relative;
  font-size: 14px;
  word-break: break-all;

  .label {
    padding: 7px 12px;
    border-bottom: 1px solid rgb(0 0 0 / 7%);
    color: rgb(0 0 0 / 85%);
    font-weight: 500;
  }

  .resultItem {
    display: flex;
    height: 20px;
    align-items: center;
    padding: 4px 13px;
    border-radius: 2px;
    margin-bottom: 4px;
    background-color: rgb(0 0 0 / 3%);
    color: #4762b2;
    font-size: 12px;
    line-height: 12px;

    .elllips {
      color: #4762b2;
      font-size: 12px;
    }

    &:hover {
      color: #4762b2;

      .elllips {
        color: #4762b2;
      }
    }

    .title {
      margin-left: 11px;
    }

    .defaultIcon {
      margin-right: 11px;
    }

    .ellipsisName {
      overflow: hidden;
      max-width: 70px;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    :hover {
      cursor: pointer;
    }
  }

  .bottom {
    color: rgb(0 0 0 / 65%);
    font-size: 12px;

    .copy {
      display: flex;
      margin-bottom: 4px;
    }

    :global {
      .ant-typography {
        color: rgb(0 0 0 / 68%);
      }

      .ant-badge {
        margin-right: 8px;
      }
    }
  }

  .continueRunBtn {
    margin-left: 16px;
    color: #c1615a;
    cursor: pointer;
    font-size: 12px;
  }
}

.popover {
  :global {
    .ant-popover-content {
      margin-top: 8px;
    }

    .ant-popover-inner-content {
      padding: 0;
    }
  }
}
