import { QuestionCircleFilled } from '@ant-design/icons';
import {
  Badge,
  Button,
  Input,
  Popconfirm,
  Popover,
  Space,
  type TableProps,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { useState } from 'react';

import CommonTable from '@/components/common-table';
import { StatusTag } from '@/components/status-tag';

import {
  SelectOptionsValueEnum,
  StatusEnum,
  type MessageTableData,
} from '../message.service';

import style from './index.less';

function getStatusText(status: StatusEnum) {
  switch (status) {
    case StatusEnum.PROCESS:
      return <span style={{ color: '#ff9346' }}>待同意</span>;
    case StatusEnum.AGREE:
      return '已同意';
    case StatusEnum.REJECT:
      return <span style={{ color: '#e04d66' }}>已拒绝</span>;
  }
}

interface MessageTableProps extends TableProps {
  data: Record<string, any>[];
  type: string;
  hideScroll?: boolean;
  onProcess?: (params: API.VoteReplyRequest) => void;
  onShowDrawer?: (id: string) => void;
}

const MessageTable = (props: MessageTableProps) => {
  const { data, type, onProcess, onShowDrawer } = props;
  const [comment, setComment] = useState('');

  const messageTableColumns: ColumnsType<MessageTableData> = [
    // 如果是“我发起的”，不显示发起机构
    {
      title: '发起机构',
      dataIndex: 'initiatorNodeName',
      key: 'initiatorNodeName',
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (_, record: MessageTableData) => {
        if (record.voteTypeMessage?.projectId === 'PSI') return '隐私求交';
        else if (record.voteTypeMessage?.projectId === 'FI') return '联合预测';
        else {
          if (record.type === SelectOptionsValueEnum.PROJECT_ARCHIVE)
            return '联合建模-归档';
          else if (record.type === SelectOptionsValueEnum.PROJECT_NODE_ADD)
            return '联合建模-邀约';
        }
      },
    },
    {
      title: '项目名',
      dataIndex: 'messageName',
      key: 'messageName',
    },
    {
      title: '流程状态',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: StatusEnum, record) => (
        <div className={style.messageStatus}>
          {status === StatusEnum.PROCESS && <StatusTag type="warning" text="待同意" />}
          {status === StatusEnum.AGREE && <StatusTag type="success" text="已同意" />}
          {status === StatusEnum.REJECT && (
            <Space>
              <StatusTag type="failed" text="已拒绝" />
              {type !== 'apply' && (
                <Popover content={`拒绝原因：${record.reason}`}>
                  <QuestionCircleFilled />
                </Popover>
              )}
            </Space>
          )}
        </div>
      ),
    },
    {
      title: '提交时间',
      dataIndex: 'createTime',
      key: 'createTime',
      render: (createTime: string) => (
        <div>{dayjs(createTime).add(8, 'hour').format('YYYY-MM-DD HH:mm:ss')}</div>
      ),
    },
    {
      title: '意见',
      width: '23%',
      dataIndex: 'nodes',
      key: 'nodes',
      render: (nodes) => (
        <div style={{ display: 'flex', flexWrap: 'wrap' }}>
          {nodes?.map((item: any, index: number) => (
            <div key={item.participantID}>
              <span style={{ marginRight: '6px' }}>
                {item.participantName} ({getStatusText(item.action)})
              </span>
              {item.action === StatusEnum.REJECT && (
                <Popover content={item.reason}>
                  <QuestionCircleFilled />
                </Popover>
              )}
              {index !== nodes.length - 1 && <span style={{ marginRight: 4 }}>;</span>}
            </div>
          ))}
        </div>
      ),
    },
  ];

  const actionColumn = {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    render: (_: any, record: MessageTableData) => {
      return (
        <div>
          {type === 'process' && record.status === StatusEnum.PROCESS && (
            <>
              <Button
                size="small"
                className={style.btnAgree}
                type="link"
                onClick={() =>
                  onProcess?.({
                    voteId: record.voteId,
                    action: StatusEnum.AGREE,
                  })
                }
              >
                同意
              </Button>
              <Popconfirm
                title="你确定要拒绝吗？"
                placement="left"
                destroyTooltipOnHide
                onOpenChange={(open) => {
                  if (!open) {
                    setComment('');
                  }
                }}
                description={
                  <Input.TextArea
                    maxLength={50}
                    placeholder="请输50字符以内的理由"
                    allowClear
                    onChange={(e) => setComment(e.target.value)}
                  />
                }
                okText="拒绝"
                cancelText="取消"
                okButtonProps={{
                  danger: true,
                  ghost: true,
                }}
                onConfirm={async () => {
                  onProcess?.({
                    action: StatusEnum.REJECT,
                    voteId: record.voteId,
                    reason: comment,
                  });
                }}
              >
                <Button size="small" className={style.btnDisagree} type="link">
                  拒绝
                </Button>
              </Popconfirm>
            </>
          )}
          <Button
            size="small"
            type="text"
            onClick={() => onShowDrawer?.(record.voteId!)}
          >
            详情
          </Button>
        </div>
      );
    },
  };
  const handleColumns = [...messageTableColumns.slice(0, -1)];
  handleColumns.push(actionColumn);

  const applyColumns = [...messageTableColumns.slice(1)];
  applyColumns.push(actionColumn);

  const columns = type === 'apply' ? applyColumns : handleColumns;
  return (
    <CommonTable
      columns={columns}
      dataSource={data}
      hideScroll={props.hideScroll}
      rowKey={(record) => record.voteId}
      {...props}
    />
  );
};

export default MessageTable;
