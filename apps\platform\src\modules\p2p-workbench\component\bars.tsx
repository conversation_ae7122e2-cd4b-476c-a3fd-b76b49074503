import { RightOutlined } from '@ant-design/icons';
import { parse } from 'query-string';
import { history, useLocation } from 'umi';

import { ReactComponent as Data } from '@/assets/home-data.svg';
import { ReactComponent as Message } from '@/assets/home-message.svg';
import { ReactComponent as Pred } from '@/assets/home-pred.svg';
import { ReactComponent as Project } from '@/assets/home-project.svg';
import { ReactComponent as PSI } from '@/assets/home-psi.svg';

import styles from '../index.less';

const datas = [
  {
    title: '我的数据',
    desc: '自主掌控，安全托管',
    icon: <Data />,
    path: '/edge/data-manager',
  },
  {
    title: '消息中心',
    desc: '协作动态，实时触达',
    icon: <Message />,
    path: '/edge/messages',
  },
  {
    title: '联合建模',
    desc: '数据可用，模型共创',
    icon: <Project />,
    path: '/edge/my-project',
  },

  {
    title: '隐私求交',
    desc: '交集计算，密文匹配',
    icon: <PSI />,
    path: '/edge/psi',
  },
  {
    title: '联合预测',
    desc: '多方协作，算据共享',
    icon: <Pred />,
    path: '/edge/prediction',
  },
];

export const Bars = () => {
  const { search } = useLocation();
  const { ownerId } = parse(search);

  const toLink = (path: string) => {
    if (ownerId) {
      history.push(`${path}?ownerId=${ownerId}`);
    } else {
      history.push('/login');
    }
  };

  return (
    <div className={styles.workbenchBars}>
      {datas.map((item) => (
        <div className={styles.bar} key={item.title}>
          <div className={styles.barIcon}>{item.icon}</div>
          <div className={styles.barInfos}>
            <div className={styles.barTitle}>{item.title}</div>
            <div className={styles.barDesc}>{item.desc}</div>
            <div className={styles.barLink} onClick={() => toLink(item.path)}>
              立即查看
              <RightOutlined />
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};
