import { Select } from 'antd';
import { useEffect, useState } from 'react';

import { useModel } from '@/util/valtio-helper';

import { DataTableAuth } from '../data-table-info/data-table-auth/data-tabel-auth.view';
import type { AuthComponentProps } from '../data-table-info/data-table-auth-drawer';
import { AuthType } from '../data-table-info/data-table-auth-drawer';
import { DataVisibleAuthComponent } from '../data-table-info/data-visible-auth';

import styles from './auth.less';
import { DataManagerService } from './data-manager.service';
import { DataManagerView } from './data-manager.view';

const ComponentSwitcher = (props: AuthComponentProps) => {
  const { type, data, nodeVoters } = props;
  return (
    <div>
      <div style={{ display: type === AuthType.PROJECT ? 'block' : 'none' }}>
        <DataTableAuth tableInfo={data} size="middle" />
      </div>
      <div style={{ display: type !== AuthType.PROJECT ? 'block' : 'none' }}>
        <DataVisibleAuthComponent type={type} data={data} nodeVoters={nodeVoters} />
      </div>
    </div>
  );
};

const DataAuth = () => {
  const [authType, setAuthType] = useState<string>('project');
  const model = useModel(DataManagerView);
  const data =
    Object.keys(model.tableInfo).length > 0
      ? model.tableInfo
      : JSON.parse(localStorage.getItem('currentTableInfo') || '{}');
  console.log(data, 'data');
  const authTypeOptions = [
    { label: '联合建模', value: 'project' },
    { label: '联合预测', value: 'prediction' },
    { label: '隐私求交', value: 'psi' },
  ];

  const [nodeVoters, setNodeVoters] = useState<
    { nodeId: string; nodeName: string; instId: string; instName: string }[]
  >([]);
  const dataManagerService = useModel(DataManagerService);

  useEffect(() => {
    if (data.nodeId)
      dataManagerService.getNodeVoters(data.nodeId).then((res) => {
        setNodeVoters(res);
      });
  }, []);
  return (
    <div className={styles.dataAuth}>
      <div className={styles.authTypeSelect}>
        <span className={styles.authText}>工程授权类型: </span>
        <Select
          style={{ width: 300 }}
          value={authType}
          options={authTypeOptions}
          onChange={(val) => setAuthType(val)}
        />
      </div>
      <ComponentSwitcher
        type={authType as AuthType}
        data={data}
        nodeVoters={nodeVoters}
      />
    </div>
  );
};

export default DataAuth;
