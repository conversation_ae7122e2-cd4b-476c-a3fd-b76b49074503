.embeddedNodes {
  display: flex;
  justify-content: space-around;

  .embeddedNode {
    width: 306px;
    border-radius: 8px;
    background-color: rgb(0 0 0 / 2%);

    .nodeContent {
      display: flex;
      padding: 10px;

      .header {
        color: rgb(0 0 0 / 85%);
        font-size: 12px;
        font-weight: 500;
      }

      .table {
        margin-top: 8px;
        color: rgb(0 0 0 / 88%);
        font-size: 12px;
        font-weight: 400;
      }
    }
  }
}
