html,
body {
  height: 100%;
  padding: 0;
  margin: 0;
}

.ant-drawer
  .ant-drawer-content-wrapper
  .ant-drawer-content
  .ant-drawer-wrapper-body
  .ant-drawer-header-title {
  flex-direction: row-reverse;
}

.ant-drawer
  .ant-drawer-content-wrapper
  .ant-drawer-content
  .ant-drawer-wrapper-body
  .ant-drawer-header-title
  .ant-drawer-close {
  font-size: 12px;
  margin-inline-end: 0;
}

#root,
#root-master {
  min-width: 1024px;
  height: 100%;
}

/* .ant-select-dropdown
  .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  background-color: #faf3f3;
  color: #9a0000;
  font-weight: 400;
} */

.ant-select-selector {
  box-shadow: none;
  outline: 0;
}

.ant-input-outlined:focus-within {
  box-shadow: none;
  outline: 0;
}

.ant-table-wrapper .ant-table-filter-trigger {
  color: #9c9898;
}

/*
.ant-table-wrapper .ant-table-filter-trigger:hover {
  background: none;
  color: #9a0000;
} */

/* .ant-input-outlined:focus,
.ant-input-outlined:focus-within {
  box-shadow: 0 0 0 1px rgb(97 22 5 / 24%);
} */

.ant-select-focused:where(
    .css-dev-only-do-not-override-1jsmp6o
  ).ant-select-outlined:not(
    .ant-select-disabled,
    .ant-select-customize-input,
    .ant-pagination-size-changer
  )
  .ant-select-selector {
  box-shadow: 0 0 0 1px rgb(97 22 5 / 24%);
}

.ant-radio-button-wrapper {
  border-end-start-radius: 0;
  border-inline-start: none;
  border-start-start-radius: 0;
}
