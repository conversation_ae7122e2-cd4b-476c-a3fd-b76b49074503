// 引入 echarts 核心模块，核心模块提供了 echarts 使用必须要的接口。
// 引入柱状图图表，图表后缀都为 Chart
import dayjs from 'dayjs';
import { LineChart } from 'echarts/charts';
// 引入标题，提示框，直角坐标系，数据集，内置数据转换器组件，组件后缀都为 Component
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  LegendComponent,
} from 'echarts/components';
import * as echarts from 'echarts/core';
// 标签自动布局、全局过渡动画等特性
import { LabelLayout, UniversalTransition } from 'echarts/features';
// 引入 Canvas 渲染器，注意引入 CanvasRenderer 或者 SVGRenderer 是必须的一步
import { CanvasRenderer } from 'echarts/renderers';
import { useEffect, useRef } from 'react';

import {
  getMetricsHistory,
  getTaskStatistics,
} from '@/services/secretpad2/WorkbenchController';
import { Model, useModel } from '@/util/valtio-helper';

import styles from '../index.less';

import { Card } from './card';

const option1 = {
  tooltip: {
    trigger: 'axis',
  },
  legend: {
    bottom: 5,
    icon: 'circle',
    itemWidth: 8,
    data: ['隐私求交', '联合预测', '联合建模'],
    textStyle: {
      fontSize: 10,
    },
  },
  grid: {
    top: 10,
    bottom: 30,
    left: '2%',
    right: '5%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: [],
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: function (value: number) {
        return Math.round(value);
      },
      margin: 16,
    },
  },
  series: [
    {
      name: '隐私求交',
      type: 'line',
      data: [],
      smooth: true,
      symbol: 'none',
    },
    {
      name: '联合预测',
      type: 'line',
      data: [],
      smooth: true,
      symbol: 'none',
    },
    {
      name: '联合建模',
      type: 'line',
      data: [],
      smooth: true,
      symbol: 'none',
    },
  ],
};

const option2 = {
  tooltip: {
    trigger: 'axis',
  },
  legend: {
    bottom: 5,
    icon: 'circle',
    itemWidth: 8,
    data: ['CPU利用率', '内存利用率'],
    textStyle: {
      fontSize: 10,
    },
  },
  grid: {
    top: 10,
    bottom: 35,
    left: 0,
    right: '4%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: [],
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      margin: 14,
      formatter: '{value}%',
    },
  },
  series: [
    {
      name: 'CPU利用率',
      type: 'line',
      data: [],
      smooth: true,
      symbol: 'none',
    },
    {
      name: '内存利用率',
      type: 'line',
      data: [],
      smooth: true,
      symbol: 'none',
    },
  ],
};

// 注册必须的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  LegendComponent,
  LineChart,
  LabelLayout,
  UniversalTransition,
  CanvasRenderer,
]);

export const Charts = () => {
  const taskRef = useRef<HTMLDivElement>(null);
  const nodeRef = useRef<HTMLDivElement>(null);
  const model = useModel(ChartModel);

  useEffect(() => {
    let chart: echarts.ECharts;
    let chart2: echarts.ECharts;
    if (taskRef.current) {
      chart = echarts.init(taskRef.current);
      chart.setOption(option1);

      model.getTaskData().then(() => {
        option1.series[0].data = model.taskData[0];
        option1.series[1].data = model.taskData[1];
        option1.series[2].data = model.taskData[2];
        option1.xAxis.data = model.taskData[3];
        chart.setOption(option1);
      });
    }
    if (nodeRef.current) {
      chart2 = echarts.init(nodeRef.current);
      model.getMetricsData().then(() => {
        option2.series[0].data = model.metricsData[0];
        option2.series[1].data = model.metricsData[1];
        option2.xAxis.data = model.metricsData[2];
        chart2.setOption(option2);
      });
    }

    const resizeFn = () => {
      chart?.resize();
      chart2?.resize();
    };
    window.addEventListener('resize', resizeFn);
    return () => {
      window.removeEventListener('resize', resizeFn);
      chart?.dispose();
      chart2?.dispose();
    };
  }, []);

  return (
    <div className={styles.chartsWrapper}>
      <div className={styles.chart}>
        <Card title="任务执行趋势">
          <div className={styles.chartContent} ref={taskRef}></div>
        </Card>
      </div>
      <div className={styles.chart}>
        <Card title="设备运行趋势">
          <div className={styles.chartContent} ref={nodeRef}></div>
        </Card>
      </div>
    </div>
  );
};

class ChartModel extends Model {
  taskData: any[] = [];
  metricsData: any[] = [];

  constructor() {
    super();
  }

  getMetricsData = async () => {
    const res = await getMetricsHistory({
      interval: 8,
    });
    this.metricsData = [
      res.data?.metrics.map((item: Record<string, any>) => {
        return item.cpuUsage;
      }),
      res.data?.metrics.map((item: Record<string, any>) => {
        return item.memoryUsage;
      }),
      res.data?.metrics.map((item: Record<string, any>) => {
        return dayjs(item.timestamp).format('HH:mm');
      }),
    ];
    // this.metricsData = res.data;
  };

  getTaskData = async () => {
    const res = await getTaskStatistics({
      startDate: dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
      endDate: dayjs().format('YYYY-MM-DD'),
    });
    this.taskData = [
      res.data?.map((item: Record<string, any>) => {
        return item.psiTaskCount;
      }),
      res.data?.map((item: Record<string, any>) => {
        return item.fiTaskCount;
      }),
      res.data?.map((item: Record<string, any>) => {
        return item.projectTaskCount;
      }),
      [
        ...Array.from({ length: 7 }, (_, i) =>
          dayjs()
            .subtract(7 - i, 'day')
            .format('MM-DD'),
        ),
        dayjs().format('MM-DD'),
      ],
    ];
  };
}
