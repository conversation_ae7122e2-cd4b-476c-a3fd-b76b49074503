@import url('@/variables.less');

.content {
  display: flex;
  height: 100%;
  background-image: url('../../assets/login-bg.png');
  background-position: left center;
  background-repeat: no-repeat;
  background-size: cover;

  .left {
    position: relative;
    display: flex;
    width: 63%;
    height: 100%;
    box-sizing: border-box;
    flex-direction: column;
    align-items: center;
    padding-left: 280px;

    .logo {
      position: absolute;
      top: 48px;
      left: 0;
      display: flex;
      width: 200px;
      height: 50px;
      align-items: center;
      justify-content: center;
      border-radius: 0 30px 30px 0;
      background: @PrimaryColor;
    }

    .introduce {
      display: flex;
      flex: 1;
      flex-direction: column;
      place-content: flex-start center;

      .title {
        color: @PrimaryColor;
        font-size: 40px;
        font-weight: 700;
        line-height: 60px;
      }

      .highlight {
        background: linear-gradient(99deg, #0080ff 11.87%, #1146e1 100%);
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .description {
        margin-top: 4px;
        margin-bottom: 52px;
        color: rgb(0 0 0 / 60%);
        font-size: 22px;
      }
    }
  }

  .right {
    width: 37%;
    margin: auto;
    text-align: center;
  }
}
