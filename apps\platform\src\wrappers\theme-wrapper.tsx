import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import React from 'react';
import { Outlet } from 'umi';
import 'dayjs/locale/zh-cn';

import platformConfig from '@/platform.config';

dayjs.locale('zh-cn');

const App: React.FC = () => (
  <ConfigProvider theme={platformConfig.theme} locale={zhCN}>
    <Outlet />
  </ConfigProvider>
);

export default App;
