.nodeList {
  display: flex;
  height: 100%;
  box-sizing: border-box;
  flex-direction: column;
  padding: 20px;

  .nodeListHeader {
    padding: 12px 20px;
    border-radius: 8px;
    margin-bottom: 12px;
    background-color: #fff;
  }

  .content {
    flex:1;
    padding: 20px;
    border-radius: 8px;
    background-color: #fff;

    .idText {
      color: rgb(0 0 0 / 45%);
      font-size: 12px;
    }

    :global(.ant-table-thead) {
      height: 54px;
    }

    :global(.ant-table-row) {
      height: 62px;
    }
  }
}

.embeddedTag {
  border: 1px solid #cdfadf;
  border-radius: 4px;
  background-color: #ecfff4;
  color: rgb(0 0 0 / 88%);
  font-weight: 400;
}
