import * as monaco from 'monaco-editor';
import { useEffect, useRef, useState } from 'react';

import style from './index.less';

interface LogEditorProps {
  text: string;
}

const LogEditor: React.FC<LogEditorProps> = ({ text }: LogEditorProps) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [editor, setEditor] = useState<monaco.editor.IStandaloneCodeEditor>();

  useEffect(() => {
    if (containerRef.current) {
      const ins = monaco.editor.create(containerRef.current, {
        value: text,
        language: 'log',
        readOnly: true,
        theme: 'logview',
        minimap: {
          enabled: false,
        },
        fontSize: 12,
        lineHeight: 18,
        scrollBeyondLastLine: false, // 禁用超出最后一行的滚动
        automaticLayout: true, // 自动布局
        wordWrap: 'on',
        lineDecorationsWidth: 0,
        lineNumbersMinChars: 4,
        find: {
          seedSearchStringFromSelection: 'never',
          autoFindInSelection: 'never',
          addExtraSpaceOnTop: false,
        },
        contextmenu: false,
        wordBasedSuggestions: false,
        unicodeHighlight: {
          nonBasicASCII: false,
          invisibleCharacters: false,
          ambiguousCharacters: false,
          includeComments: false,
        },
      });
      setEditor(ins);
    }

    return () => {
      console.log('dispose');
      editor?.dispose();
      setEditor(undefined);
    };
  }, []);

  useEffect(() => {
    editor?.setValue(text);
    editor?.setScrollTop(0);
  }, [text]);

  return <div ref={containerRef} className={style.logEditorWrapper}></div>;
};

export default LogEditor;
