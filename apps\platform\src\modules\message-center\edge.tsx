import { SearchOutlined } from '@ant-design/icons';
import type { RadioChangeEvent, TabsProps } from 'antd';
import { Input, Tabs, Select, Radio, message, Space, Flex } from 'antd';
import classNames from 'classnames';
import { parse } from 'query-string';
import { useEffect, type ChangeEvent } from 'react';
import { history, useLocation } from 'umi';

import { Platform, hasAccess, isP2PWorkbench } from '@/components/platform-wrapper';
import { TabSearchBox } from '@/components/tab-search-box';
import { LoginService } from '@/modules/login/login.service';
import { P2pProjectListService } from '@/modules/p2p-project-list/p2p-project-list.service';
import { Model, getModel, useModel } from '@/util/valtio-helper';

import { HomeLayoutService } from '../layout/home-layout/home-layout.service';

import MessageTable from './component/table';
import styles from './index.less';
import { MessageInfoModal } from './message-info/message-info.view';
import {
  MessageActiveTabType,
  MessageService,
  MessageState,
  MessageStateObj,
  P2PSelectMessageOptions,
  SelectMessageOptions,
  SelectOptionsValueEnum,
} from './message.service';

const MessageComponent: React.FC = () => {
  const viewInstance = useModel(MessageModel);
  const { pathname } = useLocation();
  const { activeTab, changeTabs, messageService, filterState } = viewInstance;
  const items = [
    {
      value: MessageActiveTabType.PROCESS,
      label: '我处理的',
    },
    {
      value: MessageActiveTabType.APPLY,
      label: '我发起的',
    },
  ];

  useEffect(() => {
    const onViewMount = async () => {
      const { active, ownerId } = parse(window.location.search);
      if (ownerId) {
        viewInstance.ownerId = ownerId as string;
      }
      await changeTabs(
        (active as MessageActiveTabType) || MessageActiveTabType.PROCESS,
        pathname,
      );
      // P2P 工作台要求如果我处理的数量为0,则默认跳转到我发起的页面
      if (
        isP2PWorkbench(pathname) &&
        activeTab === MessageActiveTabType.PROCESS &&
        filterState === MessageState.ALL &&
        viewInstance.selectType === SelectOptionsValueEnum.ALL &&
        viewInstance.totalNum === 0
      ) {
        changeTabs(MessageActiveTabType.APPLY, pathname);
      }
      viewInstance.getProcessMessage();
      viewInstance.p2pProjectListService.P2pProjectCallBack(viewInstance.getList);
    };
    viewInstance.pageSize = isP2PWorkbench(pathname) ? 5 : 10;
    onViewMount();
  }, [pathname]);

  const onProcess = (params: API.VoteReplyRequest) => {
    viewInstance.processMessage(params, pathname);
  };

  const onShowDrawer = (voteId: string) => {
    const record = viewInstance.messageService.messageList.find(
      (i) => i.voteID === voteId,
    );
    if (record) viewInstance.showInfoDrawer(record);
  };

  return (
    <div className={styles.edgeMessageContent}>
      <TabSearchBox
        tabs={items}
        onTabChange={(value) => changeTabs(value as MessageActiveTabType, pathname)}
      >
        <Space size="middle">
          {!isP2PWorkbench(pathname) && (
            <>
              <span>消息搜索</span>
              <Input
                placeholder="搜索关键字"
                onChange={(e) => viewInstance.searchNode(e)}
                style={{ width: 200 }}
                suffix={
                  <SearchOutlined
                    style={{
                      color: '#aaa',
                    }}
                  />
                }
              />
            </>
          )}

          {((isP2PWorkbench(pathname) && activeTab !== MessageActiveTabType.APPLY) ||
            !isP2PWorkbench(pathname)) && (
            <>
              <span>消息类型</span>
              <Select
                defaultValue={SelectOptionsValueEnum.ALL}
                style={{ width: 180 }}
                onChange={(value) => {
                  viewInstance.changeSelect(value);
                  viewInstance.getList();
                }}
                options={
                  isP2PWorkbench(pathname) || hasAccess({ type: [Platform.AUTONOMY] })
                    ? P2PSelectMessageOptions
                    : SelectMessageOptions
                }
              />
            </>
          )}
        </Space>
      </TabSearchBox>
      <div className={styles.content}>
        <Radio.Group
          size="small"
          buttonStyle="solid"
          defaultValue={MessageState.PENDING}
          style={{ marginBottom: 16 }}
          onChange={(e: RadioChangeEvent) => {
            viewInstance.changefilterState(e.target.value);
            viewInstance.getList();
          }}
          value={viewInstance.filterState}
        >
          {/* {activeTab === MessageActiveTabType.APPLY && ( */}
          <Radio.Button value={MessageState.ALL}>全部</Radio.Button>
          {/* )} */}
          <Radio.Button value={MessageState.PENDING}>
            {activeTab === MessageActiveTabType.PROCESS
              ? `待处理(${messageService.processCount})`
              : '待处理'}
          </Radio.Button>
          <Radio.Button value={MessageState.PROCESS}>已处理</Radio.Button>
        </Radio.Group>
        <MessageTable
          type={activeTab}
          data={viewInstance.messageService.messageTableData}
          loading={viewInstance.messageService.loading}
          size="middle"
          pagination={{
            total: viewInstance.totalNum || 1,
            current: viewInstance.pageNumber,
            pageSize: viewInstance.pageSize,
            showTotal: (total) => `共 ${total} 条`,
            showSizeChanger: true,
            onChange: (page, pageSize) => {
              viewInstance.pageNumber = page;
              viewInstance.pageSize = pageSize;
              viewInstance.getList();
            },
          }}
          onProcess={onProcess}
          onShowDrawer={onShowDrawer}
        />
        {viewInstance.chickMessageRecord && (
          <MessageInfoModal
            open={viewInstance.showMessageInfoDrawer}
            onClose={() => (viewInstance.showMessageInfoDrawer = false)}
            data={viewInstance.chickMessageRecord}
            activeTab={viewInstance.activeTab}
            onOk={viewInstance.refreshList}
          />
        )}
      </div>
    </div>
  );
};

export class MessageModel extends Model {
  readonly messageService;
  readonly homeLayoutService;
  readonly loginService;
  readonly p2pProjectListService;

  constructor() {
    super();
    this.messageService = getModel(MessageService);
    this.homeLayoutService = getModel(HomeLayoutService);
    this.loginService = getModel(LoginService);
    this.p2pProjectListService = getModel(P2pProjectListService);
  }

  onViewUnMount(): void {
    this.showMessageInfoDrawer = false;
  }

  activeTab = MessageActiveTabType.PROCESS;

  filterState = MessageState.PENDING;

  selectType = SelectOptionsValueEnum.ALL;

  search = '';

  pageNumber = 1;

  pageSize = 10;

  totalNum = 0;

  comment = '';

  searchDebounce: number | undefined = undefined;

  showMessageInfoDrawer = false;

  chickMessageRecord: API.MessageVO | undefined = undefined;

  ownerId: string | undefined = undefined;

  resetPagination = (pathname: string) => {
    this.pageNumber = 1;
    this.pageSize = isP2PWorkbench(pathname) ? 5 : 10;
  };

  changeSelect = (value: SelectOptionsValueEnum) => {
    this.selectType = value;
  };

  changefilterState = (value: MessageState) => {
    this.pageNumber = 1;
    this.filterState = value;
  };

  changeTabs = async (key: MessageActiveTabType, pathname: string) => {
    this.activeTab = key;

    if (key === MessageActiveTabType.APPLY) {
      this.changefilterState(MessageState.ALL);
    } else {
      // this.changefilterState(MessageState.PENDING);
      this.changefilterState(MessageState.ALL);
    }
    this.resetPagination(pathname);
    await this.getList();
  };

  setComment = (value: string) => {
    this.comment = value;
  };

  searchNode = (e: ChangeEvent<HTMLInputElement>) => {
    this.search = e.target.value;
    clearTimeout(this.searchDebounce);
    this.searchDebounce = setTimeout(() => {
      this.pageNumber = 1;
      this.getList();
    }, 300) as unknown as number;
  };

  getList = async (getProcessMessage = true) => {
    const data = await this.messageService.getMessageList({
      page: this.pageNumber,
      size: this.pageSize,
      isInitiator: this.activeTab === MessageActiveTabType.APPLY ? true : false,
      ownerId: this.ownerId,
      isProcessed: MessageStateObj[this.filterState],
      type:
        this.selectType === SelectOptionsValueEnum.ALL ? undefined : this.selectType,
      keyWord: this.search,
    });
    this.totalNum = data?.total || 0;

    if (getProcessMessage) {
      if (this.activeTab === MessageActiveTabType.PROCESS) {
        this.getProcessMessage();
      }
    }
  };

  refreshList = () => {
    this.getList(false);
    this.getProcessMessage();
  };

  processMessage = async (params: API.VoteReplyRequest, pathname: string) => {
    const { status } = await this.messageService.process({
      action: params.action,
      reason: params.reason,
      voteId: params.voteId,
      voteParticipantId: this.ownerId,
    });
    if (status && status.code !== 0) {
      message.error(status.msg);
    } else {
      message.success('处理成功');
      this.refreshList();
      if (isP2PWorkbench(pathname)) {
        this.p2pProjectListService.getListProject();
      }
    }
  };

  getProcessMessage = async () => {
    if (!this.ownerId) return;
    const res = await this.messageService.getMessageCount(this.ownerId);
    if (res.status) {
      this.homeLayoutService.setMessageCount(res?.data || 0);
      this.messageService.processCount = res?.data || 0;
    }
  };

  showInfoDrawer = (record: API.MessageVO) => {
    this.chickMessageRecord = record;
    this.showMessageInfoDrawer = true;
  };
}

export default MessageComponent;
