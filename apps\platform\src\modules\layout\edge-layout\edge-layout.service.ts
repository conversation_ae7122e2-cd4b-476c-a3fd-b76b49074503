import type { ReactNode } from 'react';

import { Model } from '@/util/valtio-helper';

export const routeLabelMap = {
  home: '首页',
  'data-source': '数据源管理',
  'data-management-folder': '数据管理',
  'data-manager': '我的数据',
  'data-pool': '外部数据',
  'project-center': '项目中心',
  'cooperative-node': '合作节点',
  messages: '消息中心',
  logs: '日志中心',
  'my-project': '联合建模',
  prediction: '联合预测',
  psi: '隐私求交',
  taskDetail: '任务详情',
  dag: '模型空间',
  dataAuth: '授权管理',
  'my-node': '我的节点',
};

export class EdgeLayoutService extends Model {
  // logo边上的页面标题
  subTitle: string | ReactNode = '';

  bgClassName = 'homeBg';

  showBackButton = false;

  messageCount? = 0;

  routeKeys: string[] = [];

  public setSubTitle(title: string | ReactNode) {
    this.subTitle = title;
  }

  public setBgClassName(name: string) {
    this.bgClassName = name;
  }

  public setMessageCount(count: number) {
    this.messageCount = count;
  }
}
