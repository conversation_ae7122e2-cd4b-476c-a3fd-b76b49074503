.inputTitle {
  margin-bottom: 8px;
  color: #00000073;
  font-size: 12px;
  line-height: 22px;
}

.inputTableContent {
  :global(.ant-descriptions .ant-descriptions-row > td) {
    padding-bottom: 4px !important;
  }
}

.content {
  overflow: auto;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 16px 8px;
  margin-bottom: 12px;
  background-color: #fafafa;
}

.ellipsisText {
  overflow: hidden;
  width: 167px;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.copy {
  color: #1677ff;
  cursor: pointer;
}

.featureFull {
  width: 100%;
  white-space: wrap;
  word-break: break-all;
}

.screenFullcontent {
  overflow: auto;
  height: 100%;
  max-height: calc(100% - 250px);
  background: #fff;
}
