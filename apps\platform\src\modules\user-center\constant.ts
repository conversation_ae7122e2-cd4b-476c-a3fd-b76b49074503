import type { FormItemConfig } from '@/components/form-builder/search-form';
import { FormItemType } from '@/components/form-builder/search-form';

export const AddUserFormConfig: FormItemConfig[] = [
  {
    label: '账户',
    name: 'account',
    type: FormItemType.INPUT,
    placeholder: '账号可由字母/数字/下划线/特殊符号组成，2-48字符',
    rules: [
      {
        required: true,
        message: '请输入用户名',
      },
      {
        pattern: /^[a-zA-Z0-9_!@#$%^&*,.?":|+-]+$/,
        message: '请输入字母/数字/下划线/特殊符号',
      },
      {
        max: 48,
        min: 2,
        message: '字符数为2-48',
      },
    ],
  },
  {
    label: '用户名',
    name: 'username',
    type: FormItemType.INPUT,
    placeholder: '用户名可由中文/英文/数字/下划线/中划线组成，长度限制32',
    rules: [
      {
        required: true,
        message: '请输入用户名',
      },
      {
        max: 32,
        message: '最多输入32个字符',
      },
      {
        pattern: /^[a-zA-Z0-9_\u4e00-\u9fa5-]+$/,
        message: '请输入中文/英文/数字/下划线/中划线',
      },
    ],
  },
  {
    label: '密码',
    name: 'password',
    type: FormItemType.PASSWORD,
    placeholder: '需同时包含大小写字母、数字，不可与用户名相同，8-20字符',
    rules: [
      {
        required: true,
        message: '请输入密码',
      },
      {
        max: 20,
        min: 8,
        message: '密码长度为8-20',
      },
      {
        pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]+$/,
        message: '密码需同时包含大小写字母、数字',
      },
      ({ getFieldValue }) => ({
        validator(_, value) {
          if (!value || getFieldValue('username') !== value) {
            return Promise.resolve();
          }
          return Promise.reject(new Error('密码不能与用户名相同'));
        },
      }),
    ],
  },
  {
    label: '账号状态',
    name: 'status',
    type: FormItemType.SWITCH,
    defaultValue: true,
    checkedChildren: '正常',
    unCheckedChildren: '禁用',
  },
  {
    label: '角色',
    name: 'role',
    type: FormItemType.SELECT,
    placeholder: '请选择角色',
    defaultValue: 1,
    rules: [
      {
        required: true,
        message: '请选择角色',
      },
    ],
    options: [
      { label: '机构管理员', value: 1 },
      { label: '项目负责人', value: 2 },
    ],
  },
];
