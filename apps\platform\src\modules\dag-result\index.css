.resultDrawer {
  position: absolute;
}
.resultDrawer :global(.ant-table-cell) {
  padding: 6px 8px !important;
}
.resultDrawer :global .ant-drawer-content-wrapper {
  box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.15);
}
.resultDrawer :global .ant-drawer-title {
  font-size: 14px;
}
.resultDrawer :global .ant-drawer-body {
  padding: 12px 16px;
}
.resultDrawer :global .ant-drawer-header {
  padding: 10px 12px;
  border-bottom: 1px solid #eee;
}
.resultDrawer:focus {
  outline: none;
}
.resultDrawer .actionIcon {
  color: rgba(0, 10, 26, 0.68);
}
.resultDrawer .actionIcon:hover {
  color: #9a0000;
}
.report {
  padding: 11px 12px 4px 11px;
  margin-bottom: 16px;
  background-color: rgba(0, 0, 0, 0.02);
  font-size: 12px;
}
.report :global(.ant-typography) {
  display: flex;
  margin-bottom: 0;
}
.report :global .ant-table-thead,
.report :global .ant-table-tbody {
  font-size: 12px;
}
.report .item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
}
.report .item .name {
  margin-right: 6px;
  color: rgba(0, 0, 0, 0.85);
  font-weight: 600;
}
.report .item :global(.ant-tag) {
  border-radius: 10px;
  color: #9a0000 !important;
  background-color: #faf3f3 !important;
}
.report .timeLabel {
  color: rgba(0, 0, 0, 0.45);
}
.report .time {
  margin-bottom: 8px;
}
.report .ruleContent {
  display: flex;
  align-items: baseline;
}
.report .modelContent {
  display: flex;
  align-items: baseline;
}
.tableHeader {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
}
.tableHeader .fullScreenText {
  margin-left: 8px;
  color: rgba(0, 10, 26, 0.68);
  cursor: pointer;
  font-weight: 400;
}
.tableHeader .right {
  font-size: 12px;
}
.tabsTable {
  position: relative;
}
.tabsTable :global .ant-tabs-nav::before {
  border-bottom: none !important;
}
.tabsTable :global .ant-table-container {
  border-radius: 0;
}
.tabsTable :global(.ant-tabs-tab) {
  padding: 0 0 8px !important;
}
.tabsTable :global(.ant-tabs-nav) {
  margin-right: 90px;
  margin-bottom: 8px !important;
}
.result :global .ant-table-thead,
.result :global .ant-table-tbody {
  font-size: 12px;
}
.fullScreenContentPage {
  overflow: auto;
  padding-bottom: 24px;
  background-color: #fff;
}
.fullScreenContentWrap {
  padding: 0 24px;
}
.fullScreenHeader {
  display: flex;
  height: 56px;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  border-bottom: 1px solid #eee;
  margin-bottom: 16px;
}
.fullScreenHeader .title {
  color: #1d2129;
  font-size: 20px;
  font-weight: 500;
  letter-spacing: 0;
}
.fullScreenHeader .exit {
  color: rgba(0, 10, 26, 0.88);
  cursor: pointer;
  font-size: 12px;
  font-weight: 400;
}
.fullScreenHeader .exit:hover {
  color: #9a0000;
}
.fullScreenHeader .close {
  cursor: pointer;
}
.resultModalTitle {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.resultModalTitle .resultExitFullScreen {
  margin-top: 6px;
  margin-right: 8px;
  color: rgba(0, 10, 26, 0.88);
  cursor: pointer;
  font-size: 12px;
  font-weight: 400;
}
.resultModalTitle .resultExitFullScreen:hover {
  color: #9a0000;
}
.model :global(.ant-alert-icon) {
  margin-bottom: 20px !important;
}
.model .list {
  display: flex;
  align-items: flex-start;
  padding: 8px;
  border-radius: 4px;
  margin: 8px 0;
  background-color: #f9fafa;
}
.model .listError {
  padding-bottom: 0;
  margin-bottom: 0;
}
.model .downloadContent {
  background-color: #f9fafa;
}
.model .downloadRejectReason .rejectBottom {
  padding: 4px 8px;
}
.model .rightText {
  margin-left: 16px;
}
.applyDownloadBtn {
  font-size: 12px !important;
}
.applyDownloadContent .rightText {
  margin-left: 16px;
  font-size: 12px !important;
}
