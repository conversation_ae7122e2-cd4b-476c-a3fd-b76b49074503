/* eslint-disable */
import request from 'umi-request';

/** Obtain the list of data sources that are controlled by the node as project participants Obtain the list of data sources that are controlled by the node as project participants POST /api/v1alpha1/project/datasource/list */
export async function projectGraphDomainDataSourceList(
  body: API2.GetProjectGraphDomainDataSourceRequest,
  options?: { [key: string]: any },
) {
  return request<API2.SecretPadResponseSetProjectGraphDomainDataSourceVO>(
    '/api/v1alpha1/project/datasource/list',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** add datatable to the project add datatable to the project POST /api/v1alpha1/project/datatable/add */
export async function addProjectDatatable(
  body: API2.AddProjectDatatableRequest,
  options?: { [key: string]: any },
) {
  return request<API2.SecretPadResponseObject>('/api/v1alpha1/project/datatable/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 添加授权数据表到PSI项目类型 POST /api/v1alpha1/project/datatable/add_psi_auth */
export async function addDatatablePsiToProject(
  body: API2.AddPsiDatatableGrantRequest,
  options?: { [key: string]: any },
) {
  return request<API2.SecretPadResponseVoid>(
    '/api/v1alpha1/project/datatable/add_psi_auth',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 添加授权数据表到联合预测项目类型 POST /api/v1alpha1/project/datatable/add_fi_auth */
export async function addDatatableFiToProject(
  body: API2.AddPsiDatatableGrantRequest,
  options?: { [key: string]: any },
) {
  return request<API2.SecretPadResponseVoid>(
    '/api/v1alpha1/project/datatable/add_fi_auth',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** delete datatable and cancel datatable authorization in the project delete datatable and cancel datatable authorization in the project POST /api/v1alpha1/project/datatable/delete */
export async function deleteProjectDatatable(
  body: API2.DeleteProjectDatatableRequest,
  options?: { [key: string]: any },
) {
  return request<API2.SecretPadResponseObject>(
    '/api/v1alpha1/project/datatable/delete',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 从PSI项目类型中删除授权数据表 POST /api/v1alpha1/project/datatable/delete_psi_auth */
export async function deleteDatatableFromPsiProject(
  body: API2.DeletePsiDatatableRequest,
  options?: { [key: string]: any },
) {
  return request<API2.SecretPadResponseVoid>(
    '/api/v1alpha1/project/datatable/delete_psi_auth',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 从联合预测项目类型中删除授权数据表 POST /api/v1alpha1/project/datatable/delete_fi_auth */
export async function deleteDatatableFromFiProject(
  body: API2.DeletePsiDatatableRequest,
  options?: { [key: string]: any },
) {
  return request<API2.SecretPadResponseVoid>(
    '/api/v1alpha1/project/datatable/delete_fi_auth',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** query project datatable detail query project datatable detail POST /api/v1alpha1/project/datatable/get */
export async function getProjectDatatable(
  body: API2.GetProjectDatatableRequest,
  options?: { [key: string]: any },
) {
  return request<API2.SecretPadResponseObject>('/api/v1alpha1/project/datatable/get', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 更新PSI项目类型授权的数据表配置 POST /api/v1alpha1/project/datatable/update_psi_auth */
export async function updatePsiDatatableConfig(
  body: API2.AddPsiDatatableGrantRequest,
  options?: { [key: string]: any },
) {
  return request<API2.SecretPadResponseVoid>(
    '/api/v1alpha1/project/datatable/update_psi_auth',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** query project detail query project detail POST /api/v1alpha1/project/get */
export async function getProject(
  body: API2.GetProjectRequest,
  options?: { [key: string]: any },
) {
  return request<API2.SecretPadResponseProjectVO>('/api/v1alpha1/project/get', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 POST /api/v1alpha1/project/getOutTable */
export async function getProjectAllOutTable(
  body: API2.GetProjectGraphRequest,
  options?: { [key: string]: any },
) {
  return request<API2.SecretPadResponseProjectOutputVO>(
    '/api/v1alpha1/project/getOutTable',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** project job detail project job detail POST /api/v1alpha1/project/job/get */
export async function getJob(
  body: API2.GetProjectJobRequest,
  options?: { [key: string]: any },
) {
  return request<API2.SecretPadResponseProjectJobVO>('/api/v1alpha1/project/job/get', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** project job list project job list POST /api/v1alpha1/project/job/list */
export async function listJob1(
  body: API2.ListProjectJobRequest,
  options?: { [key: string]: any },
) {
  return request<API2.SecretPadResponsePageResponseProjectJobSummaryVO>(
    '/api/v1alpha1/project/job/list',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** stop project job stop project job POST /api/v1alpha1/project/job/stop */
export async function stopJob(
  body: API2.StopProjectJobTaskRequest,
  options?: { [key: string]: any },
) {
  return request<API2.SecretPadResponseVoid>('/api/v1alpha1/project/job/stop', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** project job task logs project job task logs POST /api/v1alpha1/project/job/task/logs */
export async function getJobLog(
  body: API2.GetProjectJobTaskLogRequest,
  options?: { [key: string]: any },
) {
  return request<API2.SecretPadResponseGraphNodeTaskLogsVO>(
    '/api/v1alpha1/project/job/task/logs',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** project job task output project job task output POST /api/v1alpha1/project/job/task/output */
export async function getJobTaskOutput(
  body: API2.GetProjectJobTaskOutputRequest,
  options?: { [key: string]: any },
) {
  return request<API2.SecretPadResponseGraphNodeOutputVO>(
    '/api/v1alpha1/project/job/task/output',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** update project table config update project table config POST /api/v1alpha1/project/update/tableConfig */
export async function updateProjectTableConfig(
  body: API2.AddProjectDatatableRequest,
  options?: { [key: string]: any },
) {
  return request<API2.SecretPadResponseVoid>(
    '/api/v1alpha1/project/update/tableConfig',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}
