import { parse } from 'query-string';

import API from '@/services/secretpad';
import API2 from '@/services/secretpad2';
import { Model } from '@/util/valtio-helper';

export class DataPoolService extends Model {
  async getDataList(params: {
    pageNum: number;
    pageSize: number;
    nodeId: string;
    status?: string;
    search?: string;
    typeFilters?: string[];
    nodeNamesFilter?: string;
  }) {
    const { data } = await API2.DatatableController.listGrantedDatatables({
      pageSize: params.pageSize,
      pageNumber: params.pageNum,
      datatableNameFilter: params.search,
      statusFilter: params.status,
      nodeId: params.nodeId,
      types: params.typeFilters,
    });
    return data;
  }

  /** 获取本方机构与之授权成功的节点 且合作节点路由是可用状态 */
  getCurrentNodeInfo = async () => {
    const { ownerId } = parse(window.location.search);
    const { data } = await API.NodeRouteController.page({
      page: 1,
      size: 1000,
      search: '',
      sort: {},
      ownerId: ownerId as string,
    });
    return (data?.list || []).map((item: API.NodeRouterVO) => ({
      nodeId: item.dstNode?.nodeId || '',
      nodeName: item.dstNode?.nodeName || '',
      instId: item.dstNode?.instId || '',
      instName: item.dstNode?.instName || '',
    }));
  };
}
