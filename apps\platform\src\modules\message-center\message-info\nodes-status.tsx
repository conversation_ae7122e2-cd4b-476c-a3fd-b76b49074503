import { Badge, Space } from 'antd';

import { ReactComponent as IconArrow } from '@/assets/icon-arrow.svg';
import { ReactComponent as InstIcon } from '@/assets/inst.icon.svg';
import { StatusTag } from '@/components/status-tag';
import { convertToNodeData } from '@/modules/p2p-project-detail/helper';

import styles from './index.less';
import { getStatus } from './info-content';

export const NodesStatus = ({ info }: { info: Record<string, any> }) => {
  const { nodeData } = convertToNodeData(info);
  console.log(nodeData, 'participantNodeInstVOS');
  return nodeData?.length > 0 ? (
    <div className={styles.nodesStatusWrapper}>
      {nodeData.map((item: Record<string, any>, index: number) => {
        const data = item.data;
        const { statusType, statusText } = getStatus(data.action);
        return (
          <div className={styles.nodeBoxWrapper} key={data.instId}>
            <div className={styles.nodeBox}>
              <div className={styles.nodeBoxTitle}>
                <Space style={{ color: '#4762B2' }}>
                  <Badge color="#4762B2" />
                  <span>{data.isInitiator ? '发起方' : '受邀方'}</span>
                </Space>
                <StatusTag type={statusType} text={statusText} />
              </div>
              <div className={styles.nodeBoxContent}>
                <div className={styles.nodeBoxContentItem}>
                  <span className={styles.nodeId}>{data.nodeName}</span>
                  {data.isOurNode && <span className={styles.myNode}>(我的)</span>}
                </div>
                <div className={styles.instBox}>
                  <Space>
                    <InstIcon />
                    <span>{data.instName}</span>
                  </Space>
                </div>
              </div>
            </div>
            {index !== nodeData.length - 1 && <IconArrow />}
          </div>
        );
      })}
    </div>
  ) : null;
};
