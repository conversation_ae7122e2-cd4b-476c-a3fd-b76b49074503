@import url('@/variables.less');

.content {
  .nodeName {
    display: flex;
    overflow: hidden;
    height: 22px;
    align-items: center;
    color: rgb(0 0 0 / 80%);
    font-size: 14px;
    font-weight: 400;
    gap: 4px;
    letter-spacing: 0;
    line-height: 22px;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  :global(.ant-tag) {
    line-height: 18px;
    padding-inline: 3px;
  }

  .tagApply {
    width: 28px;
    height: 20px;
    border: 1px solid rgb(0 0 0 / 6%);
    border-radius: 4px;
    background-image: linear-gradient(180deg, #fafdff 0%, #e6f4ff 100%);
    font-size: 10px;
  }

  .applyContent {
    margin-bottom: 8px;
    font-size: 14px;

    .label {
      color: rgb(0 0 0 / 40%);
    }
  }

  .processContent {
    margin-bottom: 8px;
    font-size: 14px;

    .label {
      color: rgb(0 0 0 / 40%);
    }

    .agree {
      padding: 2px 4px;
      border: 1px solid @PrimaryColor;
      border-radius: 2px;
      color: @PrimaryColor;
      cursor: pointer;
      font-size: 12px;
    }

    .agreeTag {
      border: solid 1px #23b65f;
      margin-right: 0;
      background-color: #ecfff4;
      background-image: none;
      color: #23b65f;
    }

    .reviewingTag {
      border: solid 1px #9a0000;
      margin-right: 0;
      background-color: #f0f5ff;
      background-image: none;
      color: #9a0000;
    }

    .rejectedTag {
      border: solid 1px red;
      margin-right: 0;
      background-color: #fff0f0;
      background-image: none;
      color: red;
    }

    .reject {
      padding: 2px 4px;
      border: 1px solid @FailedColor;
      border-radius: 2px;
      color: @FailedColor;
      cursor: pointer;
      font-size: 12px;
    }

    .tagProcess {
      width: 28px;
      height: 20px;
      border: 1px solid rgb(0 0 0 / 6%);
      border-radius: 4px;
      background-image: linear-gradient(180deg, #fff 0%, #eee 100%);
      font-size: 10px;
    }
  }
}
