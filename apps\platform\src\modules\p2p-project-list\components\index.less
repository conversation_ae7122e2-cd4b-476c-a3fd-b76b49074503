.updateContent {
  margin-top: 24px;
}

.projectTypeTag {
  width: 80px;
  height: 20px;
  border: 1px solid rgb(0 0 0 / 6%);
  border-radius: 4px;
  margin-right: 0;
  color: rgb(0 10 26 / 88%);
  font-size: 10px;
  font-weight: 400;
}

.intoProjectDisabled {
  flex: 1;

  :global(.ant-btn) {
    width: 100% !important;
  }

  :global(.ant-btn-primary:disabled) {
    background-color: #9a0000;
    color: #fff;
    opacity: 0.4;
  }
}

.projectListBtns {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .timeStr {
    color: rgb(0 0 0 / 40%);
    font-size: 12px;
    font-weight: 400;
    letter-spacing: 0;
    line-height: 18px;
  }
}
