import { message } from 'antd';
import { parse } from 'query-string';

import API from '@/services/secretpad';
import API2 from '@/services/secretpad2';
import { Model } from '@/util/valtio-helper';

export interface AllocatedNode {
  nodeId: string;
  nodeName: string;
  instId: string;
  instName: string;
}

export class ProjectPSIService extends Model {
  /** 获取本方机构与之授权成功的节点 且合作节点路由是可用状态 */
  getAllocatedNodes = async () => {
    const { ownerId } = parse(window.location.search);
    const { data } = await API.NodeRouteController.page({
      page: 1,
      size: 1000,
      search: '',
      sort: {},
      ownerId: ownerId as string,
    });
    const allocatedNodes: AllocatedNode[] = [];
    allocatedNodes.push({
      nodeId: data?.list?.[0].dstNode?.nodeId || '',
      nodeName: data?.list?.[0].dstNode?.nodeName || '',
      instId: data?.list?.[0].dstNode?.instId || '',
      instName: data?.list?.[0].dstNode?.instName || '',
    });
    (data?.list || []).forEach((item: API.NodeRouterVO) => {
      if (item.status === 'Succeeded') {
        // if (!allocatedNodes.find((node) => node.nodeId === item.srcNode?.nodeId)) {
        allocatedNodes.push({
          nodeId: item.srcNode?.nodeId || '',
          nodeName: item.srcNode?.nodeName || '',
          instId: item.srcNode?.instId || '',
          instName: item.srcNode?.instName || '',
        });
        // }
      }
    });
    console.log(allocatedNodes, 'allocatedNodes');
    return allocatedNodes;
  };

  createPSIJob = async (data: API2.PSIJobCreateRequest) => {
    const res = await API2.PSIController.createPSIJob(data);
    return res;
  };

  createPSIApproval = async (data: API2.PSIApprovalCreateRequest) => {
    const res = await API.ApprovalController.create(data);
    return res;
  };

  getPSIJobList = async (data: API2.PSIJobListRequest) => {
    const res = await API2.PSIController.getPSIJobList(data);
    if (res.status?.code !== 0) {
      message.error(res.status?.msg);
      return null;
    }
    return res.data;
  };

  getPSIDatatables = async (data: { nodeId: string }) => {
    const res = await API2.PSIController.getPSIDatatables(data);
    if (res.status?.code !== 0) {
      message.error(res.status?.msg);
      return null;
    }
    return res.data;
  };

  deletePSIJob = async (data: { jobId: string; nodeId: string }) => {
    const res = await API2.PSIController.deletePSIJob(data);
    if (res.status?.code === 0) {
      message.success('删除成功');
    } else {
      message.error(res.status?.msg);
    }
  };

  pausePSIJob = async (data: { jobId: string; nodeId: string }) => {
    const res = await API2.PSIController.stopPSIJob(data);
    if (res.status?.code === 0) {
      message.success('暂停成功');
    } else {
      message.error(res.status?.msg);
    }
  };

  restartPSIJob = async (data: { jobId: string; nodeId: string }) => {
    const res = await API2.PSIController.restartPSIJob(data);
    if (res.status?.code === 0) {
      message.success('重启成功');
    } else {
      message.error(res.status?.msg);
    }
  };

  cancelPSIJob = async (data: { jobId: string; nodeId: string }) => {
    const res = await API2.PSIController.cancelPSIJob(data);
    if (res.status?.code === 0) {
      message.success('取消成功');
    } else {
      message.error(res.status?.msg);
    }
  };

  getPSIJobStatus = async (data: { jobIds: string[] }) => {
    const res = await API2.PSIController.getPSIJobStatus(data);
    return res;
  };

  download = async (data: {
    jobId: string;
    nodeId: string;
    domainDataId: string;
    filename: string;
  }) => {
    const token = localStorage.getItem('User-Token') || '';
    fetch(`/api/v1alpha1/psi/job/downloadResult`, {
      method: 'POST',
      mode: 'cors',
      cache: 'no-cache',
      credentials: 'include',
      headers: {
        'content-type': 'application/json',
        'User-Token': token,
      },

      body: JSON.stringify({
        jobId: data.jobId,
        nodeId: data.nodeId,
        domainDataId: data.domainDataId,
      }),
    })
      .then((res) => {
        res.blob().then((blob) => {
          const blobObj = new Blob(['\ufeff', blob], {
            type: 'text/plain;charset=utf-8',
          });

          const disposition = res.headers.get('Content-Disposition');
          let filename = '';
          const filenameRegex = /filename[^;=\n]*=[^'"]*['"]*((['"]).*?\2|[^;\n]*)/;
          const matches = filenameRegex.exec(disposition || '');
          if (matches != null && matches[1]) {
            filename = matches[1].replace(/['"]/g, '');
          }
          const a = document.createElement('a');
          document.body.appendChild(a); //兼容火狐，将a标签添加到body当中
          const url = window.URL.createObjectURL(blobObj); // 获取 blob 本地文件连接 (blob 为纯二进制对象，不能够直接保存到磁盘上)
          a.href = url;
          a.download = filename;
          a.click();
          a.remove(); //移除a标签
          window.URL.revokeObjectURL(url);
          message.success('下载完成');
        });
      })
      .catch((err) => {
        message.error(err);
      });
  };

  getPSILogs = async (data: { jobId: string; nodeId: string }) => {
    const res = await API2.PSIController.getPSILogs(data);
    return res;
  };

  getPSILogDetails = async (data: { jobId: string; nodeId: string }) => {
    const res = await API2.PSIController.getPSILogDetails(data);
    return res;
  };
}
