import type { FormInstance } from 'antd';
import { <PERSON><PERSON>, <PERSON><PERSON>, Modal } from 'antd';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import { useEffect, useRef, useState } from 'react';

import { AutoModal } from '@/components/auto-modal';
import CommonTable from '@/components/common-table';
import FormBuilder from '@/components/form-builder/form';
import { TabSearchBox } from '@/components/tab-search-box';
import type { LogListQueryParams, LogListVO } from '@/services/secretpad/LogController';
import { getModel, Model, useModel } from '@/util/valtio-helper';

import { AdminLogDetail } from './components/admin-log-detail';
import LogEditor from './components/log-editor';
import { NodeLogList, formatLog } from './components/status-list';
import {
  LogListColumns,
  AdminLogListColumns,
  LogListSearchConfig,
  AdminLogListSearchConfig,
} from './constant';
import type { AdminLogListData, LogListData } from './constant';
import styles from './index.less';
import { LogListService } from './log-list.service';

// 判断两个数组之间是否存在交集
const isIntersect = (arr1: string[], arr2: string[]) => {
  return arr1.some((item) => arr2.includes(item));
};

const needDebounceKeys = ['userId', 'message', 'clientIp'];

const LogListComponent: React.FC = () => {
  const loglistModel = useModel(LogListModel);
  const searchFormRef = useRef<{ form: FormInstance }>();
  const [visible, setVisible] = useState(false);
  const [open, setOpen] = useState(false);
  const [adminOpen, setAdminOpen] = useState(false);
  const [adminLogDetail, setAdminLogDetail] = useState<AdminLogListData>();
  const [log, setLog] = useState('');
  const [logDetail, setLogDetail] = useState<LogListData['aggregatedLogs']>([]);
  const onClose = () => {
    setVisible(false);
  };
  const [tableKey, setTableKey] = useState('sys');
  const [columns, setColumns] = useState([...LogListColumns]);
  const [searchConfig, setSearchConfig] = useState(LogListSearchConfig);

  const tabs = [
    { label: '管理日志', value: 'admin' },
    { label: '系统日志', value: 'sys' },
  ];

  const onSearch = (values: LogListQueryParams) => {
    loglistModel.pageObj.current = 1;
    loglistModel.searchLogList({
      ...values,
    });
  };

  const onTableChange = () => {
    const values = searchFormRef.current?.form.getFieldsValue() || {};
    loglistModel.getLogList({ ...values });
  };

  useEffect(() => {
    const initDateRange: [Dayjs, Dayjs] = [dayjs().subtract(6, 'day'), dayjs()];
    loglistModel.getLogList({
      dateRange: initDateRange,
    });
  }, []);

  useEffect(() => {
    const tempColumns =
      loglistModel.logType === 'admin' ? [...AdminLogListColumns] : [...LogListColumns];
    const tempSearchConfig =
      loglistModel.logType === 'admin'
        ? [...LogListSearchConfig, ...AdminLogListSearchConfig]
        : [...LogListSearchConfig];
    setColumns([
      ...tempColumns,
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        render: (_, record) => (
          <Button
            type="link"
            onClick={() => {
              if (loglistModel.logType === 'admin') {
                setAdminLogDetail(record as AdminLogListData);
                setAdminOpen(true);
              } else if (record.errMsg) {
                setOpen(true);
                setLog(formatLog(record.errMsg));
              } else {
                setLogDetail(record.aggregatedLogs || []);
                setVisible(true);
              }
            }}
          >
            查看
          </Button>
        ),
      },
    ]);
    setSearchConfig(tempSearchConfig);
  }, [loglistModel.logType]);

  const onTabChange = (val: string) => {
    loglistModel.listData = [];
    loglistModel.logType = val;
    setTableKey(val);
    const values = searchFormRef.current?.form.getFieldsValue() || {};
    // setColumns(val === 'admin' ? [...AdminLogListColumns] : [...LogListColumns]);
    loglistModel.pageObj.current = 1;
    loglistModel.getLogList({
      ...values,
    });
  };

  const onValuesChange = (
    changedValues: Record<string, any>,
    allValues: Record<string, any>,
  ) => {
    loglistModel.pageObj.current = 1;
    if (isIntersect(needDebounceKeys, Object.keys(changedValues))) {
      onSearch(allValues as LogListQueryParams);
    } else {
      loglistModel.getLogList(allValues as LogListQueryParams);
    }
  };

  const onReset = () => {
    searchFormRef.current?.form.resetFields();
    loglistModel.getLogList({
      ...(searchFormRef.current?.form.getFieldsValue() || {}),
    });
  };

  return (
    <div className={styles.logList}>
      {/* {memoSearchForm} */}
      <TabSearchBox
        tabs={tabs}
        onTabChange={(val) => onTabChange(val)}
        showReset
        onReset={onReset}
      >
        <FormBuilder
          ref={searchFormRef}
          config={searchConfig}
          formConfig={{
            layout: 'inline',
          }}
          onValuesChange={onValuesChange}
        />
      </TabSearchBox>
      <div className={styles.content}>
        <CommonTable
          key={tableKey}
          loading={loglistModel.logListService.logListLoading}
          dataSource={loglistModel.listData}
          columns={columns}
          size="small"
          pagination={{
            total: loglistModel.pageObj.total,
            current: loglistModel.pageObj.current,
            pageSize: loglistModel.pageObj.pageSize,
            onChange(page, pageSize) {
              loglistModel.pageObj.current = page;
              loglistModel.pageObj.pageSize = pageSize;
              onTableChange();
            },
            showTotal: (total: number) => `共 ${total} 条`,
            showSizeChanger: true,
          }}
          rowKey="id"
        ></CommonTable>
      </div>
      <AutoModal
        open={visible}
        title="日志详情"
        width={600}
        zIndex={999}
        onCancel={onClose}
        footer={false}
      >
        <NodeLogList
          list={logDetail}
          onviewDetail={(detail) => {
            setOpen(true);
            setLog(detail);
          }}
        />
      </AutoModal>
      <AutoModal
        title="任务日志详情"
        open={open}
        footer={false}
        mask={false}
        width={800}
        zIndex={1000}
        onCancel={() => setOpen(false)}
      >
        <div style={{ height: '600px' }}>
          <LogEditor text={log} />
        </div>
      </AutoModal>
      <Modal
        title="管理日志详情"
        open={adminOpen}
        footer={false}
        width={700}
        zIndex={1000}
        onCancel={() => setAdminOpen(false)}
      >
        <div>{adminLogDetail && <AdminLogDetail data={adminLogDetail} />}</div>
      </Modal>
    </div>
  );
};

export class LogListModel extends Model {
  listQueryLoading = false;
  listData: Partial<LogListData>[] = [];
  searchDebounce: number | undefined = undefined;
  pageObj = {
    pageSize: 10,
    current: 1,
    total: 0,
  };

  logType = 'admin';
  readonly logListService;

  constructor() {
    super();
    this.logListService = getModel(LogListService);
  }

  formatListData(logs: LogListVO[]): Partial<LogListData>[] {
    return logs.map((item) => {
      const details = item.message;
      try {
        const data: LogListData = JSON.parse(details);
        let nodeDetails: LogListData['aggregatedLogs'] = [];
        if (data.aggregatedLogs) {
          Object.values(data.aggregatedLogs).forEach((log) => {
            nodeDetails = nodeDetails.concat(log);
          });
        }

        return {
          ...item,
          projectId: data.projectId,
          projectName: data.projectName,
          jobType: data.jobType,
          jobId: data.jobId,
          jobName: data.jobName,
          jobStatus: data.jobStatus,
          aggregatedLogs: nodeDetails,
          errMsg: data.errMsg,
          toStatus: data.toStatus,
        };
      } catch {
        console.log('parse faild', details);
        return {
          ...item,
        };
      }
    });
  }

  async getLogList(params: LogListQueryParams) {
    const requestFn =
      this.logType === 'sys'
        ? this.logListService.getSysLogList
        : this.logListService.getAdminLogList;
    const {
      data: { logs, totalCount },
    } = await requestFn({
      ...params,
      page: this.pageObj.current,
      pageSize: this.pageObj.pageSize,
      dateRange: params.dateRange
        ?.map((item: Dayjs) => dayjs(item).format('YYYY-MM-DD'))
        .join(','),
    });
    this.listData = this.formatListData(logs);
    this.pageObj.total = totalCount;
  }

  searchLogList(params: LogListQueryParams) {
    clearTimeout(this.searchDebounce);
    this.searchDebounce = setTimeout(() => {
      this.getLogList(params);
    }, 300) as unknown as number;
  }
}

export default LogListComponent;
