declare namespace API2 {
  type AbstractInitiatingTypeMessage = true;

  type AbstractVoteConfig = true;

  type AbstractVoteTypeMessage = true;

  type AddProjectDatatableRequest = {
    /** project id */
    projectId: string;
    /** node id */
    nodeId: string;
    /** datatable id */
    datatableId: string;
    /** datatable column config list */
    configs?: TableColumnConfigParam[];
    /** tee node id */
    teeNodeId?: string;
    /** datasource id */
    datasourceId?: string;
    /** table type */
    type?: string;
  };

  type AddPsiDatatableGrantRequest = {
    /** project type */
    projectType: string;
    /** node id */
    nodeId: string;
    /** datatable id */
    datatableId: string;
    /** grant node id */
    grantNodeId?: string;
    /** column configs */
    configs?: TableColumnConfigParam[];
    /** tee node id */
    teeNodeId?: string;
    /** datasource id */
    datasourceId?: string;
    /** table type */
    type?: string;
  };

  type AllDatatableListVO = {
    /** datatable view object list */
    datatableNodeVOList?: DatatableNodeVO[];
    /** the total count of datatable */
    totalDatatableNums?: number;
  };

  type AllNodeResultsListVO = {
    nodeAllResultsVOList?: NodeAllResultsVO[];
    totalNodeResultNums?: number;
  };

  type ArchiveProjectRequest = {
    projectId: string;
  };

  type AuthProjectVO = {
    /** project id */
    projectId?: string;
    /** project name */
    name?: string;
    /** compute mode */
    computeMode?: string;
    /** association key list */
    associateKeys?: string[];
    /** group key list */
    groupKeys?: string[];
    /** label key list */
    labelKeys?: string[];
    /** authorized time */
    gmtCreate?: string;
  };

  type CloudGraphNodeTaskLogsVO = {
    status?: 'STAGING' | 'INITIALIZED' | 'RUNNING' | 'STOPPED' | 'SUCCEED' | 'FAILED';
    logs?: string[];
    config?: boolean;
    nodeParties?: NodeSimpleInfo[];
  };

  type CompListVO = {
    name?: string;
    desc?: string;
    version?: string;
    comps?: ComponentSummaryDef[];
  };

  type ComponentSummaryDef = {
    /** namespace of the component */
    domain?: string;
    /** component name, should be unique among all components of the same domain */
    name?: string;
    /** component description */
    desc?: string;
    /** component version */
    version?: string;
  };

  type ComponentVersion = {
    /** teeDmImage */
    teeDmImage?: string;
    /** teeAppImage */
    teeAppImage?: string;
    /** capsuleManagerSimImage */
    capsuleManagerSimImage?: string;
    /** secretpadImage */
    secretpadImage?: string;
    /** secretflowServingImage */
    secretflowServingImage?: string;
    /** kusciaImage */
    kusciaImage?: string;
    /** secretflowImage */
    secretflowImage?: string;
    /** dataProxyImage */
    dataProxyImage?: string;
    /** scqlImage */
    scqlImage?: string;
  };

  type CreateApprovalRequest = {
    initiatorId: string;
    voteType: string;
    voteConfig?: AbstractVoteConfig;
  };

  type CreateDataRequest = {
    /** node id */
    nodeId: string;
    /** data file name */
    name: string;
    /** real name of the file */
    realName?: string;
    /** specific table name */
    tableName: string;
    /** datatable description */
    description?: string;
    /** datasource type */
    datasourceType: string;
    /** datasource name */
    datasourceName: string;
    /** datatable schema */
    datatableSchema?: DatatableSchema[];
    nullStrs?: string[];
  };

  type CreateDatasourceRequest = {
    ownerId: string;
    nodeIds: string[];
    type: string;
    name: string;
    dataSourceInfo: DataSourceInfo;
  };

  type CreateDatasourceVO = {
    datasourceId?: string;
    failedCreatedNodes?: Record<string, any>;
  };

  type CreateDatatableRequest = {
    ownerId: string;
    nodeIds: string[];
    datatableName: string;
    datasourceId: string;
    datasourceName: string;
    datasourceType: string;
    desc?: string;
    relativeUri: string;
    columns: TableColumnVO[];
    partition?: OdpsPartitionRequest;
    nullStrs?: string[];
  };

  type CreateDatatableVO = {
    failedCreatedNodes?: Record<string, any>;
    dataTableNodeInfos?: DataTableNodeInfo[];
  };

  type CreateFeatureDatasourceRequest = {
    ownerId: string;
    /** The node ID to which the data belongs. In p2p mode, there may be more than one node ID. */
    nodeIds: string[];
    featureTableName: string;
    type: string;
    desc?: string;
    url: string;
    columns: TableColumnVO[];
    datasourceId?: string;
  };

  type CreateGraphRequest = {
    projectId: string;
    name: string;
    templateId?: string;
    nodes?: GraphNode[];
    edges?: GraphEdge[];
  };

  type CreateGraphVO = {
    graphId?: string;
  };

  type CreateModelServingRequest = {
    modelId: string;
    projectId: string;
    partyConfigs?: PartyConfig[];
  };

  type CreateNodeRequest = {
    /** node name */
    name: string;
    /** node feature indicates by bit, bit0 - mpc | bit1 - tee | bit2 mpc&tee */
    mode: number;
  };

  type CreateProjectRequest = {
    /** project name */
    name: string;
    /** project description */
    description?: string;
    /** computeMode */
    computeMode: string;
    /** teeNodeId */
    teeNodeId: string;
    computeFunc?: string;
  };

  type CreateProjectVO = {
    /** project id */
    projectId?: string;
  };

  type Cron = {
    startTime: string;
    endTime: string;
    scheduleCycle: string;
    scheduleDate?: string;
    scheduleTime: string;
  };

  type DataSource = {
    nodeId?: string;
    type?: string;
    dataSourceId?: string;
    dataSourceName?: string;
  };

  type DataSourceConfig = {
    editEnable?: boolean;
    nodeId?: string;
    nodeName?: string;
    dataSourceName?: string;
    dataSourceId?: string;
  };

  type DatasourceDetailAggregateVO = {
    nodes?: DataSourceRelatedNode[];
    datasourceId?: string;
    name?: string;
    type?: string;
    status?: string;
    info?: DataSourceInfo;
  };

  type DatasourceDetailRequest = {
    ownerId: string;
    datasourceId: string;
    type: string;
  };

  type DataSourceInfo = true;

  type DatasourceListInfoAggregate = {
    nodes?: DataSourceRelatedNode[];
    datasourceId?: string;
    name?: string;
    type?: string;
    relatedDatas?: string[];
  };

  type DatasourceListRequest = {
    page?: number;
    size?: number;
    sort?: Record<string, any>;
    ownerId: string;
    name?: string;
    status?: string;
    types?: string[];
  };

  type DatasourceListVO = {
    pageSize?: number;
    pageNum?: number;
    total?: number;
    totalPage?: number;
    infos?: DatasourceListInfoAggregate[];
  };

  type DatasourceNodesRequest = {
    ownerId: string;
    datasourceId: string;
  };

  type DatasourceNodesVO = {
    nodes?: DataSourceRelatedNode[];
  };

  type DataSourceRelatedNode = {
    nodeId?: string;
    nodeName?: string;
    status?: string;
  };

  type DataTableNodeInfo = {
    nodeId?: string;
    domainDataId?: string;
  };

  type DatatableNodeVO = {
    datatableVO?: DatatableVO;
    /** node name the table belongs to */
    nodeName?: string;
    /** node id the table belongs to */
    nodeId?: string;
  };

  type DatatableSchema = {
    featureName?: string;
    featureType?: string;
    featureDescription?: string;
  };

  type DatatableVO = {
    /** datatable id */
    datatableId?: string;
    /** datatable name */
    datatableName?: string;
    /** datatable status */
    status?: string;
    /** datatable push to tee status */
    pushToTeeStatus?: string;
    /** datatable push to tee error message if failed */
    pushToTeeErrMsg?: string;
    datasourceId?: string;
    datasourceType?: string;
    datasourceName?: string;
    nodeId?: string;
    relativeUri?: string;
    type?: string;
    /** datatable description */
    description?: string;
    /** datatable table schema */
    schema?: TableColumnVO[];
    /** authorized project list */
    authProjects?: AuthProjectVO[];
    partition?: OdpsPartitionRequest;
    /** Null value defined at data table registration */
    nullStrs?: string[];
    psiDatatableVO?: PsiDatatableVO;
    fiDatatableVO?: PsiDatatableVO;
  };

  type DeleteDatasourceRequest = {
    ownerId: string;
    datasourceId: string;
    type: string;
  };

  type DeleteDatatableRequest = {
    /** node id */
    nodeId?: string;
    /** datatable id */
    datatableId?: string;
    /** tee node id */
    teeNodeId?: string;
    /** datasource id */
    datasourceId?: string;
    /** datasource type */
    datasourceType?: string;
    /** relative uri */
    relativeUri?: string;
    /** table type */
    type?: string;
  };

  type DeleteGraphRequest = {
    projectId: string;
    graphId: string;
  };

  type DeleteModelPackRequest = {
    modelId: string;
    nodeId?: string;
  };

  type DeleteModelServingRequest = {
    servingId: string;
  };

  type DeleteProjectDatatableRequest = {
    /** project id */
    projectId: string;
    /** node id */
    nodeId: string;
    /** datatable id */
    datatableId: string;
    /** table type */
    type?: string;
  };

  type DeletePsiDatatableRequest = {
    /** node id */
    nodeId: string;
    /** grant node id */
    grantNodeId: string;
    /** datasource id */
    datasourceId: string;
    /** datatable id */
    datatableId: string;
    /** table type */
    type?: string;
  };

  type DiscardModelPackRequest = {
    modelId: string;
  };

  type DownloadDataRequest = {
    /** node id */
    nodeId?: string;
    /** domain data id */
    domainDataId?: string;
  };

  type Feature = {
    offlineName?: string;
    onlineName?: string;
  };

  type FeatureDataSourceVO = {
    nodeId?: string;
    featureTableId?: string;
    featureTableName?: string;
    columns?: TableColumnVO[];
  };

  type Field = {
    name?: string;
    type?: string;
    comment?: string;
  };

  type FileMeta = {
    headers?: Record<string, any>;
    rows?: Record<string, any>;
  };

  type FullUpdateGraphRequest = {
    projectId: string;
    graphId: string;
    nodes?: GraphNodeInfo[];
    edges?: GraphEdge[];
    maxParallelism?: number;
    dataSourceConfig?: GraphDataSourceConfig[];
  };

  type GetComponentRequest = {
    app: string;
    domain: string;
    name: string;
  };

  type GetDatatableRequest = {
    /** node id */
    nodeId?: string;
    /** datatable id */
    datatableId?: string;
    /** datasource type */
    datasourceType?: string;
    /** table type */
    type?: string;
    /** tee node id */
    teeNodeId?: string;
  };

  type GetGraphRequest = {
    projectId: string;
    graphId: string;
  };

  type GetLogsRequest = {
    page?: number;
    pageSize?: number;
    level?: string;
    thread?: string;
    logger?: string;
    message?: string;
    tableId?: number;
    dateRange?: string;
  };

  type GetNodeResultDetailRequest = {
    /** node id */
    nodeId?: string;
    /** domain data id */
    domainDataId?: string;
    /** data type，for filtering */
    dataType?: string;
    /** project source */
    dataVendor?: string;
  };

  type GetProjectDatatableRequest = {
    /** project id */
    projectId: string;
    /** node id */
    nodeId: string;
    /** datatable id */
    datatableId: string;
    /** table type */
    type?: string;
  };

  type GetProjectGraphDomainDataSourceRequest = {
    /** project id */
    projectId: string;
  };

  type GetProjectGraphRequest = {
    /** project id */
    projectId: string;
    /** graphId */
    graphId: string;
  };

  type GetProjectJobRequest = {
    /** project id */
    projectId?: string;
    /** job id */
    jobId?: string;
  };

  type GetProjectJobTaskLogRequest = {
    /** project id */
    projectId: string;
    /** job id */
    jobId: string;
    /** task id */
    taskId: string;
  };

  type GetProjectJobTaskOutputRequest = {
    /** project id */
    projectId: string;
    /** job id */
    jobId: string;
    /** task id */
    taskId: string;
    /** outputId */
    outputId: string;
  };

  type GetProjectRequest = {
    /** project id */
    projectId: string;
  };

  type SecretPadPageResponseGrantedDatatableList = {
    status?: SecretPadResponseStatus;
    data?: GrantedDatatableListVO;
  };

  type GrantedDatatableDetailVO = {
    datatableName?: string;
    datatableId?: string;
    tableConfigs?: TableColumnVO[];
    type?: string;
    nodeName?: string;
    nodeId?: string;
    datasourceId?: string;
    datasourceName?: string;
    status?: string;
    createTime?: string;
    isPsiGranted?: number;
    /** datatable table schema */
    schema?: TableColumnVO[];
    psiDatatableVO?: PsiDatatableVO;
  };

  type GrantedDatatableListVO = {
    grantedDatatables?: GrantedDatatableDetailVO[];
    totalNum?: number;
  };

  type GraphDataSourceConfig = {
    nodeId?: string;
    dataSourceId?: string;
  };

  type GraphDetailVO = {
    projectId?: string;
    graphId?: string;
    name?: string;
    nodes?: GraphNodeDetail[];
    edges?: GraphEdge[];
    maxParallelism?: number;
    dataSourceConfig?: DataSourceConfig[];
  };

  type GraphEdge = {
    edgeId?: string;
    source?: string;
    sourceAnchor?: string;
    target?: string;
    targetAnchor?: string;
  };

  type GraphMetaVO = {
    projectId?: string;
    graphId?: string;
    name?: string;
    ownerId?: string;
  };

  type GraphNode = {
    codeName?: string;
    graphNodeId?: string;
    label?: string;
    x?: number;
    y?: number;
    status?: 'STAGING' | 'INITIALIZED' | 'RUNNING' | 'STOPPED' | 'SUCCEED' | 'FAILED';
  };

  type GraphNodeCloudLogsRequest = {
    projectId: string;
    graphNodeId?: string;
    jobId?: string;
    taskId?: string;
    queryParties?: boolean;
    nodeId?: string;
  };

  type GraphNodeDetail = {
    codeName?: string;
    graphNodeId?: string;
    label?: string;
    x?: number;
    y?: number;
    inputs?: string[];
    outputs?: string[];
    nodeDef?: Record<string, any>;
    status?: 'STAGING' | 'INITIALIZED' | 'RUNNING' | 'STOPPED' | 'SUCCEED' | 'FAILED';
    /** jobId，the Job associated with this GraphNode is empty if it is not associated */
    jobId?: string;
    /** taskId，the Task associated with this GraphNode is empty if it is not associated */
    taskId?: string;
    results?: ProjectResultBaseVO[];
    parties?: NodeSimpleInfo[];
    progress?: number;
  };

  type GraphNodeInfo = {
    codeName?: string;
    graphNodeId?: string;
    label?: string;
    x?: number;
    y?: number;
    inputs?: string[];
    outputs?: string[];
    nodeDef?: Record<string, any>;
  };

  type GraphNodeLogsRequest = {
    projectId: string;
    graphId: string;
    graphNodeId: string;
  };

  type GraphNodeMaxIndexRefreshRequest = {
    projectId: string;
    graphId: string;
    currentIndex?: number;
  };

  type GraphNodeMaxIndexRefreshVO = {
    maxIndex?: number;
  };

  type GraphNodeOutputRequest = {
    projectId: string;
    graphId: string;
    graphNodeId: string;
    outputId: string;
  };

  type GraphNodeOutputVO = {
    type?: string;
    codeName?: string;
    tabs?: Record<string, any>;
    meta?: FileMeta;
    jobId?: string;
    taskId?: string;
    graphID?: string;
    warning?: string[];
    gmtCreate?: string;
    gmtModified?: string;
  };

  type GraphNodeStatusVO = {
    graphNodeId?: string;
    taskId?: string;
    jobId?: string;
    status?: 'STAGING' | 'INITIALIZED' | 'RUNNING' | 'STOPPED' | 'SUCCEED' | 'FAILED';
    progress?: number;
    parties?: NodeSimpleInfo[];
  };

  type GraphNodeTaskLogsVO = {
    status?: 'STAGING' | 'INITIALIZED' | 'RUNNING' | 'STOPPED' | 'SUCCEED' | 'FAILED';
    logs?: string[];
  };

  type GraphStatus = {
    finished?: boolean;
    nodes?: GraphNodeStatusVO[];
  };

  type InstRequest = {
    instId: string;
  };

  type InstTokenVO = {
    /** nodeId */
    nodeId?: string;
    /** nodeName */
    nodeName?: string;
    /** token */
    instToken?: string;
    /** createTime */
    createTime?: string;
    /** instTokenState */
    instTokenState?: 'USED' | 'UNUSED';
  };

  type InstVO = {
    /** instId */
    instId?: string;
    /** instName */
    instName?: string;
    /** localNodeId */
    localNodeId?: string;
  };

  type ListDatatableRequest = {
    /** page size */
    pageSize?: number;
    /** page number */
    pageNumber?: number;
    /** node name filter rule */
    nodeNamesFilter?: string[];
    /** status filter rule */
    statusFilter?: string;
    /** datatable name filter rule */
    datatableNameFilter?: string;
    /** table types filter rule */
    types?: string[];
    /** node id */
    ownerId?: string;
    /** tee node id */
    teeNodeId?: string;
  };

  type ListGrantedDatatableRequest = {
    /** page size */
    pageSize?: number;
    /** page number */
    pageNumber?: number;
    /** datatable name filter */
    datatableNameFilter?: string;
    /** status filter */
    statusFilter?: string;
    /** node id */
    nodeId?: string;
    /** table types */
    types?: string[];
  };

  type ListGraphNodeStatusRequest = {
    projectId?: string;
    graphId?: string;
  };

  type ListGraphRequest = {
    projectId: string;
  };

  type ListNodeResultRequest = {
    /** node id */
    ownerId?: string;
    /** page size */
    pageSize?: number;
    /** page number */
    pageNumber?: number;
    /** node name filter */
    nodeNamesFilter?: string[];
    /** kind filters */
    kindFilters?: string[];
    /** data vendor filter */
    dataVendorFilter?: string;
    /** result name filter */
    nameFilter?: string;
    /** time sorting rule */
    timeSortingRule?: string;
    /** tee node id */
    teeNodeId?: string;
  };

  type ListProjectFeatureDatasourceRequest = {
    projectId: string;
    nodeId: string;
  };

  type ListProjectJobRequest = {
    /** page number，starting at 1 */
    pageNum?: number;
    /** page size */
    pageSize?: number;
    /** project id */
    projectId: string;
    /** graph id */
    graphId?: string;
  };

  type LoginRequest = {
    /** user name */
    name?: string;
    /** user password */
    passwordHash?: string;
  };

  type MessageDetailRequest = {
    ownerId: string;
    voteId: string;
    isInitiator: boolean;
    voteType: string;
    projectId?: string;
  };

  type MessageDetailVO = {
    messageName?: string;
    type?: string;
    status?: string;
  };

  type MessageListRequest = {
    page?: number;
    size?: number;
    sort?: Record<string, any>;
    isInitiator?: boolean;
    ownerId: string;
    isProcessed?: boolean;
    type?: string;
    keyWord?: string;
  };

  type MessageListVO = {
    pageSize?: number;
    pageNum?: number;
    total?: number;
    totalPage?: number;
    messages?: MessageVO[];
  };

  type MessagePendingCountRequest = {
    ownerId: string;
  };

  type MessageVO = {
    type?: string;
    status?: string;
    initiatingTypeMessage?: AbstractInitiatingTypeMessage;
    voteTypeMessage?: AbstractVoteTypeMessage;
    messageName?: string;
    createTime?: string;
    voteID?: string;
  };

  type ModelComponent = {
    graphNodeId: string;
    domain?: string;
    name: string;
    version: string;
  };

  type ModelExportPackageRequest = {
    projectId: string;
    graphId: string;
    trainId: string;
    modelName: string;
    modelDesc?: string;
    graphNodeOutPutId: string;
    modelPartyConfig: ModelPartyConfig[];
    modelComponent: ModelComponent[];
  };

  type ModelExportPackageResponse = {
    modelId: string;
    jobId: string;
  };

  type ModelExportStatusRequest = {
    jobId: string;
    projectId: string;
  };

  type ModelPackDetailVO = {
    parties?: Parties[];
  };

  type ModelPackInfoVO = {
    graphDetailVO?: GraphDetailVO;
    modelGraphDetail?: string[];
    modelStats?: string;
    servingDetails?: ServingDetail[];
  };

  type ModelPackListVO = {
    pageSize?: number;
    pageNum?: number;
    total?: number;
    totalPage?: number;
    modelPacks?: ModelPackVO[];
  };

  type ModelPackVO = {
    modelId?: string;
    servingId?: string;
    modelName?: string;
    modelDesc?: string;
    modelStats?: string;
    gmtCreate?: string;
    ownerId?: string;
  };

  type ModelPartiesVO = {
    parties?: Party[];
  };

  type ModelPartyConfig = {
    modelParty: string;
    modelDataSource: string;
    modelDataName?: string;
  };

  type ModelPartyPathRequest = {
    projectId: string;
    graphNodeId: string;
    graphNodeOutPutId: string;
  };

  type ModelPartyPathResponse = {
    nodeId: string;
    nodeName: string;
    dataSources?: DataSource[];
  };

  type NodeAllResultsVO = {
    nodeResultsVO?: NodeResultsVO;
    /** node id */
    nodeId?: string;
    /** node name */
    nodeName?: string;
  };

  type NodeDatatableVO = {
    /** datatable id */
    datatableId?: string;
    /** datatable name */
    datatableName?: string;
  };

  type NodeIdRequest = {
    nodeId: string;
  };

  type NodeInstanceDTO = {
    name?: string;
    status?: string;
    version?: string;
    lastHeartbeatTime?: string;
    lastTransitionTime?: string;
  };

  type NodeInstVO = {
    inviteeId?: string;
    inviteeName?: string;
    instId?: string;
    instName?: string;
  };

  type NodeResultDetailVO = {
    nodeResultsVO?: NodeResultsVO;
    graphDetailVO?: GraphDetailVO;
    /** table column view object list */
    tableColumnVOList?: TableColumnVO[];
    output?: GraphNodeOutputVO;
    /** datasource */
    datasource?: string;
  };

  type NodeResultsVO = {
    /** domain data id */
    domainDataId?: string;
    /** domain datasource id */
    datasourceId?: string;
    /** domain datasource type */
    datasourceType?: string;
    /** node result name */
    productName?: string;
    /** datatable type */
    datatableType?: string;
    /** node result source project id */
    sourceProjectId?: string;
    /** node result source project name */
    sourceProjectName?: string;
    /** uri */
    relativeUri?: string;
    /** job id */
    jobId?: string;
    /** training flow */
    trainFlow?: string;
    /** result pull from tee status */
    pullFromTeeStatus?: string;
    /** result pull from tee error message if failed */
    pullFromTeeErrMsg?: string;
    /** start time */
    gmtCreate?: string;
    /** computeMode */
    computeMode?: string;
  };

  type NodeRouterVO = {
    /** id */
    routeId?: string;
    /** srcNodeId */
    srcNodeId?: string;
    /** dstNodeId */
    dstNodeId?: string;
    srcNode?: NodeVO;
    dstNode?: NodeVO;
    /** srcNetAddress */
    srcNetAddress?: string;
    /** dstNetAddress */
    dstNetAddress?: string;
    /** status */
    status?: string;
    /** gmtCreate */
    gmtCreate?: string;
    /** gmtModified */
    gmtModified?: string;
    /** isProjectJobRunning */
    isProjectJobRunning?: boolean;
    routeType: string;
  };

  type NodeRouteVO = {
    routeId?: number;
    srcNodeId?: string;
    dstNodeId?: string;
    srcNetAddress?: string;
    dstNetAddress?: string;
    status?: string;
  };

  type NodeSimpleInfo = {
    nodeId?: string;
    nodeName?: string;
  };

  type NodeTokenRequest = {
    nodeId: string;
  };

  type NodeVO = {
    /** nodeId */
    nodeId?: string;
    /** nodeName */
    nodeName?: string;
    /** instId */
    instId?: string;
    /** instName */
    instName?: string;
    /** controlNodeId */
    controlNodeId?: string;
    /** masterNodeId */
    masterNodeId?: string;
    /** description */
    description?: string;
    /** netAddress */
    netAddress?: string;
    /** cert */
    cert?: string;
    /** certText */
    certText?: string;
    /** nodeAuthenticationCode */
    nodeAuthenticationCode?: string;
    /** token */
    token?: string;
    /** tokenStatus */
    tokenStatus?: string;
    /** nodeRole */
    nodeRole?: string;
    /** nodeStatus */
    nodeStatus?: string;
    /** nodeType */
    type?: string;
    /** node feature indicates by bit, bit0 - mpc | bit1 - tee | bit2 mpc&tee */
    mode?: number;
    /** gmtCreate */
    gmtCreate?: string;
    /** gmtModified */
    gmtModified?: string;
    /** instance list */
    nodeInstances?: NodeInstanceDTO[];
    /** datatables */
    datatables?: NodeDatatableVO[];
    /** nodeRoutes */
    nodeRoutes?: NodeRouteVO[];
    /** the count of node results */
    resultCount?: number;
    /** protocol */
    protocol?: string;
    /** instToken */
    instToken?: string;
    /** this node allow or not  to be deleted */
    allowDeletion?: boolean;
    /** the main node for network communication */
    isMainNode?: boolean;
  };

  type OdpsPartitionParam = {
    type?: string;
    fields?: Field[];
  };

  type OdpsPartitionRequest = {
    type?: string;
    fields?: Field[];
  };

  type P2pCreateNodeRequest = {
    /** node name */
    name?: string;
    /** node feature indicates by bit, bit0 - mpc | bit1 - tee | bit2 mpc&tee */
    mode: number;
    /** masterNodeId */
    masterNodeId?: string;
    /** certText */
    certText: string;
    /** dstNodeId */
    dstNodeId: string;
    /** srcNetAddress */
    srcNetAddress?: string;
    /** srcNodeId */
    srcNodeId: string;
    /** dstNetAddress */
    dstNetAddress: string;
    /** dstInstId */
    dstInstId?: string;
    /** dstInstName */
    dstInstName?: string;
  };

  type PageNodeRouteRequest = {
    page?: number;
    size?: number;
    sort?: Record<string, any>;
    search?: string;
    ownerId?: string;
  };

  type PageResponseProjectJobSummaryVO = {
    /** the total count of page */
    pageTotal?: number;
    /** page size */
    pageSize?: number;
    /** page data list */
    data?: ProjectJobSummaryVO[];
  };

  type PageScheduledRequest = {
    page?: number;
    size?: number;
    sort?: Record<string, any>;
    search?: string;
    status?: string;
    projectId: string;
  };

  type PageScheduledVO = {
    scheduleId?: string;
    scheduleDesc?: string;
    scheduleStats?: string;
    creator?: string;
    createTime?: string;
    taskRunning?: boolean;
    owner?: string;
    ownerName?: string;
  };

  type ParticipantNodeInstVO = {
    initiatorNodeId?: string;
    initiatorNodeName?: string;
    invitees?: NodeInstVO[];
  };

  type Parties = {
    nodeId?: string;
    nodeName?: string;
    columns?: string[];
  };

  type Party = {
    nodeId?: string;
    nodeName?: string;
  };

  type PartyConfig = {
    features?: Feature[];
    nodeId: string;
    featureTableId: string;
    isMock: boolean;
    resources?: ResourceVO[];
  };

  type PartyVoteInfoVO = {
    partyId?: string;
    partyName?: string;
    action?: string;
    reason?: string;
  };

  type PartyVoteStatus = {
    participantID?: string;
    participantName?: string;
    action?: string;
    reason?: string;
  };

  type ProjectDatatableBaseVO = {
    /** datatable id */
    datatableId?: string;
    /** datatable name */
    datatableName?: string;
    partition?: OdpsPartitionParam;
  };

  type ProjectGraphDomainDataSourceVO = {
    nodeId?: string;
    nodeName?: string;
    dataSources?: DataSource[];
  };

  type ProjectGraphOutputVO = {
    graphId?: string;
    graphNodeId?: string;
    outputs?: string[];
  };

  type ProjectInstVO = {
    /** inst id */
    instId?: string;
    /** inst name */
    instName?: string;
  };

  type ProjectJobSummaryVO = {
    /** job id */
    jobId?: string;
    /** job status */
    status?: 'RUNNING' | 'STOPPED' | 'SUCCEED' | 'FAILED';
    /** job error message */
    errMsg?: string;
    /** job start time */
    gmtCreate?: string;
    /** job update time */
    gmtModified?: string;
    /** finish time */
    gmtFinished?: string;
    /** the count of datatable */
    tableCount?: number;
    /** the count of model */
    modelCount?: number;
    /** the count of rule */
    ruleCount?: number;
    /** the count of report */
    reportCount?: number;
    /** the count of completed subtasks */
    finishedTaskCount?: number;
    /** the count of total subtasks */
    taskCount?: number;
  };

  type ProjectJobVO = {
    /** job id */
    jobId?: string;
    /** job status */
    status?: 'RUNNING' | 'STOPPED' | 'SUCCEED' | 'FAILED';
    /** job error message */
    errMsg?: string;
    /** job start time */
    gmtCreate?: string;
    /** job update time */
    gmtModified?: string;
    /** job finish time */
    gmtFinished?: string;
    finished?: boolean;
    graph?: GraphDetailVO;
  };

  type ProjectNodeVO = {
    /** node id */
    nodeId?: string;
    /** node name */
    nodeName?: string;
    /** node type */
    nodeType?: string;
    /** datatable list of the node, it is empty for list requests */
    datatables?: ProjectDatatableBaseVO[];
  };

  type ProjectOutputVO = {
    /** project id */
    projectId?: string;
    /** project name */
    projectName?: string;
    /** project description */
    description?: string;
    /** List of added nodes output */
    nodes?: ProjectGraphOutputVO[];
    /** the count of graph */
    graphCount?: number;
    /** the count of job */
    jobCount?: number;
    /** start time of the project */
    gmtCreate?: string;
    /** computeMode */
    computeMode?: string;
  };

  type ProjectParticipantsDetailVO = {
    initiatorId?: string;
    initiatorName?: string;
    projectName?: string;
    partyVoteStatuses?: PartyVoteStatus[];
    computeMode?: string;
    computeFunc?: string;
    projectDesc?: string;
    gmtCreated?: string;
    participantNodeInstVOS?: ParticipantNodeInstVO[];
    status?: string;
  };

  type ProjectParticipantsRequest = {
    /** vote_id */
    voteId?: string;
  };

  type ProjectResultBaseVO = {
    kind?: 'FedTable' | 'Model' | 'Rule' | 'Report' | 'READ_DATA';
    refId?: string;
  };

  type ProjectVO = {
    partyVoteInfos?: PartyVoteInfoVO[];
    /** project id */
    projectId?: string;
    /** project name */
    projectName?: string;
    /** project description */
    description?: string;
    /** list of added nodes */
    nodes?: ProjectNodeVO[];
    /** list of added insts */
    insts?: ProjectInstVO[];
    /** the count of graph */
    graphCount?: number;
    /** the count of job */
    jobCount?: number;
    /** start time of the project */
    gmtCreate?: string;
    /** computeMode */
    computeMode?: string;
    /** teeNodeId */
    teeNodeId: string;
    status?: string;
    initiator?: string;
    initiatorName?: string;
    computeFunc?: string;
    voteId?: string;
  };

  type PsiDatatableVO = {
    projectType?: string;
    gmtTime?: string;
    grantNodeIds?: string[];
  };

  type QueryModelDetailRequest = {
    modelId: string;
    projectId: string;
  };

  type QueryModelPageRequest = {
    page?: number;
    size?: number;
    sort?: Record<string, any>;
    projectId: string;
    searchKey?: string;
    modelStats?: string;
  };

  type QueryModelServingRequest = {
    servingId: string;
  };

  type registerNodeParams = {
    json_data: string;
  };

  type ResourceVO = {
    minCPU: string;
    maxCPU: string;
    minMemory: string;
    maxMemory: string;
  };

  type RouterIdRequest = {
    /** nodeId */
    nodeId?: string;
    /** routerId */
    routerId: string;
  };

  type ScheduledDelRequest = {
    scheduleId: string;
  };

  type ScheduledGraphCreateRequest = {
    scheduleId: string;
    scheduleDesc?: string;
    cron?: Cron;
    projectId: string;
    graphId: string;
    nodes: string[];
  };

  type ScheduledGraphOnceSuccessRequest = {
    projectId: string;
    graphId: string;
  };

  type ScheduledIdRequest = {
    projectId: string;
    graphId: string;
  };

  type ScheduledInfoRequest = {
    scheduleId: string;
  };

  type ScheduledOfflineRequest = {
    scheduleId: string;
  };

  type ScheduleListProjectJobRequest = {
    /** page number，starting at 1 */
    pageNum?: number;
    /** page size */
    pageSize?: number;
    projectId: string;
    graphId: string;
    scheduleTaskId: string;
  };

  type SecretPadPageResponseNodeRouterVO = {
    /** page data */
    list?: NodeRouterVO[];
    /** total */
    total?: number;
  };

  type SecretPadPageResponsePageScheduledVO = {
    /** page data */
    list?: PageScheduledVO[];
    /** total */
    total?: number;
  };

  type SecretPadPageResponseTaskPageScheduledVO = {
    /** page data */
    list?: TaskPageScheduledVO[];
    /** total */
    total?: number;
  };

  type SecretPadResponse = {
    status?: SecretPadResponseStatus;
    data?: Record<string, any>;
  };

  type SecretPadResponseAllDatatableListVO = {
    status?: SecretPadResponseStatus;
    data?: AllDatatableListVO;
  };

  type SecretPadResponseAllNodeResultsListVO = {
    status?: SecretPadResponseStatus;
    data?: AllNodeResultsListVO;
  };

  type SecretPadResponseBoolean = {
    status?: SecretPadResponseStatus;
    data?: boolean;
  };

  type SecretPadResponseCloudGraphNodeTaskLogsVO = {
    status?: SecretPadResponseStatus;
    data?: CloudGraphNodeTaskLogsVO;
  };

  type SecretPadResponseComponentVersion = {
    status?: SecretPadResponseStatus;
    data?: ComponentVersion;
  };

  type SecretPadResponseCreateDatasourceVO = {
    status?: SecretPadResponseStatus;
    data?: CreateDatasourceVO;
  };

  type SecretPadResponseCreateDatatableVO = {
    status?: SecretPadResponseStatus;
    data?: CreateDatatableVO;
  };

  type SecretPadResponseCreateGraphVO = {
    status?: SecretPadResponseStatus;
    data?: CreateGraphVO;
  };

  type SecretPadResponseCreateProjectVO = {
    status?: SecretPadResponseStatus;
    data?: CreateProjectVO;
  };

  type SecretPadResponseDatasourceDetailAggregateVO = {
    status?: SecretPadResponseStatus;
    data?: DatasourceDetailAggregateVO;
  };

  type SecretPadResponseDatasourceListVO = {
    status?: SecretPadResponseStatus;
    data?: DatasourceListVO;
  };

  type SecretPadResponseDatasourceNodesVO = {
    status?: SecretPadResponseStatus;
    data?: DatasourceNodesVO;
  };

  type SecretPadResponseDatatableNodeVO = {
    status?: SecretPadResponseStatus;
    data?: DatatableNodeVO;
  };

  type SecretPadResponseGraphDetailVO = {
    status?: SecretPadResponseStatus;
    data?: GraphDetailVO;
  };

  type SecretPadResponseGraphNodeMaxIndexRefreshVO = {
    status?: SecretPadResponseStatus;
    data?: GraphNodeMaxIndexRefreshVO;
  };

  type SecretPadResponseGraphNodeOutputVO = {
    status?: SecretPadResponseStatus;
    data?: GraphNodeOutputVO;
  };

  type SecretPadResponseGraphNodeTaskLogsVO = {
    status?: SecretPadResponseStatus;
    data?: GraphNodeTaskLogsVO;
  };

  type SecretPadResponseGraphStatus = {
    status?: SecretPadResponseStatus;
    data?: GraphStatus;
  };

  type SecretPadResponseInstTokenVO = {
    status?: SecretPadResponseStatus;
    data?: InstTokenVO;
  };

  type SecretPadResponseInstVO = {
    status?: SecretPadResponseStatus;
    data?: InstVO;
  };

  type SecretPadResponseListFeatureDataSourceVO = {
    status?: SecretPadResponseStatus;
    data?: FeatureDataSourceVO[];
  };

  type SecretPadResponseListGraphMetaVO = {
    status?: SecretPadResponseStatus;
    data?: GraphMetaVO[];
  };

  type SecretPadResponseListModelPartyPathResponse = {
    status?: SecretPadResponseStatus;
    data?: ModelPartyPathResponse[];
  };

  type SecretPadResponseListNodeVO = {
    status?: SecretPadResponseStatus;
    data?: NodeVO[];
  };

  type SecretPadResponseListProjectVO = {
    status?: SecretPadResponseStatus;
    data?: ProjectVO[];
  };

  type SecretPadResponseLong = {
    status?: SecretPadResponseStatus;
    data?: number;
  };

  type SecretPadResponseMapStringCompListVO = {
    status?: SecretPadResponseStatus;
    data?: Record<string, any>;
  };

  type SecretPadResponseMessageDetailVO = {
    status?: SecretPadResponseStatus;
    data?: MessageDetailVO;
  };

  type SecretPadResponseMessageListVO = {
    status?: SecretPadResponseStatus;
    data?: MessageListVO;
  };

  type SecretPadResponseModelExportPackageResponse = {
    status?: SecretPadResponseStatus;
    data?: ModelExportPackageResponse;
  };

  type SecretPadResponseModelPackDetailVO = {
    status?: SecretPadResponseStatus;
    data?: ModelPackDetailVO;
  };

  type SecretPadResponseModelPackInfoVO = {
    status?: SecretPadResponseStatus;
    data?: ModelPackInfoVO;
  };

  type SecretPadResponseModelPackListVO = {
    status?: SecretPadResponseStatus;
    data?: ModelPackListVO;
  };

  type SecretPadResponseModelPartiesVO = {
    status?: SecretPadResponseStatus;
    data?: ModelPartiesVO;
  };

  type SecretPadResponseNodeResultDetailVO = {
    status?: SecretPadResponseStatus;
    data?: NodeResultDetailVO;
  };

  type SecretPadResponseNodeRouterVO = {
    status?: SecretPadResponseStatus;
    data?: NodeRouterVO;
  };

  type SecretPadResponseNodeVO = {
    status?: SecretPadResponseStatus;
    data?: NodeVO;
  };

  type SecretPadResponseObject = {
    status?: SecretPadResponseStatus;
    data?: Record<string, any>;
  };

  type SecretPadResponsePageResponseProjectJobSummaryVO = {
    status?: SecretPadResponseStatus;
    data?: PageResponseProjectJobSummaryVO;
  };

  type SecretPadResponseProjectJobVO = {
    status?: SecretPadResponseStatus;
    data?: ProjectJobVO;
  };

  type SecretPadResponseProjectOutputVO = {
    status?: SecretPadResponseStatus;
    data?: ProjectOutputVO;
  };

  type SecretPadResponseProjectParticipantsDetailVO = {
    status?: SecretPadResponseStatus;
    data?: ProjectParticipantsDetailVO;
  };

  type SecretPadResponseProjectVO = {
    status?: SecretPadResponseStatus;
    data?: ProjectVO;
  };

  type SecretPadResponseSecretPadPageResponseNodeRouterVO = {
    status?: SecretPadResponseStatus;
    data?: SecretPadPageResponseNodeRouterVO;
  };

  type SecretPadResponseSecretPadPageResponsePageScheduledVO = {
    status?: SecretPadResponseStatus;
    data?: SecretPadPageResponsePageScheduledVO;
  };

  type SecretPadResponseSecretPadPageResponseTaskPageScheduledVO = {
    status?: SecretPadResponseStatus;
    data?: SecretPadPageResponseTaskPageScheduledVO;
  };

  type SecretPadResponseServingDetailVO = {
    status?: SecretPadResponseStatus;
    data?: ServingDetailVO;
  };

  type SecretPadResponseSetProjectGraphDomainDataSourceVO = {
    status?: SecretPadResponseStatus;
    data?: ProjectGraphDomainDataSourceVO[];
  };

  type SecretPadResponseStartGraphVO = {
    status?: SecretPadResponseStatus;
    data?: StartGraphVO;
  };

  type SecretPadResponseStatus = {
    code?: number;
    msg?: string;
  };

  type SecretPadResponseString = {
    status?: SecretPadResponseStatus;
    data?: string;
  };

  type SecretPadResponseSyncDataDTOObject = {
    status?: SecretPadResponseStatus;
    data?: SyncDataDTOObject;
  };

  type SecretPadResponseUploadDataResultVO = {
    status?: SecretPadResponseStatus;
    data?: UploadDataResultVO;
  };

  type SecretPadResponseUserContextDTO = {
    status?: SecretPadResponseStatus;
    data?: UserContextDTO;
  };

  type SecretPadResponseVoid = {
    status?: SecretPadResponseStatus;
    data?: Record<string, any>;
  };

  type SecretPadResponseCreateFiProjectJobVO = {
    status?: SecretPadResponseStatus;
    data?: CreateFiProjectJobVO;
  };

  type SecretPadResponsePredictionResultVO = {
    status?: SecretPadResponseStatus;
    data?: PredictionResultVO;
  };

  type SecretPadResponseFIJobListResponse = {
    status?: SecretPadResponseStatus;
    data?: {
      total: number;
      fiJobs: FIJobListVO[];
    };
  };

  type CreateFiProjectJobVO = {
    /** 任务ID */
    jobId?: string;
    /** 任务名称 */
    name?: string;
    /** 任务状态 */
    status?: string;
  };

  type ServingDetail = {
    nodeId?: string;
    nodeName?: string;
    endpoints?: string;
    featureHttp?: string;
    isMock?: boolean;
    featureMappings?: Record<string, any>;
    sourcePath?: string;
    resources?: ResourceVO[];
  };

  type ServingDetailVO = {
    modelId?: string;
    servingDetails?: ServingDetail[];
    servingId?: string;
  };

  type StartGraphRequest = {
    projectId: string;
    graphId: string;
    breakpoint?: boolean;
    nodes: string[];
  };

  type StartGraphVO = {
    jobId?: string;
  };

  type StopGraphNodeRequest = {
    projectId: string;
    graphId: string;
    graphNodeId?: string;
  };

  type StopProjectJobTaskRequest = {
    /** project id */
    projectId: string;
    /** job id */
    jobId: string;
  };

  type SyncDataDTOObject = {
    tableName?: string;
    lastUpdateTime?: string;
    action?: string;
    data?: Record<string, any>;
  };

  type TableColumnConfigParam = {
    /** column name */
    colName: string;
    /** association key or not, false by default */
    isAssociateKey?: boolean;
    /** group key or not, false by default */
    isGroupKey?: boolean;
    /** label key or not, false by default */
    isLabelKey?: boolean;
    /** key protection or not, false by default */
    isProtection?: boolean;
  };

  type TableColumnVO = {
    /** column name */
    colName?: string;
    /** column type */
    colType?: string;
    /** column comment */
    colComment?: string;
  };

  type TaskInfoScheduledRequest = {
    scheduleId: string;
    scheduleTaskId: string;
  };

  type TaskPageScheduledRequest = {
    page?: number;
    size?: number;
    sort?: Record<string, any>;
    search?: string;
    scheduleId?: string;
  };

  type TaskPageScheduledVO = {
    scheduleTaskId?: string;
    scheduleTaskExpectStartTime?: string;
    scheduleTaskStartTime?: string;
    scheduleTaskEndTime?: string;
    scheduleTaskStatus?: string;
    allReRun?: boolean;
  };

  type TaskReRunScheduledRequest = {
    scheduleId: string;
    scheduleTaskId: string;
    type?: string;
  };

  type TaskStopScheduledRequest = {
    scheduleId: string;
    scheduleTaskId: string;
  };

  type UpdateGraphMetaRequest = {
    projectId: string;
    graphId: string;
    name: string;
  };

  type UpdateGraphNodeRequest = {
    projectId: string;
    graphId: string;
    node: GraphNodeInfo;
  };

  type UpdateNodeRequest = {
    netAddress: string;
    nodeId: string;
  };

  type UpdateNodeRouterRequest = {
    /** routerId */
    routerId?: string;
    /** srcNetAddress */
    srcNetAddress?: string;
    /** dstNetAddress */
    dstNetAddress?: string;
  };

  type UpdateProjectRequest = {
    /** project id */
    projectId: string;
    /** project name */
    name?: string;
    /** project description */
    description?: string;
  };

  type UploadDataResultVO = {
    /** file name */
    name?: string;
    /** the real path to store the data */
    realName?: string;
    /** data source */
    datasource?: string;
    /** data source type */
    datasourceType?: string;
  };

  type uploadParams = {
    'Node-Id': string;
  };

  type UserContextDTO = {
    token?: string;
    name?: string;
    platformType?: 'EDGE' | 'CENTER' | 'TEST' | 'AUTONOMY';
    platformNodeId?: string;
    ownerType?: 'EDGE' | 'CENTER' | 'P2P';
    ownerId?: string;
    ownerName?: string;
    projectIds?: string[];
    apiResources?: string[];
    virtualUserForNode?: boolean;
    deployMode?: string;
  };

  type UserUpdatePwdRequest = {
    /** user name */
    name?: string;
    /** user old password */
    oldPasswordHash: string;
    /** user new password */
    newPasswordHash: string;
    /** user confirm password */
    confirmPasswordHash: string;
  };

  type VoteReplyRequest = {
    action?: string;
    reason?: string;
    voteId: string;
    voteParticipantId: string;
  };

  type PSINodeDef = {
    attrPaths: string[];
    attrs: Array<{
      ss?: string[];
      s?: string;
      b?: boolean;
      is_na: boolean;
    }>;
    domain: string;
    name: string;
    version: string;
  };

  type PSINodeInfo = {
    codeName: string;
    nodeDef: PSINodeDef;
    parties: Array<{
      nodeId: string;
      nodeName: string;
    }>;
    progress: number | null;
  };

  type PSIJobCreateRequest = {
    /** PSI task name */
    name: string;
    /** PSI task description */
    description?: string;
    /** Initiator node ID */
    initiatorNodeId: string;
    /** Partner node ID */
    partnerNodeId: string;
    /** Initiator datatable ID */
    initiatorDatatableId: string;
    /** Partner datatable ID */
    partnerDatatableId: string;
    /** Receiver parties */
    receiverParties: string[];
    /** Node information */
    nodeInfo: PSINodeInfo;
  };

  type PSIJobCreateResponse = {
    status?: SecretPadResponseStatus;
    data?: {
      name: string;
      jobId: string;
    };
  };

  type PSIParticipantNodeInst = {
    /** Initiator node ID */
    initiatorNodeId: string;
    /** List of invitees */
    invitees: Array<{
      /** Invitee ID */
      inviteeId: string;
    }>;
  };

  type PSIVoteConfig = {
    /** Project ID */
    projectId: string;
    /** Job ID */
    jobId: string;
    /** List of participants */
    participants: string[];
    /** Participant node instance information */
    participantNodeInstVOS: PSIParticipantNodeInst[];
  };

  type PSIApprovalCreateRequest = {
    /** Initiator ID */
    initiatorId: string;
    /** Vote type */
    voteType: 'COMMON_JOB_CREATE';
    /** Vote configuration */
    voteConfig: PSIVoteConfig;
  };

  type PSIApprovalCreateResponse = {
    status?: SecretPadResponseStatus;
    data?: any;
  };

  type PSIJobInst = {
    /** Instance ID */
    instId: string;
    /** Instance name */
    instName: string;
  };

  type PSIJobDatatable = Record<string, any>;

  type PSIJobNode = {
    /** Node ID */
    nodeId: string;
    /** Node name */
    nodeName: string;
    /** Node type */
    nodeType: string;
    /** Datatables */
    datatables: PSIJobDatatable[];
  };

  type PSIJobPartyVoteInfo = {
    /** Party ID */
    partyId: string;
    /** Party name */
    partyName: string;
    /** Action */
    action: string;
    /** Reason */
    reason: string;
  };

  type PSIJobInfo = {
    /** Job ID */
    jobId: string;
    /** Job name */
    jobName: string;
    /** Job description */
    description: string;
    /** Job status */
    status: string;
    /** Job create time */
    createTime: string;
    /** Job start time */
    startTime: string;
    /** Job finished time */
    finishedTime: string;
    /** Node definition */
    nodeDef: string;
    /** Initiator datatable name */
    initiatorDatatableName: string;
    /** Partner datatable name */
    partnerDatatableName: string;
    /** Domain data ID */
    domainDataId: string;
    /** Vote ID */
    voteId: string;
    /** Instances */
    insts: PSIJobInst[];
    /** Nodes */
    nodes: PSIJobNode[];
    /** Receiver parties */
    receiverParties: string[];
    /** Party vote information */
    partyVoteInfos: PSIJobPartyVoteInfo[];
    /** Initiator ID */
    initiator: string;
    /** Initiator name */
    initiatorName: string;
  };

  type PSIJobListResponse = {
    /** Response status */
    status?: SecretPadResponseStatus;
    /** Response data */
    data: {
      /** List of PSI jobs */
      psiJobs: PSIJobInfo[];
      /** Total count */
      total: number;
    };
  };

  type PSIJobListRequest = {
    /** Page number */
    page: number;
    /** Page size */
    pageSize: number;
    /** Task name */
    taskName?: string;
    /** Initiator */
    initiator?: number;
    /** Partner */
    partner?: number;
    /** Status */
    status?: string;
  };

  type PSIJobDatatableSchema = {
    /** Column name */
    colName: string;
    /** Column type */
    colType: string;
    /** Column comment */
    colComment: string;
  };

  type PSIJobDatatables = {
    /** Datatable ID */
    datatableId: string;
    /** Datatable name */
    datatableName: string;
    /** Datasource ID */
    datasourceId: string;
    /** Datasource name */
    datasourceName: string;
    /** Datatable type */
    datasourceType: string;
    type: string;
    status: string;
    nodeId: string;
    grantNodeId: string;
    schema: PSIJobDatatableSchema[];
  };

  type PSIJobDatatablesResponse = {
    status?: SecretPadResponseStatus;
    data?: PSIJobDatatables[];
  };

  type PSITaskStatus = {
    taskId: string;
    jobId: string;
    status: string;
    progress: string;
    finished: boolean;
    domainDataId: string;
  };

  type PSITaskStatusResponse = {
    status?: SecretPadResponseStatus;
    data?: {
      taskStatusList: PSITaskStatus[];
    };
  };

  type PSILogsResponse = {
    status?: SecretPadResponseStatus;
    data?: {
      logLevel: string;
      logTime: string;
      logMessage: string;
    }[];
  };

  type PSILogDetailsResponse = {
    status?: SecretPadResponseStatus;
    data?: {
      jobId: string;
      state: string;
      errMsg: string;
      taskId: string;
      taskState: string;
      taskErrMsg: string;
      partyLogs: {
        domainId: string;
        state: string;
        errMsg: string;
      }[];
    };
  };

  type FiPartyConfig = {
    features?: Feature[];
    nodeId: string;
    joinId: string;
    resources?: ResourceVO[];
  };

  type CreateFiProjectJobRequest = {
    /** 项目ID */
    projectId: string;
    /** 任务名称 */
    name: string;
    /** 任务描述 */
    description?: string;
    /** 发起方节点ID */
    initiatorNodeId: string;
    /** 发起方数据表ID */
    initiatorTableId: string;
    /** 发起方模型 */
    initiatorModel: string;
    /** 参与方节点ID */
    partnerNodeId: string;
    /** 参与方数据表ID */
    partnerTableId: string;
    /** 模型来源项目ID */
    sourceProjectId: string;
    /** 模型ID */
    modelId: string;
    /** 发起方键列 */
    initiatorKeyColumns: string[];
    /** 参与方键列 */
    partnerKeyColumns: string[];
    /** 接收方列表 */
    receiverParties: string[];
    /** 预测结果列名配置 */
    resultColumns: string;
    /** 模板ID（可选） */
    templateId?: number;
    /** 参与方配置 */
    partyConfigs?: FiPartyConfig[];
  };

  type getPredictionResultsParams = {
    jobId: string;
  };

  type PredictionResult = {
    id?: number;
    servingId?: string;
    resultData?: Record<string, any>;
    statusCode?: number;
    statusMessage?: string;
    isSuccess?: boolean;
    recordTime?: string;
  };

  type PredictionResultVO = {
    jobId?: string;
    jobName?: string;
    projectId?: string;
    results?: PredictionResult[];
  };

  type FIJobListVO = {
    jobId: string;
    jobName: string;
    description: string;
    status: string;
    createTime: string;
    startTime: string;
    finishedTime: string | null;
    domainDataId: string;
    error: string | null;
    insts: {
      instId: string;
      instName: string;
    }[];
    nodes: {
      nodeId: string;
      nodeName: string;
      nodeType: string;
      datatables: any[];
    }[];
    receiverParties: string[];
    partyVoteInfos: FIJobPartyVoteInfo[];
    initiator: string;
    initiatorName: string;
    initiatorDatatableName: string;
    partnerDatatableName: string;
    sourceProjectId: string;
    sourceProjectName: string;
    modelId: string;
    initiatorTableId: string;
    partner: string;
    partnerTableId: string;
    resultColumns: string;
    initiatorKeyColumns: string;
    partnerKeyColumns: string;
    partyConfig: string;
    modelName: string;
    modelStats: number;
    servingId: string;
    servingInputConfig: string;
    projectId: string;
    partyEndpoints: PartyEndpoint[];
  };

  type FIJobPartyVoteInfo = {
    partyId: string;
    partyName: string;
    action: string;
    reason: string | null;
  };

  type PartyEndpoint = {
    endpoints: string;
    nodeId: string;
  };
}
