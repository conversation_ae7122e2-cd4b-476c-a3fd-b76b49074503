import { ReactComponent as Logo } from '@/assets/logo2.svg';

const hour = new Date().getHours();
let time = '';
if (hour < 12) {
  time = '上午';
} else if (hour > 18) {
  time = '晚上';
} else if (hour >= 12) {
  time = '下午';
}

export default {
  theme: {
    token: {
      colorPrimary: '#4762B2',
      colorInfo: '#4762B2',
      colorLink: '#4762B2',
      colorIcon: '#4762B2',
      colorIconHover: '#4762B2',
      // colorInfoActive: '#9a0000',
      // colorInfoTextActive: '#faf3f3',
      // colorPrimaryActive: '#faf3f3',
      // colorPrimaryTextActive: '#9a0000',
      // colorPrimaryText: '#9a0000',
      // colorPrimaryBorder: '#9a0000',
      // colorPrimaryBorderHover:'#faf3f3'	,
      // // colorPrimaryHover:'#faf3f3'	,
      // // colorPrimaryTextHover:'#faf3f3'	,
      // colorInfoText: '#9a0000',
      // colorInfoBg:'#faf3f3',//#c1615a
      // colorInfoBgHover:'#c1615a',
      // colorInfoBorder:'#c1615a',
      // colorInfoBorderHover:'#c1615a',
      // colorInfoHover:'#c1615a',
    },
    components: {
      Steps: {
        colorPrimary: '#1677ff',
        colorPrimaryBorder: '#91caff',
      },
    },
  },
  slogan: '科技护航数据安全 开源加速数据流通', // 全局标语
  header: {
    logo: <Logo style={{ fill: '#144a9b' }} />, // 左上角Logo React Component
    rightLinks: true, // boolean | React Component
  },
  createProject: {
    showTemplate: true, // 创建项目时是否显示模板选项
  },
  home: {
    HomePageTitle: `${time}好👋，欢迎来到燕云可信数据空间`,
  },
  guide: true, //
};
