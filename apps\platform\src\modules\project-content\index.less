.content {
  display: flex;
  height: 100%;
  flex-direction: column;
}

.contentHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 24px;

  .contentHeaderleft {
    font-size: 20px;
    font-weight: 500;
  }

  .contentHeaderRight {
    position: relative;
    width: 192px;
    height: 32px;
    box-sizing: border-box;
    border-radius: 16px;
    background-image: linear-gradient(270deg, #319dff 0%, #166bff 100%);
    box-shadow: 0 4px 10px 0 rgb(22 119 255 / 6%), 0 2px 4px 0 rgb(22 119 255 / 16%);
    line-height: 32px;

    .smallGirl {
      position: absolute;
      bottom: 0;
      left: 6px;
      width: 45px;
    }

    .smallGirlText {
      margin-left: 12px;
    }

    .contentHeaderRightText {
      display: flex;
      width: 100%;
      align-items: center;
      justify-content: center;
      padding-left: 10px;
      color: #fff;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
    }
  }
}

.projectContent {
  display: flex;
  overflow: hidden;
  height: 500px;
  box-sizing: border-box;
  flex: 1;

  .projectContentLeft {
    width: 73%;
    border-radius: 8px;
    background: #fff;
  }

  .projectContentRight {
    width: 27%;
    border-radius: 8px;
    margin-left: 20px;
    background: #fff;
  }
}
