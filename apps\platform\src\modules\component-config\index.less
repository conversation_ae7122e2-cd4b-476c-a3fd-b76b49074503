.componentConfigDrawer {
  position: absolute;

  :global {
    .ant-drawer-content-wrapper {
      border-radius: 8px;
      box-shadow: none;
    }

    .ant-drawer-title {
      font-size: 14px;
    }

    .ant-drawer-body {
      padding: 0 12px;
    }

    .ant-drawer-header {
      padding: 10px 12px 20px;
      border-bottom-color: transparent;
    }
  }
}

.float {
  right: 320px;
}

.configModalWrapper {
  display: flex;
  height: 100%;
  flex-direction: column;
}

.description {
  padding: 0;
  font-size: 12px;

  .label {
    color: rgb(0 0 0 / 45%);
  }

  > div {
    margin-bottom: 16px;
  }
}

.configForm {
  position: relative;
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow-y: auto;

  :global {
    .ant-form {
      display: flex;
      flex: 1;
      flex-direction: column;
    }

    .ant-form-item {
      margin-bottom: 16px;
    }

    .ant-form-item-label {
      padding: 0;
    }

    .ant-alert {
      margin-bottom: 16px;
      color: rgb(0 0 0 / 65%);
      font-size: 12px;
    }
  }

  .customItems {
    flex: 1;
    overflow-y: auto;
  }

  .footer {
    position: sticky;
    bottom: 0;
    left: 0;
    display: flex;
    width: 100%;
    height: 52px;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #e1e1e1;
    background-color: white;
  }
}

.configItemLabel {
  color: rgb(0 0 0 / 45%);
  font-size: 12px;
  font-weight: 400;
}

.configItemStructLabel {
  padding: 0 0 8px;
  border-bottom: 1px #e1e1e1 solid;
  margin: 20px 0 0;
  color: rgb(0 0 0 / 60%);
  font-size: 12px;
  font-weight: 600;
}

.defaultRender {
  :global {
    .ant-input-number {
      width: 100%;
    }
  }
}

.componentConfigDrawer:focus {
  outline: none;
}
