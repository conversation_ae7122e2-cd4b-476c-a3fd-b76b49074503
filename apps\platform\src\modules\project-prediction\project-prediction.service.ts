import { message } from 'antd';
import { parse } from 'query-string';

import API from '@/services/secretpad';
import API2 from '@/services/secretpad2';
import { Model } from '@/util/valtio-helper';

import type { AllocatedNode } from '../project-psi/project-psi.service';

export class ProjectPredictionService extends Model {
  getAllocatedNodes = async () => {
    const { ownerId } = parse(window.location.search);
    const { data } = await API.NodeRouteController.page({
      page: 1,
      size: 1000,
      search: '',
      sort: {},
      ownerId: ownerId as string,
    });
    const allocatedNodes: AllocatedNode[] = [];
    allocatedNodes.push({
      nodeId: data?.list?.[0].dstNode?.nodeId || '',
      nodeName: data?.list?.[0].dstNode?.nodeName || '',
      instId: data?.list?.[0].dstNode?.instId || '',
      instName: data?.list?.[0].dstNode?.instName || '',
    });
    (data?.list || []).forEach((item: API.NodeRouterVO) => {
      if (item.status === 'Succeeded') {
        // if (!allocatedNodes.find((node) => node.nodeId === item.srcNode?.nodeId)) {
        allocatedNodes.push({
          nodeId: item.srcNode?.nodeId || '',
          nodeName: item.srcNode?.nodeName || '',
          instId: item.srcNode?.instId || '',
          instName: item.srcNode?.instName || '',
        });
        // }
      }
    });
    console.log(allocatedNodes, 'allocatedNodes');
    return allocatedNodes;
  };

  getPredictionDatatables = async (nodeId: string) => {
    const res = await API2.FIController.getPredictionDatatables(nodeId);
    if (res.status?.code !== 0) {
      message.error(res.status?.msg);
      return null;
    }
    return res.data;
  };

  getPredictionList = async (data: API2.PSIJobListRequest) => {
    const res = await API2.FIController.getPredictionList(data);
    if (res.status?.code !== 0) {
      message.error(res.status?.msg);
      return null;
    }
    return res.data;
  };

  createFIApproval = async (data: API2.PSIApprovalCreateRequest) => {
    const res = await API.ApprovalController.create(data);
    return res;
  };

  createFIJob = async (data: API2.CreateFiProjectJobRequest) => {
    const res = await API2.FIController.createFiProjectJob(data);
    return res;
  };

  checkPredictionJobStatus = async (data: { jobIds: string[] }) => {
    const res = await API2.FIController.checkPredictionJobStatus(data);
    return res;
  };

  deletePredictionJob = async (data: { jobId: string; nodeId: string }) => {
    const res = await API2.FIController.deletePredictionJob(data);
    if (res.status?.code !== 0) {
      message.error(res.status?.msg);
      return null;
    } else {
      message.success('删除成功');
      return true;
    }
  };

  cancelPredictionJob = async (data: { jobId: string; nodeId: string }) => {
    const res = await API2.FIController.cancelPredictionJob(data);
    if (res.status?.code !== 0) {
      message.error(res.status?.msg);
      return null;
    } else {
      message.success('取消成功');
      return true;
    }
  };

  getPredictionJobLogs = async (data: { jobId: string; nodeId: string }) => {
    const res = await API2.FIController.getPredictionJobLogs(data);
    if (res.status?.code !== 0) {
      message.error(res.status?.msg);
      return null;
    }
    return res.data;
  };

  getPredictionJobLogDetails = async (data: { jobId: string; nodeId: string }) => {
    const res = await API2.FIController.getPredictionJobLogDetails(data);
    if (res.status?.code !== 0) {
      message.error(res.status?.msg);
      return undefined;
    } else return res.data;
  };

  download = async (data: { jobId: string; nodeId: string; domainDataId: string }) => {
    const token = localStorage.getItem('User-Token') || '';
    fetch(`/api/v1alpha1/fi/job/downloadResult`, {
      method: 'POST',
      mode: 'cors',
      cache: 'no-cache',
      credentials: 'include',
      headers: {
        'content-type': 'application/json',
        'User-Token': token,
      },

      body: JSON.stringify({
        jobId: data.jobId,
        nodeId: data.nodeId,
        domainDataId: data.domainDataId,
      }),
    })
      .then((res) => {
        res.blob().then((blob) => {
          const blobObj = new Blob(['\ufeff', blob], {
            type: 'text/plain;charset=utf-8',
          });

          const disposition = res.headers.get('Content-Disposition');
          let filename = '';
          const filenameRegex = /filename[^;=\n]*=[^'"]*['"]*((['"]).*?\2|[^;\n]*)/;
          const matches = filenameRegex.exec(disposition || '');
          if (matches != null && matches[1]) {
            filename = matches[1].replace(/['"]/g, '');
          }
          const a = document.createElement('a');
          document.body.appendChild(a); //兼容火狐，将a标签添加到body当中
          const url = window.URL.createObjectURL(blobObj); // 获取 blob 本地文件连接 (blob 为纯二进制对象，不能够直接保存到磁盘上)
          a.href = url;
          a.download = filename;
          a.click();
          a.remove(); //移除a标签
          window.URL.revokeObjectURL(url);
          message.success('下载完成');
        });
      })
      .catch((err) => {
        message.error(err);
      });
  };
}
