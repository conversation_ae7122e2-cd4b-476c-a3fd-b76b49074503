import { message, Table, Typography } from 'antd';
import type { ColumnType } from 'antd/es/table';
import { Gau<PERSON><PERSON><PERSON> } from 'echarts/charts';
import { GridComponent, TooltipComponent, TitleComponent } from 'echarts/components';
import * as echarts from 'echarts/core';
import {
  CanvasRenderer,
  // SVGRenderer,
} from 'echarts/renderers';
import { parse } from 'query-string';
import { useEffect, useRef } from 'react';

import CommonTable from '@/components/common-table';
import { StatusTag } from '@/components/status-tag';
import { listNode } from '@/services/secretpad/InstController';
import { page, refresh } from '@/services/secretpad/NodeRouteController';
import { Model, useModel } from '@/util/valtio-helper';

import styles from '../index.less';

import { Card } from './card';

echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  Gau<PERSON><PERSON><PERSON>,
  <PERSON>vas<PERSON><PERSON><PERSON>,
]);

const statusMap = {
  Ready: { type: 'success', text: '可用' },
  NoteReady: { type: 'failed', text: '不可用' },
  Succeeded: { type: 'success', text: '可用' },
};

const nodeTableColumns = [
  {
    title: '名称',
    dataIndex: 'nodeName',
    key: 'nodeName',
  },
  {
    title: '状态',
    dataIndex: 'nodeStatus',
    key: 'nodeStatus',
    render: (status: string) => {
      return (
        <StatusTag
          type={statusMap[status as keyof typeof statusMap]?.type || 'failed'}
          text={statusMap[status as keyof typeof statusMap]?.text || '不可用'}
        />
      );
    },
  },
];

export const Nodes = () => {
  const instance = useModel(WorkbenchNodeModel);

  const { ownerId } = parse(window.location.search);

  const editColumn: ColumnType = {
    title: '操作',
    key: 'action',
    render: (_, record) => {
      return (
        <Typography.Link onClick={() => instance.refreshNode(record.routerId)}>
          刷新
        </Typography.Link>
      );
    },
  };

  useEffect(() => {
    instance.getCurrentNodes();
    instance.getCooperativeNodes(ownerId as string);
  }, []);

  return (
    <div className={styles.workbenchNodesWrapper}>
      <Card title="节点详情">
        <div className={styles.nodeWrapper}>
          <div className={styles.nodeTitle}>我的节点</div>
          <Table
            className={styles.nodeTable}
            size="small"
            dataSource={instance.currentNodes}
            scroll={{ y: 100 }}
            columns={[...nodeTableColumns, editColumn]}
            rowKey="nodeId"
            pagination={false}
          />
        </div>
        <div className={styles.nodeWrapper}>
          <div className={styles.nodeTitle}>合作节点</div>
          <Table
            className={styles.nodeTable}
            size="small"
            dataSource={instance.cooperativeNodes}
            scroll={{ y: 100 }}
            rowKey="nodeId"
            columns={[...nodeTableColumns, editColumn]}
            pagination={false}
          />
        </div>
      </Card>
    </div>
  );
};

interface NodeModel {
  nodeId: string;
  nodeName: string;
  nodeStatus: string;
  routerId?: string;
}

class WorkbenchNodeModel extends Model {
  currentNodes: NodeModel[] = [];
  cooperativeNodes: NodeModel[] = [];
  cooperativeNodesCount = 4;

  constructor() {
    super();
  }

  async getCurrentNodes() {
    const res = await listNode();
    const nodes = res.data || [];
    this.currentNodes = nodes.map((node) => ({
      nodeId: node.nodeId || '',
      nodeName: node.nodeName || '',
      nodeStatus: node.nodeStatus || '',
    }));
  }

  async getCooperativeNodes(ownerId: string) {
    const res = await page({
      page: 1,
      size: 100,
      ownerId,
      search: '',
      sort: {},
    });
    this.cooperativeNodes =
      res.data?.list?.map((item) => ({
        nodeId: item.srcNodeId || '',
        nodeName: item.srcNode?.nodeName || '',
        nodeStatus: item.status || '',
        routerId: item.routeId || '',
      })) || [];
  }

  async refreshNode(routerId: string) {
    if (!routerId) {
      message.success('刷新成功');
      return;
    }
    const res = await refresh({
      routerId: routerId,
    });
    message.success('刷新成功');
    const index = this.cooperativeNodes.findIndex((item) => item.routerId === routerId);
    if (index !== -1) {
      this.cooperativeNodes.splice(index, 1, {
        nodeId: res.data?.srcNodeId || '',
        nodeName: res.data?.srcNode?.nodeName || '',
        nodeStatus: res.data?.status || '',
        routerId: routerId,
      });
    }
  }
}
