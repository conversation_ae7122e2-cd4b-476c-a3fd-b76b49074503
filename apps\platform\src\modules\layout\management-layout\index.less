.layoutContainer {
  display: flex;
  height: 100%;

  .menuContainer {
    display: flex;
    height: 100%;
    flex-direction: column;
    justify-content: space-between;
    background: #fff;
    transition: width 0.3s cubic-bezier(0.2, 0, 0, 1) 0s;

    :global {
      .ant-menu {
        width: 100%;
        background-color: transparent;
        border-inline-end: none;
      }

      .ant-menu-item-selected {
        background-color: #9a0000;
      }

      .ant-menu-item:not(.ant-menu-item-selected):hover {
        background-color: #c1615a;
        color: #fff;
      }

      .ant-menu-item {
        // color: #fff;

        &.ant-menu-item-selected {
          color: #fff;
        }
      }
    }
  }

  .unfold {
    width: 208px;
  }

  .fold {
    width: 60px;
  }

  .contentContainer {
    flex: 1;
    padding-right: 10px;
    border-radius: 8px;
    margin-left: 4px;
    background-color: white;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;

      &-track {
        background-color: #f1f1f1;
      }

      &-thumb {
        border-radius: 3px;
        background-color: #999;
      }
    }
  }

  .workbenchContentContainer {
    overflow: auto;
    background-color: transparent;
  }

  .hide {
    display: none;
  }

  .collapseInfo {
    display: flex;
    justify-content: space-around;
    color: rgb(0 0 0 / 65%);
    font-size: 12px;
  }
}
