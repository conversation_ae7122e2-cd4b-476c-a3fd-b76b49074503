.cardContent {
  display: flex;
  gap: 20px;
}
.cardContent .card {
  position: relative;
  width: 200px;
  height: 100%;
  box-sizing: border-box;
  padding: 12px;
  padding-bottom: 0;
  border: 1px solid transparent;
  border-radius: 8px;
  background-color: #f7f8fa;
}
.cardContent .card .cardChecked {
  position: absolute;
  top: 6px;
  right: 6px;
  color: #9a0000;
  font-size: 20px;
}
.cardContent .card:hover,
.cardContent .checked {
  border: 1px solid #9a0000;
}
.cardContent .cardTitle {
  margin-top: 12px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 22px;
}
.cardContent .cardDesc {
  margin-top: 4px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
}
.cardContent .cardImg {
  width: 176px;
  height: 129px;
  background-color: #fff;
  -webkit-user-drag: none;
  user-select: none;
}
.cardContent .cardImg .imgContent {
  display: inline-block;
  width: 100%;
  height: 100%;
  border: none;
  -webkit-user-drag: none;
  user-select: none;
}
