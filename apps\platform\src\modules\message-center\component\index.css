.listItemTitleName {
  color: rgba(0, 0, 0, 0.85);
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}
.listItemTitleName:hover {
  color: #9a0000;
}
.listItemDescText {
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-weight: 400;
}
.listItemTitleTypeTag {
  display: flex;
  width: 80px;
  height: 20px;
  align-items: center;
  border: 1px solid rgba(0, 0, 0, 0.06);
  border-radius: 4px;
  margin-right: 0;
  color: rgba(0, 10, 26, 0.88);
  font-size: 10px;
  font-weight: 400;
}
.messageStateTagContent {
  display: flex;
  cursor: default;
}
.messageStateTagContent .label {
  display: flex;
  height: 20px;
  align-items: center;
  justify-content: center;
  border: none !important;
  border-radius: 4px 0 0 4px;
  margin: 0;
  color: #fff !important;
  font-size: 10px;
  font-weight: 500;
}
.messageStateTagContent .statusText {
  display: flex;
  width: 52px;
  height: 20px;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  border-radius: 0 4px 4px 0;
  border-left: none !important;
  margin-right: 0;
  font-size: 10px;
  font-weight: 400;
}
.nodeStatusListContent {
  display: flex;
  margin: 8px 0;
  margin-top: 0;
  gap: 8px;
}
.nodeStatusListContent .text {
  color: rgba(0, 0, 0, 0.88);
  font-size: 14px;
  font-weight: 400;
}
.nodeStatusListContent .action {
  width: 65px;
  white-space: nowrap;
}
.reasonTooltipContent {
  word-break: break-all;
}
