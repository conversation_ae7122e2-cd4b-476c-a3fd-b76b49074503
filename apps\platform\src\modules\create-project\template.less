.templates {
  display: flex;
  gap: 20px;
}

.template {
  position: relative;
  width: 200px;
  height: 212px;
  box-sizing: border-box;
  padding: 12px;
  border: 1px solid transparent;
  border-radius: 8px;
  background-color: #f7f8fa;

  .templateChecked {
    position: absolute;
    top: 6px;
    right: 6px;
    color: #9a0000;
    font-size: 20px;
  }
}

.template:hover,
.checked {
  border: 1px solid #9a0000;
}

.templateTitle {
  margin-top: 12px;
  color: rgb(0 0 0 / 85%);
  font-size: 14px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 22px;
}

.templateDesc {
  margin-top: 4px;
  color: rgb(0 0 0 / 45%);
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
}

.templateImg {
  width: 176px;
  height: 129px;
  background-color: #fff;
  -webkit-user-drag: none;
  user-select: none;

  .imgContent {
    display: inline-block;
    width: 100%;
    height: 100%;
    border: none;
    -webkit-user-drag: none;
    user-select: none;
  }
}

.popverTemplateImage {
  width: 300px;

  :global {
    img {
      width: 100%;
    }
  }
}

:global {
  .create-project-tour {
    width: 331px;
    height: 146px;

    .ant-tour-content {
      height: 100%;
    }

    .ant-tour-inner {
      display: flex;
      height: 100%;
      flex-direction: column;
    }

    .ant-tour-description {
      flex: 1;
      color: rgb(255 255 255 / 88%);
      font-size: 14px;
      font-weight: 400;
    }
  }
}
