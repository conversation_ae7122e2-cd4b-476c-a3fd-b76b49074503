import { Button, Form, Select, Table, Tag } from 'antd';
import { useWatch } from 'antd/es/form/Form';
import { parse } from 'query-string';
import { useEffect, useState } from 'react';
import { useLocation } from 'umi';

import { DataManagerService } from '@/modules/data-manager/data-manager.service';
import { ToggleButton } from '@/modules/model-manager/model-release/common';
import type { Datatables } from '@/modules/project-psi/components/datasets';
import type { AllocatedNode } from '@/modules/project-psi/project-psi.service';
import { useModel } from '@/util/valtio-helper';

import style from '../index.less';
import { ProjectPredictionService } from '../project-prediction.service';

interface FeatureDataValue {
  nodeId?: string;
  datatableId?: string;
  indexId?: string;
  features?: {
    offlineName: string;
    onlineName: string | undefined;
  }[];
}
type FeatureRecord = NonNullable<FeatureDataValue['features']>[number];

interface FeatureTableProps {
  type: 'local' | 'remote';
  value?: FeatureDataValue;
  onChange?: (value: FeatureDataValue) => void;
  parties?: AllocatedNode[];
  modelDetail?: API.ModelPackDetailVO;
  editable?: boolean;
  disabled?: boolean;
}

export const checkMatchStatus = (features: FeatureRecord[]) => {
  const matchStatus = features.every((item) => item.onlineName === item.offlineName);
  return matchStatus;
};

export const FeatureTable = (props: FeatureTableProps) => {
  const {
    value,
    onChange,
    parties,
    type,
    editable = true,
    modelDetail,
    disabled,
  } = props;
  const [btnDisabled, setBtnDisabled] = useState(true);
  const [toggle, setToggle] = useState(editable ? false : true);

  const form = Form.useFormInstance();
  const modelId = useWatch('modelId', form);

  const service = useModel(ProjectPredictionService);
  const dataManagerService = useModel(DataManagerService);
  const { search } = useLocation();
  const parsedSearch = parse(search);

  const { ownerId } = parsedSearch as { ownerId: string };

  const [datatables, setDatatables] = useState<Datatables<typeof type>>([]);
  const [nodeOptions, setNodeOptions] = useState<{ label: string; value: string }[]>(
    [],
  );
  const [datatableOptions, setDatatableOptions] = useState<
    { label: string; value: string }[]
  >([]);
  const [indexOptions, setIndexOptions] = useState<{ label: string; value: string }[]>(
    [],
  );

  const [matchStatus, setMatchStatus] = useState<boolean | undefined>();

  const getDatatables = async (nodeId: string) => {
    const res = await service.getPredictionDatatables(nodeId);
    if (res) {
      setDatatables(res);
      setDatatableOptions(
        res.map((item) => ({
          label: item.datatableName,
          value: item.datatableId,
        })),
      );
    }
  };

  const getLocalDatatables = async () => {
    const res = await dataManagerService.listDataTables(ownerId, 1, 1000);
    if (res) {
      const localDatatables = res.datatableNodeVOList?.map(
        (item) => item.datatableVO,
      ) as Datatables<'local'>;
      setDatatables(localDatatables);
      setDatatableOptions(
        localDatatables.map((item) => ({
          label: item.datatableName || '',
          value: item.datatableId || '',
        })),
      );
    }
  };

  const triggerChange = (changedValue: FeatureDataValue) => {
    onChange?.({
      ...(value || {}),
      ...changedValue,
    });
  };

  const columns = [
    {
      title: '入模特征',
      dataIndex: 'offlineName',
      key: 'offlineName',
      render: (text: string) => <div style={{ marginLeft: 24 }}>{text}</div>,
    },
    {
      title: '在线特征',
      dataIndex: 'onlineName',
      key: 'onlineName',
      render: (text: string, record: FeatureRecord, index: number) => {
        if (!editable) return <div>{text}</div>;
        return (
          <Select
            placeholder="请手动选择"
            size="small"
            value={text}
            defaultValue={record.onlineName}
            options={indexOptions}
            onChange={(val) => {
              triggerChange({
                features: value?.features?.map((item, idx) =>
                  idx === index ? { ...item, onlineName: val } : item,
                ),
              });
            }}
          />
        );
      },
    },
  ];

  const onNodeChange = (val: string) => {
    triggerChange({
      ...(value || {}),
      nodeId: val,
    });
    getDatatables(val);
  };

  const onDatatableChange = (val: string) => {
    const datatable = datatables.find((item) => item.datatableId === val);
    if (!datatable) return;

    let flag = true;
    const party = modelDetail?.parties?.find(
      (item) => item.nodeId === (value?.nodeId || parties?.[0].nodeId),
    );
    const features = party?.columns.map((item: string) => {
      if (datatable.schema?.find((option) => option.colName === item)) {
        return {
          offlineName: item,
          onlineName: item,
        };
      } else {
        flag = false;
        return {
          offlineName: item,
          onlineName: undefined,
        };
      }
    });

    setMatchStatus(flag);

    triggerChange({
      ...(value || {}),
      nodeId: value?.nodeId || parties?.[0].nodeId,
      datatableId: val,
      indexId: undefined,
      features,
    });

    setIndexOptions(
      datatable.schema?.map((item) => ({
        label: item.colName || '',
        value: item.colName || '',
      })) || [],
    );
  };

  const onIndexChange = (val: string) => {
    triggerChange({
      ...(value || {}),
      indexId: val,
    });
  };

  useEffect(() => {
    if (value?.features?.length) {
      setBtnDisabled(false);
      setMatchStatus(checkMatchStatus(value.features));
    } else {
      setBtnDisabled(true);
      setMatchStatus(undefined);
      setToggle(false);
    }
  }, [value?.features]);

  useEffect(() => {
    if (!parties) return;
    if (type !== 'local') {
      setNodeOptions(
        parties
          .slice(1)
          .filter((party) =>
            modelDetail?.parties?.find(
              (item: Record<string, any>) => item.nodeId === party.nodeId,
            ),
          )
          .map((party) => ({ label: party.instName, value: party.nodeId })),
      );
    } else {
      setNodeOptions(
        parties.map((party) => ({ label: party.instName, value: party.nodeId })),
      );
      getLocalDatatables();
    }
  }, [parties, modelDetail]);

  useEffect(() => {
    if (!modelId) {
      if (type !== 'local') {
        setNodeOptions([]);
        setDatatableOptions([]);
        setIndexOptions([]);
        setMatchStatus(undefined);
        setBtnDisabled(true);
      } else {
        setIndexOptions([]);
        setMatchStatus(undefined);
        setBtnDisabled(true);
      }
    }
  }, [modelId]);

  return (
    <>
      <div className={style.featureTableHeader}>
        <div className={style.headerNode}>预测节点</div>
        <div className={style.headerDatatable}>数据表</div>
        <div className={style.headerIndex}>索引列</div>
        <div className={style.headerOptions}>匹配结果</div>
      </div>
      <div className={style.featureTableBody}>
        <ToggleButton disabled={btnDisabled} value={toggle} onChange={setToggle} />
        <div className={style.bodyNode}>
          {editable ? (
            <Select
              size="small"
              options={nodeOptions}
              disabled={type === 'local'}
              defaultValue={type === 'local' ? parties?.[0]?.nodeId : undefined}
              value={value?.nodeId}
              onChange={onNodeChange}
            ></Select>
          ) : (
            value?.nodeId
          )}
        </div>
        <div className={style.bodyDatatable}>
          {editable ? (
            <Select
              size="small"
              options={datatableOptions}
              disabled={disabled}
              value={value?.datatableId}
              onChange={onDatatableChange}
            ></Select>
          ) : (
            value?.datatableId
          )}
        </div>
        <div className={style.bodyIndex}>
          {editable ? (
            <Select
              size="small"
              options={indexOptions}
              value={value?.indexId}
              disabled={disabled}
              onChange={onIndexChange}
            ></Select>
          ) : (
            value?.indexId
          )}
        </div>
        <div className={style.bodyOptions}>
          {matchStatus !== undefined ? (
            <Tag color={matchStatus ? 'success' : 'error'}>
              匹配{matchStatus ? '成功' : '失败'}
            </Tag>
          ) : (
            <Tag color="default">未匹配</Tag>
          )}
        </div>
      </div>
      {toggle && (
        <Table
          size="small"
          columns={columns}
          dataSource={value?.features}
          rowKey="offlineName"
        />
      )}
    </>
  );
};
