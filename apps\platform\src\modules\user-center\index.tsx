import { SearchOutlined } from '@ant-design/icons';
import { Tag, Button, Input } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { useState } from 'react';

import CommonTable from '@/components/common-table';

import AddUserComponent from './components/add-user';
import styles from './index.less';

interface UserCenterTableData {
  account?: string;
  username?: string;
  role?: string;
  lastLoginTime: string;
  status: string;
}

const columns: ColumnsType<UserCenterTableData> = [
  {
    title: '账号',
    dataIndex: 'account',
    key: 'account',
  },
  {
    title: '用户名',
    dataIndex: 'username',
    key: 'username',
  },
  {
    title: '角色',
    dataIndex: 'role',
    key: 'role',
  },
  {
    title: '最近登录时间',
    dataIndex: 'lastLoginTime',
    key: 'lastLoginTime',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    render: (status: string) => (
      <Tag color={status === 'active' ? 'green' : 'red'}>
        {status === 'active' ? '正常' : '禁用'}
      </Tag>
    ),
  },
];

const UserCenterComponent = () => {
  const [visible, setVisible] = useState(false);

  const actionColumn = {
    title: '操作',
    key: 'action',
    width: 200,
    render: () => (
      <div>
        <Button type="text" size="small">
          重置密码
        </Button>
        <Button type="text" size="small">
          编辑
        </Button>
        <Button type="text" size="small" danger>
          停用
        </Button>
      </div>
    ),
  };

  const tableColumns = [...columns, actionColumn];
  return (
    <div className={styles.userCenterWrapper}>
      <div className={styles.userCenterHeader}>
        <div className={styles.title}>成员管理</div>
        <div>
          <Input
            placeholder="搜索账号/用户名"
            style={{ width: 200 }}
            suffix={<SearchOutlined style={{ color: '#aaa' }} />}
          />
          <Button
            type="primary"
            style={{ marginLeft: 16 }}
            onClick={() => setVisible(true)}
          >
            添加成员
          </Button>
        </div>
      </div>
      <CommonTable
        size="middle"
        columns={tableColumns as ColumnsType}
        dataSource={[{ account: '123' }]}
      />
      <AddUserComponent open={visible} onClose={() => setVisible(false)} />
    </div>
  );
};

export default UserCenterComponent;
