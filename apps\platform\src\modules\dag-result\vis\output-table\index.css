.VisOutputTableContent .itemClickButton {
  margin-bottom: 16px;
}
.exportBtn {
  position: absolute;
  top: -36px;
  right: 0;
}
.exportBtn button {
  color: rgba(0, 10, 26, 0.68);
}
.exportBtn button:hover {
  color: #9a0000 !important;
}
.outPutTableItem .ant-table-column-title {
  white-space: nowrap !important;
}
.fullScreenContentPage {
  overflow: auto;
  padding-bottom: 24px;
  background-color: #fff;
}
.fullScreenContentPage .fullScreenHeader {
  display: flex;
  height: 56px;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  border: 1px solid #eee;
  margin-bottom: 16px;
}
.fullScreenContentPage .fullScreenHeader .title {
  color: #1d2129;
  font-size: 20px;
  font-weight: 500;
  letter-spacing: 0;
}
.fullScreenContentPage .fullScreenHeader .exit {
  color: rgba(0, 10, 26, 0.68);
  cursor: pointer;
  font-size: 14px;
  font-weight: 400;
}
.fullScreenContentPage .fullScreenContentWrap {
  padding: 0 24px;
}
.fullScreenContentPage .customBtn {
  color: rgba(0, 10, 26, 0.68);
}
.fullScreenContentPage .customBtn:hover {
  color: #9a0000 !important;
}
