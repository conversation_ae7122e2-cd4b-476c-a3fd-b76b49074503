export const routes = [
  {
    path: '/',
    wrappers: ['@/wrappers/theme-wrapper', '@/wrappers/login-auth'],
    component: '@/modules/layout/layout.view',
    routes: [
      {
        path: '/',
        component: 'edge',
        wrappers: ['@/wrappers/basic-node-auth', '@/wrappers/p2p-login-auth'],
      },
      // { path: '/', component: 'new-home', wrappers: ['@/wrappers/center-auth'] },
      {
        path: '/center',
        component: 'new-home',
        wrappers: ['@/wrappers/center-auth'],
        routes: [
          { path: 'node', component: '@/modules/managed-node-list/index' },
          { path: 'project', component: '@/modules/project-list/index' },
          { path: 'project/dag', component: '@/modules/dag-layout/index' },
          { path: 'log', component: '@/modules/log-list/index' },
        ],
      },
      {
        path: '/dag',
        component: 'dag',
        wrappers: ['@/wrappers/p2p-center-auth', '@/wrappers/component-wrapper'],
      },
      {
        path: '/record',
        component: 'record',
        wrappers: ['@/wrappers/p2p-center-auth', '@/wrappers/component-wrapper'],
      },
      {
        path: '/model-submission',
        component: 'model-submission',
        wrappers: ['@/wrappers/p2p-center-auth', '@/wrappers/component-wrapper'],
      },
      {
        path: '/periodic-task-detail',
        component: 'periodic-task-detail',
        wrappers: ['@/wrappers/p2p-center-auth', '@/wrappers/component-wrapper'],
      },
      {
        path: '/node',
        component: 'new-node',
        wrappers: ['@/wrappers/edge-auth', '@/wrappers/component-wrapper'],
        routes: [
          { path: 'data-source', component: '@/modules/data-source-list/index' },
          {
            path: 'data-manage',
            component: '@/modules/data-manager/data-manager.view',
          },
          { path: 'cooperative', component: '@/modules/cooperative-node-list/index' },
          { path: 'result', component: '@/modules/result-manager/result-manager.view' },
        ],
      },
      {
        path: '/my-node',
        component: 'my-node',
        wrappers: ['@/wrappers/basic-node-auth', '@/wrappers/p2p-edge-center-auth'],
      },
      {
        path: '/message',
        component: 'message',
        wrappers: [
          '@/wrappers/basic-node-auth',
          '@/wrappers/p2p-edge-center-auth',
          '@/wrappers/component-wrapper',
        ],
      },
      {
        path: '/edge',
        component: 'edge',
        wrappers: ['@/wrappers/basic-node-auth', '@/wrappers/p2p-login-auth'],
        routes: [
          { path: 'home', component: '@/modules/p2p-workbench/workbench.view' },
          { path: 'data-source', component: '@/modules/data-source-list/index' },
          {
            path: 'data-manager',
            component: '@/modules/data-manager/data-manager.view',
          },
          { path: 'data-manager/auth', component: '@/modules/data-manager/auth' },
          { path: 'data-pool', component: '@/modules/data-pool/index' },
          { path: 'my-project', component: '@/modules/p2p-project-list/index' },
          { path: 'my-project/dag', component: '@/modules/dag-layout/index' },
          {
            path: 'prediction',
            component: '@/modules/project-prediction/index',
          },
          { path: 'prediction/:id', component: '@/modules/project-prediction/detail' },
          { path: 'psi', component: '@/modules/project-psi/index' },
          { path: 'psi/:id', component: '@/modules/project-psi/detail' },
          {
            path: 'cooperative-node',
            component: '@/modules/cooperative-node-list/index',
          },
          { path: 'messages', component: '@/modules/message-center/edge' },
          { path: 'logs', component: '@/modules/log-list/index' },
          { path: 'my-node', component: '@/modules/my-node/index' },
        ],
      },
      { path: '/*', redirect: '/login' },
    ],
  },
  {
    path: '/',
    wrappers: [
      '@/wrappers/theme-wrapper',
      '@/wrappers/login-auth',
      '@/wrappers/guide-auth',
    ],
    component: '@/modules/layout/layout.view',
    routes: [{ path: '/guide', component: 'guide' }],
  },
  {
    path: '/login',
    wrappers: ['@/wrappers/theme-wrapper', '@/wrappers/login-wrapper'],
    component: 'login',
  },
];
