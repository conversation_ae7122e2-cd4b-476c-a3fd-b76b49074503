import { RedoOutlined } from '@ant-design/icons';
import { Descriptions, message, Modal, Space, Tabs } from 'antd';
import type { TabsProps } from 'antd';
import { parse } from 'query-string';
import React, { useEffect } from 'react';

import { hasAccess, Platform } from '@/components/platform-wrapper';
import { StatusTag } from '@/components/status-tag';
import { DataSheetType } from '@/modules/data-manager/data-manager.service';
import { getDatatable } from '@/services/secretpad/DatatableController';
import { Model, useModel } from '@/util/valtio-helper';

import { DataTableStructure } from './component/data-table-structure';
import styles from './index.less';

interface PropsData {
  tableInfo: API.DatatableVO & { currentNodeId: string };
}

interface IProps<T> {
  visible: boolean;
  close: () => void;
  data: T;
  origin?: string;
}

export const DataSheetText = {
  [DataSheetType.CSV]: '节点本地数据',
  [DataSheetType.HTTP]: 'HTTP数据',
  [DataSheetType.OSS]: 'OSS数据',
};

export const DataTableInfoDrawer: React.FC<IProps<PropsData>> = (props) => {
  const { visible, close, data, origin } = props;
  const viewInstance = useModel(DataTableInfoDrawerView);
  const isAutonomy = hasAccess({ type: [Platform.AUTONOMY] });

  const tableInfo: API.DatatableVO & { currentNodeId: string } = viewInstance.tableInfo;

  useEffect(() => {
    viewInstance.tableInfo = data.tableInfo || {};
  }, [viewInstance, data]);

  const tabItems: TabsProps['items'] = [
    {
      key: '1',
      label: '数据表结构',
      children: <DataTableStructure schema={tableInfo.schema || []} />,
    },
    // {
    //   key: '2',
    //   label: '授权信息',
    //   children: <DataTableAuthComponent tableInfo={tableInfo} size="small" />,
    // },
  ];
  return (
    <Modal
      title={
        <div>
          <Space style={{ fontSize: 16 }}>
            「{tableInfo.datatableName}」 详情{' '}
            <Space>
              {tableInfo.status === 'Available' ? (
                <StatusTag type="success" text="可用" />
              ) : (
                <StatusTag type="failed" text="不可用" />
              )}
            </Space>
            <a
              style={{ fontSize: 14 }}
              onClick={() =>
                viewInstance.refreshTableInfo(
                  tableInfo as API.DatatableVO & { currentNodeId: string },
                  isAutonomy,
                  origin,
                )
              }
            >
              <RedoOutlined spin={viewInstance.refreshing} /> 刷新状态
            </a>
          </Space>
        </div>
      }
      width={700}
      open={visible}
      onCancel={close}
      footer={null}
    >
      <Descriptions title="" column={2}>
        <Descriptions.Item label="所属数据源">
          {tableInfo.datasourceName}
        </Descriptions.Item>
        <Descriptions.Item label="数据源类型">
          {tableInfo?.datasourceType}
        </Descriptions.Item>
        {isAutonomy && (
          <Descriptions.Item label="所属节点">
            {tableInfo?.nodeName || tableInfo?.nodeId || '-'}
          </Descriptions.Item>
        )}
        <Descriptions.Item span={2} label="数据地址">
          {tableInfo.relativeUri || '-'}
        </Descriptions.Item>
        <Descriptions.Item span={2} label="空缺值">
          {tableInfo?.nullStrs?.map((i) => `"${i}"`).join(',') || '-'}
        </Descriptions.Item>
        <Descriptions.Item span={2} label="描述">
          {tableInfo.description || '-'}
        </Descriptions.Item>
      </Descriptions>
      <div className={styles.tableWrapper}>
        <Tabs
          defaultActiveKey="1"
          destroyInactiveTabPane={true}
          tabBarStyle={{ border: 'none' }}
          items={tabItems}
        />
      </div>
    </Modal>
  );
};

export const DataTableInfoId = 'DataTableInfoId';

type DatatableVO = API.DatatableVO;

export class DataTableInfoDrawerView extends Model {
  tableInfo: DatatableVO & { currentNodeId: string } = {
    currentNodeId: '',
  };

  refreshing = false;

  async refreshTableInfo(
    tableInfo: API.DatatableVO & { currentNodeId: string },
    isAutonomy: boolean,
    origin?: string,
  ) {
    this.refreshing = true;
    const { ownerId } = parse(window.location.search);
    const refreshTableNodeId = isAutonomy ? tableInfo?.nodeId : ownerId;
    const response = await getDatatable({
      datatableId: tableInfo.datatableId,
      nodeId:
        origin === 'outer' ? tableInfo.currentNodeId : (refreshTableNodeId as string),
      type: tableInfo.type,
      datasourceType: tableInfo.datasourceType || 'LOCAL',
    });

    setTimeout(() => {
      this.refreshing = false;
    }, 500);

    const tableInfoData = {
      ...(response?.data?.datatableVO || {}),
      nodeId: origin === 'outer' ? tableInfo.nodeId : response?.data?.nodeId,
      nodeName: origin === 'outer' ? tableInfo.nodeName : response?.data?.nodeName,
    };
    this.tableInfo = {
      currentNodeId: tableInfo.currentNodeId,
      ...tableInfoData,
    };
    message.success('数据状态刷新成功');
  }
}
