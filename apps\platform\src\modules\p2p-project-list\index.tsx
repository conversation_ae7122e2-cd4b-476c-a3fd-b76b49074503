import { EditOutlined, SearchOutlined } from '@ant-design/icons';
import { Empty, Flex, Tag } from 'antd';
import { Button, Typography, Tooltip, Input, Space } from 'antd';
import { Spin } from 'antd';
import classNames from 'classnames';
import { parse } from 'query-string';
import type { ChangeEvent } from 'react';
import { useEffect, useState } from 'react';
import React from 'react';
import { history, useLocation } from 'umi';

import { EdgeRouteWrapper, isP2PWorkbench } from '@/components/platform-wrapper';
import { TabSearchBox } from '@/components/tab-search-box';
import { P2PCreateProjectModal } from '@/modules/create-project/p2p-create-project/p2p-create-project.view';
import { formatTimestamp } from '@/modules/dag-result/utils';
import { EditProjectModal } from '@/modules/project-list/components/edit-project';
import { getModel, Model, useModel } from '@/util/valtio-helper';

import type { ProjectType } from '../create-project/p2p-create-project/compute-func-data';
import { DefaultModalManager } from '../dag-modal-manager';
import {
  P2pProjectDetailDrawer,
  p2pProjectDetailDrawer,
} from '../p2p-project-detail/project-detail-drawer';
import { AuthProjectTag } from '../p2p-project-list/components/auth-project-tag';
import {
  SelectProjectState,
  checkAllApproved,
} from '../p2p-project-list/components/common';

import {
  ComputeModeType,
  P2pProjectButtons,
  ProjectComputeModeSelect,
  ProjectStateSelect,
  ProjectStatus,
  RadioGroup,
  RadioGroupState,
  computeModeText,
  ProjectListBtns,
} from './components/common';
import { ProjectTypeTag } from './components/project-type-tag';
import styles from './index.less';
import { P2pProjectListService } from './p2p-project-list.service';

export enum TabKey {
  'PARTIES' = 'parties',
  'PIPELINES' = 'pipelines',
  'TASKS' = 'tasks',
}

const P2pProjectListComponent: React.FC = () => {
  const projectListModel = useModel(ProjectListModel);
  const p2pProjectService = useModel(P2pProjectListService);
  const modalManager = useModel(DefaultModalManager);
  const { pathname } = useLocation();

  const { handleCreateProject } = projectListModel;

  const [isModalOpen, setIsModalOpen] = useState(false);

  const { displayProjectList: projectList } = p2pProjectService;

  const { Title, Paragraph } = Typography;

  const { ownerId } = parse(window.location.search);

  useEffect(() => {
    p2pProjectService.getListProject();
  }, []);

  const [editProjectData, setEditProjectData] = useState({});

  const [hoverCurrent, setHoverCurrent] = useState(-1);

  const [searchInput, setSearchInput] = useState('');
  const searchProject = (e: ChangeEvent<HTMLInputElement>) => {
    setSearchInput(e.target.value);
    projectListModel.searchProject(e.target.value);
  };

  useEffect(() => {
    setSearchInput('');
  }, [
    projectListModel.radioFilterState,
    projectListModel.computeMode,
    projectListModel.selectState,
  ]);

  const handleOpenProjectDetail = (item: API.ProjectVO, tabKey: string) => {
    return () => {
      modalManager.openModal(p2pProjectDetailDrawer.id, {
        ...item,
        tabKey,
      });
    };
  };

  const tabs = [
    { label: '全部', value: '' },
    { label: '我发起', value: 'APPLY' },
    { label: '我受邀', value: 'PROCESS' },
  ];

  return (
    <div className={styles.projectList}>
      <EdgeRouteWrapper>
        <TabSearchBox
          tabs={tabs}
          onTabChange={(val) =>
            projectListModel.changefilterState(val as RadioGroupState)
          }
        >
          <Space size="middle" wrap>
            <span>项目名称</span>
            <Input
              placeholder="请输入项目名称"
              onChange={(e) => searchProject(e)}
              style={{ width: 200 }}
              value={searchInput}
              suffix={
                <SearchOutlined
                  style={{
                    color: '#aaa',
                  }}
                />
              }
            />
            {/* <RadioGroup
              value={projectListModel.radioFilterState}
              onChange={projectListModel.changefilterState}
            /> */}
            <span>计算模式</span>
            <ProjectComputeModeSelect
              onChange={projectListModel.onSelectProject}
              value={projectListModel.computeMode}
            />
            <span>项目状态</span>
            <ProjectStateSelect
              onChange={projectListModel.changeProjectState}
              value={projectListModel.selectState}
            />
          </Space>
        </TabSearchBox>
      </EdgeRouteWrapper>
      <Spin
        spinning={projectListModel.projectListService.projectListLoading}
        className={styles.spin}
      >
        <div></div>
      </Spin>
      <div className={styles.listWrapper}>
        <Flex justify="end">
          <Button type="primary" onClick={handleCreateProject}>
            新建项目
          </Button>
        </Flex>
        <P2PCreateProjectModal
          visible={projectListModel.showCreateProjectModel}
          close={() => {
            projectListModel.showCreateProjectModel = false;
          }}
          onOk={() => p2pProjectService.getListProject()}
        />
        {projectList.length === 0 ? (
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        ) : (
          <div className={styles.content}>
            {projectList.map((item, index) => {
              return (
                <div
                  className={styles.projectBox}
                  key={item.projectId}
                  onMouseEnter={() => {
                    setHoverCurrent(index);
                  }}
                  onMouseLeave={() => {
                    setHoverCurrent(-1);
                  }}
                >
                  <div>
                    <div className={styles.listBox}>
                      {item.status === ProjectStatus.ARCHIVED && (
                        <div className={styles.archiveTag}>
                          <span>已归档</span>
                        </div>
                      )}
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        {/* <Tooltip
                          title={
                            item.computeMode === ComputeModeType.TEE
                              ? item.teeNodeId
                              : ''
                          }
                        >
                          <Tag className={styles.computeModeTag}>
                            {computeModeText[
                              item.computeMode as keyof typeof computeModeText
                            ] || computeModeText[ComputeModeType.MPC]}
                          </Tag>
                        </Tooltip>
                        <div style={{ marginRight: 8 }}>
                          <ProjectTypeTag
                            type={(item.computeFunc as ProjectType) || 'DAG'}
                          />
                        </div> */}
                        <div className={styles.header} style={{ flex: 1 }}>
                          <div className={styles.bar}></div>
                          <Tooltip title={item.projectName}>
                            <Title
                              className={styles.ellipsisName}
                              level={5}
                              ellipsis={true}
                            >
                              {item.projectName}
                            </Title>
                          </Tooltip>
                          {/* 只有项目发起方才可编辑，并且项目不是已归档项目 */}
                          {item.status !== ProjectStatus.ARCHIVED &&
                            item.initiator === ownerId && (
                              <EditOutlined
                                className={styles.editButton}
                                onClick={() => {
                                  setIsModalOpen(true);
                                  setEditProjectData(item);
                                }}
                              />
                            )}
                        </div>
                      </div>
                      <Paragraph ellipsis={{ rows: 1 }} className={styles.ellipsisDesc}>
                        {item.description || '暂无描述'}
                      </Paragraph>
                      {/* 有受邀方没有通过 */}
                      {!checkAllApproved(item) && (
                        <div className={styles.authProjectTagContent}>
                          <AuthProjectTag
                            currentInst={{ id: ownerId as string }}
                            simple={hoverCurrent !== index}
                            project={item}
                          />
                        </div>
                      )}
                      {/* 所有的受邀方都通过展示 */}
                      {checkAllApproved(item) && (
                        <div className={styles.projects}>
                          <div className={styles.task}>
                            <div
                              className={styles.count}
                              onClick={handleOpenProjectDetail(item, TabKey.PARTIES)}
                            >
                              {[
                                {
                                  instId: item.initiator,
                                  nodeName: item.initiatorName,
                                },
                                ...(item.partyVoteInfos || []),
                              ].length || 0}
                            </div>
                            <div className={styles.titleName}>参与机构</div>
                          </div>
                          <div className={styles.line}></div>
                          <div className={styles.task}>
                            <span
                              className={styles.count}
                              onClick={handleOpenProjectDetail(item, TabKey.PIPELINES)}
                            >
                              {item.graphCount}
                            </span>
                            <div className={styles.titleName}>训练流</div>
                          </div>
                          <div className={styles.line}></div>
                          <div className={styles.task}>
                            <div
                              className={styles.count}
                              onClick={handleOpenProjectDetail(item, TabKey.TASKS)}
                            >
                              {item.jobCount}
                            </div>
                            <div className={styles.titleName}>任务数</div>
                          </div>
                        </div>
                      )}
                      <ProjectListBtns
                        timeStr={formatTimestamp(item.gmtCreate as string)}
                        project={item}
                      />
                      {/* <div className={styles.time}>
                        创建于{formatTimestamp(item.gmtCreate as string)}
                      </div> */}
                    </div>
                    {/* <div className={styles.bootom}>
                      <P2pProjectButtons project={item} />
                    </div> */}
                  </div>
                </div>
              );
            })}
            {!isP2PWorkbench(pathname) && (
              <>
                <i></i>
                <i></i>
                <i></i>
              </>
            )}
          </div>
        )}
      </div>

      <EditProjectModal
        isModalOpen={isModalOpen}
        handleCancel={() => setIsModalOpen(false)}
        data={editProjectData}
        onEdit={p2pProjectService.projectEdit}
      />
      <P2pProjectDetailDrawer />
    </div>
  );
};

export class ProjectListModel extends Model {
  readonly projectListService;

  constructor() {
    super();
    this.projectListService = getModel(P2pProjectListService);
  }

  instId: string | undefined = undefined;

  onViewMount() {
    const { ownerId } = parse(window.location.search);
    if (ownerId) {
      this.instId = ownerId as string;
    }
    this.resetFilters();
  }

  pipelines: API.GraphMetaVO[] = [];

  showCreateProjectModel = false;

  radioFilterState = RadioGroupState.ALL;
  selectState = SelectProjectState.ALL;
  computeMode = ComputeModeType.ALL;

  changefilterState = (value: RadioGroupState) => {
    this.resetFilters();
    this.radioFilterState = value;
    this.projectListService.displayProjectList =
      this.projectListService.projectList.filter((i) => {
        if (value === RadioGroupState.ALL) {
          return i;
        } else if (value === RadioGroupState.APPLY) {
          return i.initiator && i.initiator === this.instId;
        } else if (value === RadioGroupState.PROCESS) {
          return (
            i.partyVoteInfos &&
            (i.partyVoteInfos || []).some((item) => item.partyId === this.instId)
          );
        }
      });
  };

  changeProjectState = (value: SelectProjectState) => {
    this.resetFilters();
    this.selectState = value;
    this.projectListService.displayProjectList =
      this.projectListService.projectList.filter((i) => {
        if (!i.status) return;
        if (value === SelectProjectState.ALL) {
          return i;
        } else if (value === SelectProjectState.ARCHIVED) {
          return i.status && i.status === SelectProjectState.ARCHIVED;
        } else if (value === SelectProjectState.REVIEWING) {
          return i.status && i.status === SelectProjectState.REVIEWING;
        }
      });
  };

  searchProject = (value: string) => {
    this.projectListService.displayProjectList =
      this.projectListService.projectList.filter((i) => {
        if (!i.projectName) return;
        return i.projectName?.indexOf(value) >= 0;
      });
  };

  onSelectProject = (e: string) => {
    this.resetFilters();
    this.computeMode = e as ComputeModeType;
    this.projectListService.displayProjectList =
      this.projectListService.projectList.filter((i) => {
        if (e === ComputeModeType.ALL) {
          return i;
        } else if (e === ComputeModeType.TEE) {
          return i.computeMode && i.computeMode.indexOf(ComputeModeType.TEE) >= 0;
        } else if (e === ComputeModeType.MPC) {
          // 兼容除tee外的
          return i.computeMode && !(i.computeMode.indexOf(ComputeModeType.TEE) >= 0);
        }
      });
  };

  resetFilters = () => {
    this.computeMode = ComputeModeType.ALL;
    this.radioFilterState = RadioGroupState.ALL;
    this.selectState = SelectProjectState.ALL;
  };

  handleCreateProject = () => {
    this.showCreateProjectModel = true;
  };
}

export default P2pProjectListComponent;
