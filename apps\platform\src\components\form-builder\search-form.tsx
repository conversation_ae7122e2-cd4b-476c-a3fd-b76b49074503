import { DownOutlined } from '@ant-design/icons';
import { Form, Input, Button, Select, Row, Col, Space, DatePicker, Switch } from 'antd';
import type { FormInstance, FormItemProps } from 'antd/es/form';
import classNames from 'classnames';
import { cloneElement, forwardRef, useImperativeHandle, useState } from 'react';

import { AsyncOptionsSelect, ControlledSelect } from './enhanced-items';
import EventBus from './form-eventbus';
import style from './index.module.less';

export enum FormItemType {
  INPUT = 'input',
  TEXTAREA = 'textarea',
  PASSWORD = 'password',
  SELECT = 'select',
  CONTROLLED_SELECT = 'controlled_select',
  ASYNC_SELECT = 'async_select',
  DATE_PICKER = 'datePicker',
  RANGE_PICKER = 'rangePicker',
  SWITCH = 'switch',
  CUSTOM = 'custom',
}

export interface FormItemConfig {
  type: FormItemType;
  name: string;
  hidden?: boolean;
  customNode?: (form?: FormInstance) => React.ReactElement;
  watchFields?: string[];
  watchHooks?: Record<string, (value: any, form: FormInstance) => void>;
  itemProps?: FormItemProps;
  // 组件数据
  props?: {
    [key: string]: any; // 其他属性
  };
}

export interface SearchFormProps {
  config: FormItemConfig[];
  onSearch: (values: Record<string, any>) => void;
  collapse?: boolean;
  collapsedRow?: number;
}

export const FormItem = (props: {
  data: FormItemConfig;
  children: React.ReactElement;
  form?: FormInstance;
  inline?: boolean;
}) => {
  const { data, form, children, inline } = props;

  const enhancedChild = cloneElement(children, {
    onChange: (event: any) => {
      const value = event?.target?.value ?? event;

      // 调用原始 onChange
      if (children.props.onChange) {
        children.props.onChange(event);
      }

      // 判断当前FormItem是否需要派发更新
      const bus = EventBus.getInstance();
      if (bus.getEvents().get(data.name)) {
        bus.publish(data.name, value, form);
      }

      // // 自定义副作用
      // if (extraEffect) {
      //   extraEffect(value, form);
      // }

      // // 同步表单值
      // form?.setFieldsValue({ [data.name]: value });
    },
  });

  return !data.hidden ? (
    <Form.Item
      name={data.name}
      className={classNames({
        [style.searchFormItem]: inline,
      })}
      required={data.itemProps?.required}
      {...data.itemProps}
    >
      {enhancedChild}
    </Form.Item>
  ) : (
    <></>
  );
};

export const getFieldItem = (
  data: FormItemConfig,
  form?: FormInstance,
): React.ReactElement => {
  console.log('field render');
  switch (data.type) {
    case FormItemType.INPUT:
      return <Input {...data.props} />;
    case FormItemType.TEXTAREA:
      return <Input.TextArea {...data.props} />;
    case FormItemType.PASSWORD:
      return <Input.Password {...data.props} />;
    case FormItemType.SELECT:
      return <Select {...data.props} />;
    case FormItemType.CONTROLLED_SELECT:
      return <ControlledSelect {...data.props} />;
    case FormItemType.ASYNC_SELECT:
      return (
        <AsyncOptionsSelect fetchOptions={data.props?.fetchOptions} {...data.props} />
      );
    case FormItemType.RANGE_PICKER:
      return <DatePicker.RangePicker {...data.props} />;
    case FormItemType.SWITCH:
      return <Switch {...data.props} />;
    case FormItemType.CUSTOM:
      return data.customNode ? data.customNode(form) : <></>;
    default:
      return <></>;
  }
};

const SearchForm = forwardRef((props: SearchFormProps, ref) => {
  const { config, onSearch, collapse = false, collapsedRow = 2 } = props;
  const [form] = Form.useForm();
  const [expanded, setExpanded] = useState(false);

  useImperativeHandle(ref, () => ({
    form,
  }));

  const onFinish = (values: Record<string, any>) => {
    onSearch(values);
  };

  const getFields = () => {
    const count = expanded ? config.length : collapsedRow * 3 - 1;

    return config.map((item, index) => {
      return (
        <Col style={{ display: index >= count ? 'none' : '' }} span={8} key={item.name}>
          <FormItem inline data={item}>
            {getFieldItem(item)}
          </FormItem>
        </Col>
      );
    });
  };

  const getButtons = () => {
    return (
      <div className={style.searchBtns}>
        <Space>
          <Button type="primary" htmlType="submit">
            查询
          </Button>
          <Button onClick={() => form.resetFields()}>重置</Button>
          {collapse && (
            <a onClick={() => setExpanded(!expanded)}>
              <DownOutlined rotate={expanded ? 180 : 0} />
              {expanded ? '收起' : '展开'}
            </a>
          )}
        </Space>
      </div>
    );
  };

  const getButtonOffset = () => {
    if (!expanded && collapsedRow * 3 - 1 < config.length) return 8;
    return 8 * (3 - (config.length % 3));
  };

  return (
    <Form form={form} onFinish={onFinish}>
      <Row gutter={24}>
        {getFields()}
        {collapse && <Col span={getButtonOffset()}>{getButtons()}</Col>}
      </Row>
      {!collapse && (
        <Row gutter={24}>
          <Col span={24}>{getButtons()}</Col>
        </Row>
      )}
    </Form>
  );
});

SearchForm.displayName = 'SearchForm';

export default SearchForm;
