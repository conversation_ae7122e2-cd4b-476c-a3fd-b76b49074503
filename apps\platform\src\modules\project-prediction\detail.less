@import url('@/variables.less');

.detailBox {
  box-sizing: border-box;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 12px;
  background: #fff;

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;

    .title {
      font-size: 16px;
      font-weight: 700;
      line-height: 24px;
    }

    .collapse {
      color: @PrimaryColor;
      cursor: pointer;
    }
  }

  .body {
    overflow: hidden;
    transition: height 0.3s ease-in-out;
  }
}

.predictionDetail {
  padding: 20px;
}

.matchStatus {
  :global(.ant-descriptions-item-container) {
    display: flex;
    align-items: center;
  }
}

.stepWrapper {
  max-width: 980px;
}

.logWrapper {
  max-width: 730px;
  padding-top: 10px;
}

.detailInMessage {
  padding: 0;
}
