.main {
  display: flex;
  overflow: auto;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  flex-direction: column;
  padding: 24px;
  padding-bottom: 0;
  border-radius: 8px;
  background: #fff;

  .toolbar {
    display: flex;
    justify-content: space-between;
  }

  .content {
    padding: 16px 0 0;

    :global(.ant-table-thead) {
      height: 54px;
    }

    :global(.ant-table-row) {
      height: 54px;
    }

    :global(.ant-pagination) {
      position: sticky;
      bottom: 0;
      padding: 16px 16px 16px 0;
      margin: 0 !important;
      background-color: #fff;
    }
  }
}

.datatableName {
  margin-top: 8px;
}
