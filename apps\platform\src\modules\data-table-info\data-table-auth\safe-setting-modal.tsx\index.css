.safeSettingModal {
  padding: 0;
}
.safeSettingModal :global .ant-modal-header {
  display: flex;
  height: 64px;
  align-items: center;
  padding-left: 24px;
  border-bottom: 1px solid #e5e5e5;
  margin-bottom: 0 !important;
  color: rgba(0, 0, 0, 0.88);
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
}
.safeSettingModal :global .ant-modal-content {
  padding: 0;
}
.content {
  display: flex;
  width: 100%;
  height: 743px;
}
.content .left {
  width: 260px;
  height: 743px;
  box-sizing: border-box;
  flex-basis: 260px;
  padding: 24px;
  border-radius: 0 0 0 8px;
  background-color: #f5f5f5;
}
.content .right {
  height: 743px;
  flex: 1;
  padding: 24px 16px;
}
.content .right .desc {
  width: 636px;
  margin-bottom: 16px;
  color: rgba(0, 0, 0, 0.88);
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
}
.content .right .table :global .highlight-row {
  background-color: #faad14;
}
.content .right .table :global .highlight-col {
  background-color: #fff;
}
.content .right .table .safeConfigTable :global .ant-table-content .ant-table-thead > tr > th {
  background-color: #fafafa !important;
}
.content .right .code {
  position: relative;
  height: 456px;
  margin-top: 16px;
}
.content .right .code .codeTitle {
  position: absolute;
  top: 12px;
  right: 16px;
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  font-weight: 400;
  gap: 24px;
  line-height: 16px;
}
.content .right .code .codeTitle .copy {
  color: rgba(0, 0, 0, 0.65);
}
.content .right .code .codeTitle :global .ant-typography {
  margin-bottom: 0;
}
.content .right .code .codeTitle :global .ant-typography svg {
  margin-right: 8px;
  color: rgba(0, 0, 0, 0.65);
}
.content .right .code .codeContent {
  overflow: auto;
  width: 100%;
  height: 100%;
  background-color: #f5f5f5 !important;
}
.checkBox {
  display: flex;
  width: 212px;
  height: 74px;
  box-sizing: border-box;
  align-items: center;
  justify-content: space-around;
  padding: 16px 12px;
  border: 1px solid #e8e9ea;
  border-radius: 4px;
  margin-bottom: 16px;
  background-color: #fff;
  cursor: pointer;
}
.checkBox:hover {
  border: 1px solid #9a0000;
  transition: all 0.3s;
}
.checkBox .box {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.checkBox .box div {
  margin-bottom: 4px;
  color: rgba(0, 0, 0, 0.55);
  font-size: 12px;
  font-weight: 400;
  line-height: 18px;
  text-align: center;
}
.checkBox .box span {
  color: rgba(0, 0, 0, 0.85);
  font-size: 12px;
  font-weight: 500;
  line-height: 20px;
  text-align: center;
}
.checkReady {
  position: relative;
  border: 1px solid #9a0000;
}
.checkReady::after {
  position: absolute;
  top: 0;
  right: -4px;
  display: block;
  width: 0;
  height: 0;
  border-top: 10px solid #9a0000;
  border-right: 10px solid transparent;
  border-left: 10px solid transparent;
  content: '';
  transform: rotate(227deg);
}
