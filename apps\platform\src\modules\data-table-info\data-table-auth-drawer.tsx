import { CloseOutlined } from '@ant-design/icons';
import { Drawer, Select } from 'antd';
import React, { useEffect, useState } from 'react';

// import { DataTableAuthComponent } from './component/data-table-auth/data-table-auth.view';
import { getModel } from '@/util/valtio-helper';

import { DataManagerService } from '../data-manager/data-manager.service';

import { DataTableAuth as DataTableAuthComponent } from './data-table-auth/data-tabel-auth.view';
import { DataVisibleAuthComponent } from './data-visible-auth';
import style from './index.less';

export enum DataAuthDrawerArea {
  Auth = 'Auth',
}

export enum AuthType {
  PROJECT = 'project',
  PSI = 'psi',
  PREDICTION = 'prediction',
}

interface IProps<T> {
  visible: boolean;
  close: () => void;
  data: T;
}

export interface AuthComponentProps {
  type: AuthType;
  data: API2.DatatableVO;
  nodeVoters: {
    nodeId: string;
    nodeName: string;
    instId: string;
    instName: string;
  }[];
}

const authTypeOptions = [
  { label: '联合建模', value: 'project' },
  { label: '联合预测', value: 'prediction' },
  { label: '隐私求交', value: 'psi' },
];

const ComponentSwitcher = (props: AuthComponentProps) => {
  const { type, data, nodeVoters } = props;
  return (
    <div>
      <div style={{ display: type === AuthType.PROJECT ? 'block' : 'none' }}>
        <DataTableAuthComponent tableInfo={data} size="middle" />
      </div>
      <div style={{ display: type !== AuthType.PROJECT ? 'block' : 'none' }}>
        <DataVisibleAuthComponent type={type} data={data} nodeVoters={nodeVoters} />
      </div>
    </div>
  );
};

export const DataTableAuth: React.FC<IProps<API.DatatableVO>> = ({
  visible,
  close,
  data,
}) => {
  const [authType, setAuthType] = useState<AuthType>(AuthType.PROJECT);
  const [nodeVoters, setNodeVoters] = useState<
    { nodeId: string; nodeName: string; instId: string; instName: string }[]
  >([]);
  const dataManagerService = getModel(DataManagerService);

  useEffect(() => {
    if (data.nodeId)
      dataManagerService.getNodeVoters(data.nodeId).then((res) => {
        setNodeVoters(res);
      });
  }, []);

  return (
    <Drawer
      title={<div>「{data.datatableName}」授权管理</div>}
      extra={<CloseOutlined style={{ fontSize: 12 }} onClick={close} />}
      width={700}
      open={visible}
      onClose={close}
      closable={false}
    >
      <div className={style.authTypeSelect}>
        <span className={style.authText}>请选择工程授权类型: </span>
        <Select
          style={{ width: 300 }}
          value={authType}
          options={authTypeOptions}
          onChange={(val) => setAuthType(val)}
        />
      </div>
      <ComponentSwitcher type={authType} data={data} nodeVoters={nodeVoters} />
      {/* <DataTableAuthComponent tableInfo={data} size="middle" /> */}
      {/* <DataTableAuthComponent tableInfo={data} size="middle" /> */}
    </Drawer>
  );
};
