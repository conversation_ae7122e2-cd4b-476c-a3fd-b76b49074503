.details {
  :global {
    .ant-descriptions .ant-descriptions-row > td {
      padding-bottom: 12px;
    }
  }
}

.dagBoxContent {
  height: 240px;
}

.dagBox {
  position: relative;
  height: 100%;
  border: 1px solid rgb(0 0 0 / 6%);
  border-radius: 6px;
  margin-top: -13px;
  background-color: #f9f9f9;

  &:hover {
    .zoomIn {
      display: flex;
    }

    .aim {
      display: flex;
    }
  }

  .zoomIn {
    position: absolute;
    top: 12px;
    right: 72px;
    display: none;
    width: 56px;
    height: 24px;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    background-color: rgb(0 0 0 / 45%);
    color: white;
    cursor: pointer;

    .zoomInIcon {
      padding-right: 4px;
    }
  }

  .aim {
    position: absolute;
    top: 12px;
    right: 12px;
    display: none;
    width: 56px;
    height: 24px;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    background-color: rgb(0 0 0 / 45%);
    color: white;
    cursor: pointer;

    .aimIcon {
      padding-right: 4px;
    }
  }
}

.tableTitle {
  padding: 16px 0 8px;
  color: rgb(0 0 0 / 88%);
  font-size: 14px;
  font-weight: 500;
}

.graphContainer {
  overflow: hidden;
  width: 100%;
  height: 100%;
}

.graph {
  width: 100%;
  height: 100%;
  cursor: zoom-in;
}

.previewModal {
  cursor: zoom-out;

  :global(.ant-modal) {
    width: 800px !important;
    cursor: grab;
  }

  .graph {
    width: 100%;
    height: 550px;
  }
}

.tableWrapper {
  :global {
    .ant-table-cell {
      padding: 6px 8px !important;
    }
  }
}

.actions {
  display: flex;
  align-items: center;
  justify-content: end;
}

.tabsTable {
  position: relative;

  :global {
    .ant-tabs-nav::before {
      border-bottom: none !important;
    }

    .ant-table-container {
      border-radius: 0;
    }
  }

  :global(.ant-tabs-tab) {
    padding: 0 0 8px !important;
  }

  :global(.ant-tabs-nav) {
    margin-right: 135px;
    margin-bottom: 8px !important;
  }
}

.tableTitleHeader {
  display: flex;
  height: 22px;
  align-items: center;
  justify-content: space-between;
  margin: 16px 0 8px;
}

.fullScreenContentPage {
  overflow: auto;
  padding-top: 0;
  padding-bottom: 24px;
  background-color: #fff;

  .tableTitleContent {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    border: 1px solid #eee;
    margin-bottom: 24px;
  }

  .title {
    color: #1d2129;
    font-size: 20px;
    font-weight: 500;
    letter-spacing: 0;
  }

  .fullScreenContentWrap {
    padding: 0 24px;
  }
}

.resultDrawerTitle {
  display: flex;
  align-items: center;
  justify-content: space-between;

  :global(.ant-progress) {
    margin-bottom: 0 !important;
  }

  :global(.ant-badge-status-text) {
    color: rgb(0 0 0 / 85%);
    font-size: 14px;
    font-weight: 400;
  }
}
