.fieldsComponent {
  .nums {
    display: inline-block;
    width: calc(100% - 35px);
    height: 30px;
    // border: 1px solid #d9d9d9;
    border-right-width: 0;
    background-color: #f6f6f6;
    color: rgb(0 0 0 / 65%);
    cursor: pointer;
    line-height: 30px;
    text-align: center;
  }

  .numsDisabled {
    display: inline-block;
    width: calc(100% - 35px);
    height: 30px;
    border: 1px solid #d9d9d9;
    border-right-width: 0;
    background: #f5f5f5;
    cursor: not-allowed;
    line-height: 30px;
    text-align: center;
  }

  :global(.anticon-menu) {
    color: rgb(0 0 0 / 45%);
  }

  button {
    width: 30px;
    height: 30px;
    border: none;
    border-radius: 0;
    background-color: #f6f6f6;
    line-height: 26px;
  }

  &.small {
    .nums {
      width: calc(100% - 26px);
      height: 26px;
      font-size: 12px;
      line-height: 26px;
      vertical-align: middle;
    }

    .numsDisabled {
      width: calc(100% - 26px);
      height: 26px;
      font-size: 12px;
      line-height: 26px;
    }

    button {
      width: 26px;
      height: 26px;
      line-height: 26px;
      vertical-align: middle;
    }
  }
}

.selectModal {
  .tableSelector {
    .label {
      color: var(--heading-color);
    }

    .selector {
      width: 480px;
    }

    :global {
      .anticon-question-circle {
        margin-left: 4px;
      }
    }
  }
}

.fieldBlock {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;

  .fieldSelectTable {
    width: calc(~'50%');
    height: 392px;
    border: 1px solid rgb(0 0 0 / 15%);
    border-radius: 0 2px 2px 0;
    border-left: none;

    .fieldSelectHeader {
      display: flex;
      height: 40px;
      align-items: center;
      justify-content: space-between;
      padding-right: 16px;
      padding-left: 16px;
      border-bottom: 1px solid rgb(0 0 0 / 15%);
    }

    .fieldSelectContent {
      height: 352px;
      padding-left: 23px;
      overflow-y: auto;

      .fieldAllSelect {
        margin: 14px 0;
      }

      .fieldCol {
        margin-bottom: 14px;
      }
    }

    .fieldInputContent {
      width: 100%;

      :global(.ant-input) {
        height: 338px;
        border: none;
        margin-top: 12px;
      }

      :global(.ant-input:focus) {
        box-shadow: none !important;
      }

      :global(.ant-input-focused) {
        box-shadow: none !important;
      }

      :global(.ant-input[disabled]) {
        background-color: #fff;
        color: rgb(0 0 0 / 85%);
      }
    }
  }
}

.selectedField {
  width: calc(~'50%');
  height: 392px;
  border: 1px solid rgb(0 0 0 / 15%);
  border-radius: 2px 0 0 2px;

  .header {
    height: 40px;
    padding-left: 16px;
    border-bottom: 1px solid rgb(0 0 0 / 15%);
    margin-top: 0;
    margin-bottom: 0;
    line-height: 40px;

    .radio {
      float: left;
    }
  }

  .fieldTags {
    height: 432px;
    padding: 8px;
    padding-bottom: 1px;
    border: 1px solid var(--border-color-split);
    overflow-y: auto;

    .tag {
      margin-bottom: 8px;
    }
  }

  .fieldText {
    height: 340px;
    padding: 11px 31px 0 16px;
    overflow-y: auto;
  }
}

.componentTag {
  margin-left: 8px;
}

.treeWrap {
  padding-top: 16px;

  :global {
    .ant-tree-checkbox-indeterminate .ant-tree-checkbox-inner::after {
      height: 2px;
    }

    .ant-tree-switcher {
      margin-left: 20px;
    }

    .ant-tree-checkbox {
      margin-left: -42px;
    }

    .ant-tree .ant-tree-node-content-wrapper {
      margin-left: 12px;
    }

    .ant-tree-checkbox-inner {
      border-radius: 4px;
    }
  }
}
