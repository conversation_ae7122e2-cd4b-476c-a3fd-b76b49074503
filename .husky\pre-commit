#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# 1) Validate committer email

EMAIL=$(git config user.email)

if [[ ! $EMAIL =~ ^.*(@alibaba-inc.com)$ ]];
then
    :;
else
    echo "You are using a company email address.";
    echo "Please use your personal or GitHub email address instead.";
    echo "";
    echo "To configure your email for this repository, run:";
    echo "";
    echo "   git config user.email '<EMAIL>'";
    echo "";
    exit 1;
fi;

# 2) Lint staged files

pnpm exec lint-staged
