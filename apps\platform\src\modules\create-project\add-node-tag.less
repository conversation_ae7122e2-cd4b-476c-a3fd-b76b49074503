.addNodeTagContent {
  min-height: 30px;
  padding: 20px;
  border-radius: 8px;
  background-color: #f7f8fa;
}

.addNode {
  width: 80px;
  margin-top: 12px;
  color: #9a0000;
  cursor: pointer;
}

.addNodePosition {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 0;
}

.popverNodeContent {
  :global(.ant-popover-inner) {
    padding: 20px 24px;
  }

  :global(.ant-popover-title) {
    margin-bottom: 12px;
    color: rgb(0 0 0 / 88%);
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
  }
}

.popverContent {
  overflow: auto;
  width: 440px;
  max-height: 446px;

  .checkBoxGroup {
    display: block;
    margin-top: 12px;
  }

  .checkItem {
    margin: 12px 0;
  }
}

.embeddedTag {
  border: 1px solid #cdfadf;
  border-radius: 4px;
  background-color: #ecfff4;
  color: rgb(0 0 0 / 88%);
  font-weight: 400;
  scale: 0.85;
}
