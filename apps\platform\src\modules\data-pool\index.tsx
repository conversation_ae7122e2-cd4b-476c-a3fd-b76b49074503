import { SearchOutlined, InfoCircleOutlined, ReloadOutlined } from '@ant-design/icons';
import type { RadioChangeEvent } from 'antd';
import {
  Button,
  Radio,
  Input,
  Space,
  Badge,
  Tooltip,
  Popover,
  Empty,
  message,
  Typography,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import type { ChangeEvent } from 'react';
import { useEffect } from 'react';

import CommonTable from '@/components/common-table';
import { hasAccess, Platform } from '@/components/platform-wrapper';
// import { getColumnSearchProps } from '@/components/table-column-search';
import { StatusTag } from '@/components/status-tag';
import { TabSearchBox } from '@/components/tab-search-box';
import { getDatatable } from '@/services/secretpad/DatatableController';
import { getModel, Model, useModel } from '@/util/valtio-helper';

import { DataSourceType } from '../data-manager/data-manager.service';
import { DataTableInfoDrawer } from '../data-table-info/data-table-info.view';

import { DataPoolService } from './data-pool.service';
import styles from './index.less';

const DataPoolComponent = () => {
  const model = useModel(DataPoolModel);
  const isAutonomyMode = hasAccess({ type: [Platform.AUTONOMY] });

  const columns: ColumnsType = [
    {
      title: '数据表名',
      dataIndex: 'datatableName',
      key: 'datatableName',
      ellipsis: true,
      render: (text: string, tableInfo: API.DatatableVO) => (
        <Tooltip title={text}>
          <Typography.Link onClick={() => model.openDataInfo(tableInfo)}>
            {text}
          </Typography.Link>
        </Tooltip>
      ),
    },
    {
      title: '数据源类型',
      dataIndex: 'datasourceType',
      key: 'datasourceType',
      filters: [
        { text: 'OSS', value: DataSourceType.OSS },
        { text: 'HTTP', value: DataSourceType.HTTP },
        { text: 'LOCAL', value: DataSourceType.LOCAL },
        { text: 'ODPS', value: DataSourceType.ODPS },
        { text: 'MYSQL', value: DataSourceType.MYSQL },
      ],
    },
    {
      title: '所属机构',
      dataIndex: 'nodeName',
      key: 'nodeName',
      render: (text: string, record: API2.DatatableVO) => {
        return text || record.nodeId;
      },
      // ...getColumnSearchProps('nodeName', '请输入机构名称搜索'),
    },
    {
      // TODO: 这里联调的时候要聚合数据，包括authprojects和任务的授权
      title: '权限概览',
      key: 'psiDatatableVO',
      width: 250,
      ellipsis: {
        showTitle: false,
      },
      render: (_, tableInfo: API2.DatatableVO) => {
        const psiProjects = tableInfo.psiDatatableVO?.grantNodeIds || [];
        const fiProjects = tableInfo.fiDatatableVO?.grantNodeIds || [];
        const auths: { name: string; type: string }[] = [];

        psiProjects.forEach((i) => {
          auths.push({ name: `隐私求交-${i}`, type: 'psi' });
        });
        fiProjects.forEach((i) => {
          auths.push({ name: `联合预测-${i}`, type: 'prediction' });
        });

        return (
          <Popover
            placement="topLeft"
            title="已授权"
            content={
              <div className={styles.authProjectListPopover}>
                {(() => {
                  const authItems = auths.map((i) => (
                    <div key={i.name} className={styles.authProjectListPopoverItem}>
                      {i.name}
                    </div>
                  ));
                  return auths.length > 0 ? (
                    authItems
                  ) : (
                    <Empty description={false} image={Empty.PRESENTED_IMAGE_SIMPLE} />
                  );
                })()}
              </div>
            }
            trigger="hover"
          >
            {auths.map((i) => i.name).join('; ') || '-'}
          </Popover>
        );
      },
    },
    {
      title: (
        <Space>
          <div className={styles.uploadText}>状态</div>
          <Tooltip title="数据表状态可能展示不准确,请点击刷新后查看">
            <InfoCircleOutlined className={styles.uploadIcon} />
          </Tooltip>
        </Space>
      ),
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        return (
          <>
            {status === 'Available' ? (
              <StatusTag type="success" text="可用" />
            ) : (
              <StatusTag type="failed" text="不可用" />
            )}
          </>
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: API2.GrantedDatatableDetailVO) => {
        return (
          <Typography.Link
            onClick={() => {
              model.refreshTableStatus(record, isAutonomyMode);
            }}
          >
            刷新
          </Typography.Link>
        );
      },
    },
  ];

  useEffect(() => {
    model.getCurrentNode().then(() => {
      model.getTableList();
    });
  }, []);

  const tabs = [
    {
      value: 'all',
      label: '全部',
    },
    {
      value: 'Available',
      label: '可用',
    },
    {
      value: 'UnAvailable',
      label: '不可用',
    },
  ];

  return (
    <div className={styles.dataPoolWrapper}>
      <TabSearchBox tabs={tabs} onTabChange={(value) => model.dataFilter(value)}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span style={{ marginRight: 8 }}>数据表</span>
          <Input
            style={{ width: 220 }}
            placeholder="搜索表名"
            onChange={(e) => model.searchTable(e)}
            suffix={
              <SearchOutlined
                style={{
                  color: '#aaa',
                }}
              />
            }
          />
        </div>
      </TabSearchBox>
      {/* <div className={styles.toolbar}>
        <div style={{ marginRight: 12, width: 220 }}>
          <Input
            placeholder="搜索表名"
            onChange={(e) => model.searchTable(e)}
            suffix={
              <SearchOutlined
                style={{
                  color: '#aaa',
                }}
              />
            }
          />
        </div>
        <div style={{ flex: 1 }}>
          <Radio.Group defaultValue="all" onChange={(e) => model.dataFilter(e)}>
            <Radio.Button value="all">全部</Radio.Button>
            <Radio.Button value="Available">可用</Radio.Button>
            <Radio.Button value="UnAvailable">不可用</Radio.Button>
          </Radio.Group>
        </div>
      </div> */}
      <div className={styles.content}>
        <CommonTable
          columns={columns}
          dataSource={model.tablesList}
          rowKey={(record) => record.datatableId}
          loading={model.tableLoading}
          pagination={{
            total: model.totalNum,
            current: model.pageNumber,
            pageSize: model.pageSize,
            showSizeChanger: true,
            showTotal: (total) => `共${total}条`,
            onChange: (page, pageSize) => {
              model.pageNumber = page;
              model.pageSize = pageSize;
            },
          }}
          onChange={(pagination, filters) => {
            model.typeFilters = (filters?.datasourceType as string[]) || [];
            model.nodeNamesFilter = (filters?.nodeName as string[]) || [];
            model.getTableList();
          }}
        ></CommonTable>
      </div>
      {model.showDataDetail && (
        <DataTableInfoDrawer
          visible={model.showDataDetail}
          close={() => (model.showDataDetail = false)}
          data={{
            tableInfo: model.tableInfo,
          }}
          origin="outer"
        />
      )}
    </div>
  );
};

export class DataPoolModel extends Model {
  service = getModel(DataPoolService);
  tablesList: API2.GrantedDatatableDetailVO[] = [];

  tableInfo: API.DatatableVO & { currentNodeId: string } = {
    currentNodeId: '',
  };
  nodeInfo: {
    nodeId: string;
    nodeName: string;
  }[] = [];

  showDataDetail = false;
  tableLoading = false;

  pageNumber = 1;
  pageSize = 10;
  totalNum = 0;
  statusFilter = '';
  search = '';
  typeFilters: string[] = [];
  nodeNamesFilter: string[] = [];
  searchDebounce: number | undefined = undefined;

  async getCurrentNode() {
    this.nodeInfo = await this.service.getCurrentNodeInfo();
  }

  async getTableList() {
    this.tableLoading = true;
    // const { ownerId } = parse(window.location.search);
    if (!this.nodeInfo || this.nodeInfo.length === 0) {
      this.tablesList = [];
      this.tableLoading = false;
      return;
    }

    const listData = await this.service.getDataList({
      nodeId: this.nodeInfo[0].nodeId,
      pageNum: this.pageNumber,
      pageSize: this.pageSize,
      status: this.statusFilter,
      search: this.search,
      typeFilters: this.typeFilters as string[],
    });
    this.totalNum = listData?.totalNum || 0;

    this.tableLoading = false;
    this.tablesList = listData?.grantedDatatables || [];
  }

  openDataInfo(table: API2.GrantedDatatableDetailVO) {
    this.tableInfo = {
      ...table,
      currentNodeId: this.nodeInfo[0].nodeId,
    };
    this.showDataDetail = true;
    console.log(this.tableInfo);
  }

  dataFilter(value: string) {
    if (value === 'all') {
      this.statusFilter = '';
    } else {
      this.statusFilter = value;
    }
    this.pageNumber = 1;
    this.getTableList();
  }

  searchTable(e: ChangeEvent<HTMLInputElement>) {
    this.search = e.target.value;
    clearTimeout(this.searchDebounce);
    this.searchDebounce = setTimeout(() => {
      this.pageNumber = 1;
      this.getTableList();
    }, 300) as unknown as number;
  }

  refreshTableStatus = async (
    record: API2.GrantedDatatableDetailVO,
    isAutonomyMode: boolean,
  ) => {
    try {
      const { status, data } = await getDatatable({
        datatableId: record.datatableId,
        nodeId: this.nodeInfo[0].nodeId,
        type: record.type,
        datasourceType: 'LOCAL',
      });
      if (status?.code === 0) {
        message.success('数据状态刷新成功');
        // TODO: 这里服务端列表状态和这个状态暂时做不到同步，需要手动修改列表状态
        // this.getTableList();
        const newStatus = data?.datatableVO?.status;
        const newList = this.tablesList.map((item) => {
          const changeStatus = isAutonomyMode
            ? item.datatableId === record.datatableId && item.nodeId === record.nodeId
            : item.datatableId === record.datatableId;
          if (changeStatus) {
            return {
              ...item,
              status: newStatus,
            };
          }
          return item;
        });
        this.tablesList = newList;
      } else {
        message.error('数据状态刷新失败');
      }
    } catch (error) {
      message.error((error as Error).message);
    }
  };
}

export default DataPoolComponent;
