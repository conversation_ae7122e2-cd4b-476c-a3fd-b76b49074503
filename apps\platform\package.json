{"private": true, "name": "secretpad", "scripts": {"dev": "umi dev", "build": "umi build", "postinstall": "umi setup", "setup": "umi setup", "start": "npm run dev", "lint:js": "eslint 'src/**/*.{js,jsx,ts,tsx}'", "lint:css": "stylelint --allow-empty-input 'src/**/*.{css,less}'", "lint:format": "prettier --check *.json 'src/[^.umi]**/*.{js,jsx,ts,tsx,css,less,md,json}'", "lint:typing": "tsc --noEmit", "test": "jest --coverage", "preview": "umi preview", "openapi": "node config/openapi.config.js", "oneapi": "bigfish api generate service"}, "dependencies": {"@ant-design/icons": "^5.0.1", "@antv/g2": "^4.2.9", "@antv/layout": "^0.3.23", "@antv/s2": "^1.52.0", "@antv/s2-react": "^1.44.2", "@antv/x6": "^2.11.1", "@antv/x6-plugin-keyboard": "2.2.1", "@antv/x6-plugin-selection": "^2.1.7", "@antv/x6-react-shape": "^2.2.2", "@secretflow/dag": "workspace:^", "@secretflow/utils": "workspace:^", "ahooks": "^3.7.8", "classnames": "^2.3.2", "crypto-js": "^4.1.1", "dayjs": "^1.11.8", "echarts": "^5.6.0", "lodash": "^4.17.21", "monaco-editor": "^0.41.0", "papaparse": "^5.4.1", "pb-bi-render": "^0.0.4", "query-string": "^6.14.1", "react-csv": "^2.2.2", "react-syntax-highlighter": "^15.5.0", "sql-formatter": "^15.4.2", "umi": "^4.0.64", "uuid": "^10.0.0", "valtio": "^1.10.7"}, "devDependencies": {"@types/lodash": "^4.14.191", "@types/papaparse": "^5.3.7", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@umijs/openapi": "^1.8.5", "antd": "^5.0.0", "babel-plugin-parameter-decorator": "^1.0.16", "babel-plugin-transform-typescript-metadata": "^0.3.2", "dotenv": "^16.3.1"}, "peerDependencies": {"antd": "^5.0.0", "react": "^18.0.0"}}