.home {
  display: flex;
  overflow: hidden;
  width: 100%;
  height: 100%;
  flex-direction: column;
  background-size: cover;
}
.homeBg {
  background-image: url('../../../assets/bg2.png');
}
.centerBg {
  background-image: url('../../../assets/bg2.png');
}
.header-link {
  text-decoration: none;
}
.header-items {
  display: flex;
  width: 100%;
  height: 56px;
  align-items: center;
  color: #fefcfc;
}
.header-items .left {
  display: flex;
  flex: 1;
  align-items: center;
  padding-left: 16px;
}
.header-items .left svg {
  padding-right: 16px;
  border-right: none;
}
.header-items .left .logo {
  cursor: pointer;
  height: 44px;
}
.header-items .left .subTitle {
  margin-left: 16px;
  color: #ddbbb8;
  font-size: 20px;
  font-weight: 500;
}
.header-items .left .line {
  width: 1px;
  height: 12px;
  margin: 14px;
  margin: 0 24px;
  background-color: rgba(0, 10, 26, 0.16);
}
.header-items .left .myNodeTitle {
  display: flex;
  width: 188px;
  height: 32px;
  align-items: center;
  border-radius: 4px;
  background-color: #741109 !important;
  color: #a96c67;
  cursor: pointer;
  font-size: 14px;
  font-weight: 400;
}
.header-items .left .myNodeTitle svg {
  padding-right: 4px;
  padding-left: 14px;
  border: none !important;
}
.header-items .left .myNodeTitle .nodeName {
  display: inline-block;
  overflow: hidden;
  max-width: 115px;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.header-items .p2pLeft svg {
  padding-right: 16px;
  border: none;
}
.header-items .right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 16px;
  color: #fefcfc;
}
.header-items .right .community {
  display: flex;
  align-items: center;
  padding: 0 12px;
  cursor: pointer;
  font-size: 14px;
}
.header-items .right .community svg {
  margin-right: 4px;
  font-size: 16px;
}
.header-items .right .line {
  width: 1px;
  height: 12px;
  background-color: rgba(0, 10, 26, 0.16);
}
.header-items .right .loginline {
  width: 1px;
  height: 12px;
  margin: 14px;
  margin-right: 24px;
  margin-left: 12px;
  background-color: rgba(0, 10, 26, 0.16);
}
.header-items .right .contentHeaderRight {
  padding: 0 12px;
}
.header-items .right .help {
  display: flex;
  align-items: center;
  padding: 0 12px;
  cursor: pointer;
  font-size: 14px;
}
.header-items .right .help svg {
  margin-right: 4px;
  font-size: 16px;
}
.header {
  height: 56px;
  background: #9a0000;
}
.content {
  width: 100%;
  height: 500px;
  box-sizing: border-box;
  flex: 1;
  padding: 0 4px 12px;
  overflow-y: auto;
}
.messageBadge {
  color: #fefcfc !important;
}
.messageBadge :global(.ant-badge) {
  box-shadow: none !important;
}
.headerDropdown {
  min-width: 300px;
  min-height: 100px;
}
