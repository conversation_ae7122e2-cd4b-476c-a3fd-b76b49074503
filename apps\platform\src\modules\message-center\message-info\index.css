.messageInfDrawer {
  background-color: #fff;
}
.descNodeStatusList :global(.ant-descriptions-item-content) {
  display: block !important;
}
.descNodeStatusList .configToggle {
  color: #9a0000;
  cursor: pointer;
}
.dagBoxContent {
  width: 100%;
  height: 240px;
}
.sheetText {
  width: 42px;
  height: 22px;
  margin: 8px 0;
  color: rgba(0, 0, 0, 0.88);
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
}
.sheetTag {
  border-radius: 10px;
  background-color: rgba(0, 104, 250, 0.08);
  color: rgba(0, 0, 0, 0.65);
}
