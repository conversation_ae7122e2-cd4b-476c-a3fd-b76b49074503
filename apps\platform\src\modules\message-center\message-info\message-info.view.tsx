import { CloseOutlined } from '@ant-design/icons';
import {
  <PERSON><PERSON>,
  <PERSON>er,
  Flex,
  Input,
  Modal,
  Popconfirm,
  Space,
  Spin,
  message,
} from 'antd';
import { parse } from 'query-string';
import React from 'react';

import { AutoModal } from '@/components/auto-modal';
import { EllipsisText } from '@/components/text-ellipsis.tsx';
import { useModel } from '@/util/valtio-helper';

import {
  ListItemTitleMap,
  MessageItemType,
  MessageStateTagWrap,
} from '../component/common';
import { MessageActiveTabType, MessageService, StatusEnum } from '../message.service';

import styles from './index.less';
import {
  CommonJobCreateInfo,
  NodeAuthInfo,
  ProjectInviteInfo,
  TeeDownloadInfo,
} from './info-content';

export const InfoContentRender = (props: {
  item: any;
  activeTab: string;
  status: string;
}) => {
  const { item, activeTab, status } = props;
  if (!item.type) return <>{'--'}</>;
  const ContentMap = React.useMemo(
    () => ({
      [MessageItemType.NODE_ROUTE]: (
        <NodeAuthInfo info={item} activeTab={activeTab} status={status} />
      ),
      [MessageItemType.TEE_DOWNLOAD]: (
        <TeeDownloadInfo info={item} activeTab={activeTab} status={status} />
      ),
      [MessageItemType.PROJECT_ARCHIVE]: (
        <ProjectInviteInfo info={item} activeTab={activeTab} status={status} />
      ),
      [MessageItemType.PROJECT_NODE_ADD]: (
        <ProjectInviteInfo info={item} activeTab={activeTab} status={status} />
      ),
      [MessageItemType.COMMON_JOB_CREATE]: (
        <CommonJobCreateInfo info={item} activeTab={activeTab} status={status} />
      ),
    }),
    [item],
  );
  return ContentMap[item.type as MessageItemType];
};

export const MessageInfoModal = ({
  open,
  onClose,
  data,
  activeTab,
  onOk,
}: {
  open: boolean;
  onClose: () => void;
  data: API.MessageVO & { isJobExists?: boolean };
  activeTab: string;
  onOk?: () => void;
}) => {
  const service = useModel(MessageService);
  const { messageDetail, messageInfoLoading } = service;
  const [comment, setComment] = React.useState('');
  const { ownerId } = parse(window.location.search);

  const getMessageInfo = () => {
    console.log(data, 'message data');
    if (!data.voteID || !ownerId) return;
    service.getMessageDetail(
      {
        ownerId: ownerId as string,
        voteId: data.voteID,
        isInitiator: activeTab === MessageActiveTabType.APPLY ? true : false,
        voteType: data.type,
      },
      data.isJobExists,
    );
  };

  React.useEffect(() => {
    if (open) {
      getMessageInfo();
    }
  }, [data.voteID, open]);

  const processMessage = async (action: StatusEnum) => {
    const { status } = await service.process({
      action,
      reason: comment,
      voteId: data.voteID,
      voteParticipantId: ownerId as string,
    });
    if (status && status.code !== 0) {
      message.error(status.msg);
    } else {
      message.success('处理成功');
      onOk && onOk();
      onClose && onClose();
      // getMessageInfo();
    }
  };

  const itemObj = ListItemTitleMap[data?.type as MessageItemType];

  return (
    <AutoModal
      title={
        <div style={{ width: 500 }}>
          <Space>
            <EllipsisText style={{ width: '100%' }}>
              {data.initiatingTypeMessage?.initiatorNodeName
                ? `来自${data.initiatingTypeMessage.initiatorNodeName}机构的${
                    data.messageName
                  }${itemObj?.suffix || ''}`
                : `${data.messageName}${itemObj?.suffix || ''}`}
            </EllipsisText>
            {/* <MessageStateTagWrap
              label={
                activeTab === MessageActiveTabType.PROCESS ? '本方状态' : '当前状态'
              }
              status={(data?.status as StatusEnum) || StatusEnum.PROCESS}
            /> */}
          </Space>
        </div>
      }
      onCancel={onClose}
      open={open}
      getContainer={false}
      width={650}
      className={styles.messageInfDrawer}
      footer={
        activeTab === MessageActiveTabType.PROCESS &&
        messageDetail.status === StatusEnum.PROCESS ? (
          <Flex justify="end">
            <Space>
              <Popconfirm
                title="你确定要拒绝吗？"
                placement="top"
                description={
                  <Input.TextArea
                    maxLength={50}
                    placeholder="请输50字符以内的理由"
                    allowClear
                    value={comment}
                    onChange={(e) => setComment(e.target.value)}
                  />
                }
                okText="拒绝"
                cancelText="取消"
                okButtonProps={{
                  danger: true,
                  ghost: true,
                }}
                onConfirm={async () => {
                  processMessage(StatusEnum.REJECT);
                  setComment('');
                }}
                onCancel={() => setComment('')}
              >
                <Button
                  loading={service.processLoading.rejectLoading}
                  disabled={service.processLoading.type === StatusEnum.AGREE}
                >
                  拒绝
                </Button>
              </Popconfirm>
              <Button
                type="primary"
                onClick={() => processMessage(StatusEnum.AGREE)}
                loading={service.processLoading.agreeLoading}
                disabled={service.processLoading.type === StatusEnum.REJECT}
              >
                同意
              </Button>
            </Space>
          </Flex>
        ) : null
      }
    >
      <Spin spinning={messageInfoLoading}>
        <InfoContentRender
          item={messageDetail}
          activeTab={activeTab}
          status={data.status || ''}
        />
      </Spin>
    </AutoModal>
  );
};
