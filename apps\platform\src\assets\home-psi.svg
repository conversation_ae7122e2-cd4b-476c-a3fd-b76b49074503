<svg width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M35 62C39.2614 62 43.1948 62.1883 46.3643 62.5H54V64.5C54 65.8807 45.4934 67 35 67C24.5066 67 16 65.8807 16 64.5V62.5H23.6357C26.8052 62.1883 30.7386 62 35 62Z" fill="#5AADEE"/>
<g filter="url(#filter0_i_1581_3645)">
<ellipse cx="35" cy="62.5" rx="19" ry="2.5" fill="url(#paint0_linear_1581_3645)"/>
</g>
<path d="M20.3938 1.81177C22.5301 3.161 27.3424 5.82578 29.5012 6.36548C32.1997 7.0401 34.8984 5.69059 36.4163 4.51001C36.4163 6.36521 40.2952 12.2685 44.8489 15.4729C48.4918 18.0364 51.9885 20.0268 53.2815 20.7014V47.5178C53.2251 49.8788 52.3709 52.274 50.0774 53.7581C47.2103 55.6132 41.8133 56.4559 36.5852 56.2874C32.6075 52.1841 25.6675 43.6958 22.384 35.6731C21.1594 32.8074 20.3938 29.9921 20.3938 27.4475C20.3938 17.8679 20.45 6.64654 20.3938 1.81177ZM20.3938 6.70239L15.3342 3.66724L20.3938 1.81177V6.70239Z" fill="url(#paint1_linear_1581_3645)"/>
<path d="M24.4416 8.3893C22.2828 7.8496 17.4705 5.0162 15.3342 3.66696C15.3904 8.50173 15.5029 20.5662 15.5029 30.1458C15.5029 39.7254 26.1844 53.139 31.5251 58.6484C36.7534 58.8171 42.1503 57.9738 45.0175 56.1186C47.3112 54.6344 48.1657 52.2395 48.2219 49.8784V23.3996C46.9289 22.725 43.4321 20.7348 39.7892 18.1713C35.2355 14.9668 31.5251 8.72661 31.5251 6.8714C31.0191 8.55795 27.1401 9.06392 24.4416 8.3893Z" fill="url(#paint2_linear_1581_3645)"/>
<mask id="path-5-inside-1_1581_3645" fill="white">
<path d="M58.9284 12.491C63.294 19.2134 55.5974 31.9595 41.7377 40.9602C27.8779 49.9608 13.1033 51.8077 8.73766 45.0852C4.37204 38.3627 12.0686 25.6166 25.9284 16.616C39.7881 7.61533 54.5627 5.7685 58.9284 12.491Z"/>
</mask>
<g clip-path="url(#paint3_angular_1581_3645_clip_path)" data-figma-skip-parse="true" mask="url(#path-5-inside-1_1581_3645)"><g transform="matrix(0.00763809 0.0115344 -0.0237001 0.0158713 33.833 28.7881)"><foreignObject x="-1203.19" y="-1203.19" width="2406.38" height="2406.38"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(208, 245, 255, 1) 0deg,rgba(123, 236, 251, 1) 97.3688deg,rgba(123, 236, 251, 0) 147.302deg,rgba(123, 236, 251, 0) 210.66deg,rgba(123, 236, 251, 1) 256.858deg,rgba(208, 245, 255, 1) 360deg);height:100%;width:100%;opacity:1"></div></foreignObject></g></g><path d="M58.9284 12.491L57.5139 13.4095C59.1466 15.9237 58.743 19.9994 55.7238 24.9995C52.7712 29.8892 47.5829 35.1533 40.8191 39.5457L41.7377 40.9602L42.6562 42.3746C49.7522 37.7664 55.342 32.1572 58.6113 26.743C61.8139 21.4393 63.0757 15.7807 60.3428 11.5724L58.9284 12.491ZM41.7377 40.9602L40.8191 39.5457C34.0553 43.9382 27.136 46.5368 21.4682 47.2453C15.6723 47.9698 11.7848 46.6808 10.1521 44.1666L8.73766 45.0852L7.32321 46.0037C10.0561 50.212 15.7387 51.3609 21.8865 50.5924C28.1623 49.8079 35.5602 46.9828 42.6562 42.3746L41.7377 40.9602ZM8.73766 45.0852L10.1521 44.1666C8.5194 41.6525 8.92301 37.5768 11.9422 32.5767C14.8948 27.687 20.0832 22.4229 26.8469 18.0304L25.9284 16.616L25.0098 15.2015C17.9138 19.8097 12.324 25.419 9.05473 30.8331C5.85215 36.1368 4.59031 41.7955 7.32321 46.0037L8.73766 45.0852ZM25.9284 16.616L26.8469 18.0304C33.6107 13.638 40.53 11.0393 46.1979 10.3308C51.9937 9.60635 55.8812 10.8954 57.5139 13.4095L58.9284 12.491L60.3428 11.5724C57.6099 7.36412 51.9273 6.21531 45.7795 6.98378C39.5037 7.76826 32.1058 10.5933 25.0098 15.2015L25.9284 16.616Z" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.48235294222831726,&#34;g&#34;:0.92575162649154663,&#34;b&#34;:0.98431372642517090,&#34;a&#34;:1.0},&#34;position&#34;:0.27046883106231689},{&#34;color&#34;:{&#34;r&#34;:0.48235294222831726,&#34;g&#34;:0.92549020051956177,&#34;b&#34;:0.98431372642517090,&#34;a&#34;:0.0},&#34;position&#34;:0.40917140245437622},{&#34;color&#34;:{&#34;r&#34;:0.48235294222831726,&#34;g&#34;:0.92549020051956177,&#34;b&#34;:0.98431372642517090,&#34;a&#34;:0.0},&#34;position&#34;:0.58516591787338257},{&#34;color&#34;:{&#34;r&#34;:0.48235294222831726,&#34;g&#34;:0.92549020051956177,&#34;b&#34;:0.98431372642517090,&#34;a&#34;:1.0},&#34;position&#34;:0.71349394321441650},{&#34;color&#34;:{&#34;r&#34;:0.81678187847137451,&#34;g&#34;:0.96088600158691406,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.48235294222831726,&#34;g&#34;:0.92575162649154663,&#34;b&#34;:0.98431372642517090,&#34;a&#34;:1.0},&#34;position&#34;:0.27046883106231689},{&#34;color&#34;:{&#34;r&#34;:0.48235294222831726,&#34;g&#34;:0.92549020051956177,&#34;b&#34;:0.98431372642517090,&#34;a&#34;:0.0},&#34;position&#34;:0.40917140245437622},{&#34;color&#34;:{&#34;r&#34;:0.48235294222831726,&#34;g&#34;:0.92549020051956177,&#34;b&#34;:0.98431372642517090,&#34;a&#34;:0.0},&#34;position&#34;:0.58516591787338257},{&#34;color&#34;:{&#34;r&#34;:0.48235294222831726,&#34;g&#34;:0.92549020051956177,&#34;b&#34;:0.98431372642517090,&#34;a&#34;:1.0},&#34;position&#34;:0.71349394321441650},{&#34;color&#34;:{&#34;r&#34;:0.81678187847137451,&#34;g&#34;:0.96088600158691406,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;transform&#34;:{&#34;m00&#34;:15.276184082031250,&#34;m01&#34;:-47.400257110595703,&#34;m02&#34;:49.895046234130859,&#34;m10&#34;:23.068708419799805,&#34;m11&#34;:31.742567062377930,&#34;m12&#34;:1.3824423551559448},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}" mask="url(#path-5-inside-1_1581_3645)"/>
<defs>
<filter id="filter0_i_1581_3645" x="16" y="60" width="38" height="5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1581_3645"/>
</filter>
<clipPath id="paint3_angular_1581_3645_clip_path"><path d="M58.9284 12.491L57.5139 13.4095C59.1466 15.9237 58.743 19.9994 55.7238 24.9995C52.7712 29.8892 47.5829 35.1533 40.8191 39.5457L41.7377 40.9602L42.6562 42.3746C49.7522 37.7664 55.342 32.1572 58.6113 26.743C61.8139 21.4393 63.0757 15.7807 60.3428 11.5724L58.9284 12.491ZM41.7377 40.9602L40.8191 39.5457C34.0553 43.9382 27.136 46.5368 21.4682 47.2453C15.6723 47.9698 11.7848 46.6808 10.1521 44.1666L8.73766 45.0852L7.32321 46.0037C10.0561 50.212 15.7387 51.3609 21.8865 50.5924C28.1623 49.8079 35.5602 46.9828 42.6562 42.3746L41.7377 40.9602ZM8.73766 45.0852L10.1521 44.1666C8.5194 41.6525 8.92301 37.5768 11.9422 32.5767C14.8948 27.687 20.0832 22.4229 26.8469 18.0304L25.9284 16.616L25.0098 15.2015C17.9138 19.8097 12.324 25.419 9.05473 30.8331C5.85215 36.1368 4.59031 41.7955 7.32321 46.0037L8.73766 45.0852ZM25.9284 16.616L26.8469 18.0304C33.6107 13.638 40.53 11.0393 46.1979 10.3308C51.9937 9.60635 55.8812 10.8954 57.5139 13.4095L58.9284 12.491L60.3428 11.5724C57.6099 7.36412 51.9273 6.21531 45.7795 6.98378C39.5037 7.76826 32.1058 10.5933 25.0098 15.2015L25.9284 16.616Z" mask="url(#path-5-inside-1_1581_3645)"/></clipPath><linearGradient id="paint0_linear_1581_3645" x1="35" y1="60" x2="35" y2="65" gradientUnits="userSpaceOnUse">
<stop stop-color="#5AADEE"/>
<stop offset="1" stop-color="#86D7FF"/>
</linearGradient>
<linearGradient id="paint1_linear_1581_3645" x1="44.8833" y1="19.0549" x2="53.0571" y2="56.2914" gradientUnits="userSpaceOnUse">
<stop offset="0.0511022" stop-color="#CBF4FF"/>
<stop offset="0.143449" stop-color="#80C6FD"/>
<stop offset="1" stop-color="#5AADEE"/>
</linearGradient>
<linearGradient id="paint2_linear_1581_3645" x1="31.8637" y1="3.21282" x2="31.8637" y2="58.6695" gradientUnits="userSpaceOnUse">
<stop stop-color="#AAEDFF"/>
<stop offset="1" stop-color="#74E1FF"/>
</linearGradient>
</defs>
</svg>
