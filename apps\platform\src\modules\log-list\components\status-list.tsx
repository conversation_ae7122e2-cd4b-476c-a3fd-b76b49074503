import { Badge, Flex, List, Space } from 'antd';

import { StatusTag } from '@/components/status-tag';
import { getGraphNodeLogs } from '@/services/secretpad/GraphController';

import type { LogListData } from '../constant';

import style from './index.less';

export const StatusIcon = (status?: string) => {
  switch (status?.toLocaleLowerCase()) {
    case 'succeed':
      return <Badge color="#68D092" />;
    case 'failed':
      return <Badge color="#f5222d" />;
    default:
      return <Badge color="#f5222d" />;
  }
};

const statusLabelMap = {
  SUCCEED: '成功',
  FAILED: '失败',
};

const statusTypeMap = {
  SUCCEED: 'success',
  FAILED: 'failed',
};

export const formatLog = (logString: string) => {
  return logString.split('\\n').join('\n');
};

const NodeLogListItem = (
  props: Partial<LogListData['aggregatedLogs'][0]> & {
    onviewDetail: (text: string) => void;
  },
) => {
  const { label, status, jobId, taskId, onviewDetail } = props;
  const graphNodeId = taskId?.split('-').slice(1).join('-');
  let flag = false,
    log = '';

  const setLogs = async (params: API.GraphNodeLogsRequest) => {
    const logs = await getGraphNodeLogs(params);
    const text = logs.data?.logs?.map((l) => formatLog(l)).join('\n');
    if (text) log = text;
    onviewDetail(log);
  };

  const getDetail = async () => {
    if (flag) {
      onviewDetail(log);
      return;
    }
    await setLogs({
      projectId: props.projectId,
      graphNodeId,
      graphId: graphNodeId?.split('-')[0],
    });
    if (log) flag = true;
  };

  return (
    <List.Item style={{ display: 'block' }}>
      <List.Item.Meta
        title={
          <Space>
            <div>{label}</div>
            <StatusTag
              type={statusTypeMap[status as keyof typeof statusTypeMap]}
              text={statusLabelMap[status as keyof typeof statusLabelMap]}
            />
          </Space>
        }
        description={
          <Flex justify="space-between">
            <Space>
              <span>训练流id: {jobId}</span>
              <span>任务id: {graphNodeId}</span>
            </Space>
            {status !== 'SUCCEED' && (
              <div className={style.btnDetail} onClick={getDetail}>
                <span className={style.text}>详情</span>
              </div>
            )}
          </Flex>
        }
      />
    </List.Item>
  );
};

export const NodeLogList = (props: {
  list: LogListData['aggregatedLogs'];
  onviewDetail: (text: string) => void;
}) => {
  const { list, onviewDetail } = props;

  return (
    <List
      itemLayout="horizontal"
      dataSource={list}
      renderItem={(item) => (
        <NodeLogListItem key={item.taskId} onviewDetail={onviewDetail} {...item} />
      )}
    ></List>
  );
};
