@import url('@/styles/mixins.less');
@import url('@/variables.less');

.cancelPipelinePopconfirm {
  width: 187.2px;
}

.pipelineItemContainer {
  display: flex;
  width: 100%;
  height: 32px;
  align-items: center;
  color: rgb(0 0 0 / 85%);
  font-size: 12px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 20px;

  .pipelineItemIcons {
    display: none;
  }

  &:hover .pipelineItemIcons {
    display: block;
  }

  .treeIcon {
    display: inline-block;
    width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    vertical-align: top;
  }

  .pipelineItemText {
    overflow: hidden;
    max-width: 200px;
    flex: 1;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .commandIcon :hover {
    color: @PrimaryColor;
  }
}

.pipelineCreation {
  margin-top: 8px;
  margin-right: 12px;
  // margin-top: 20px;

  // .pipelineCreationBtn {
  // width: 199px;
  // height: 32px;
  // border-color: #efefef;
  // background-color: white;
  // color: rgb(0 0 0 / 85%);
  // font-size: 12px;
  // }

  .pipelineCreationDisableBtn {
    color: rgb(0 0 0 / 25%);

    &:hover {
      color: rgb(0 0 0 / 25%);
    }
  }
}

.pipelineTree {
  :global {
    padding-top: 8px;
    background-color: transparent;

    .ant-tree-treenode {
      padding: 2px 7px;

      &.ant-tree-treenode-selected.ant-tree-node-content-wrapper {
        background-color: rgb(0 0 0 / 4%);

        &::before {
          background-color: transparent !important;
        }
      }

      .ant-tree-node-content-wrapper {
        padding: 0;
      }

      .ant-tree-node-selected.ant-tree-node-content-wrapper {
        background-color: rgb(0 0 0 / 4%);
      }

      .ant-tree-switcher.ant-tree-switcher-noop {
        width: 0;
      }
    }

    .ant-tree-node-content-wrapper {
      &:hover {
        background-color: rgb(0 0 0 / 4%);
      }
    }
  }
}

.templatePreview {
  width: 263.5px;
  height: 190px;
}

.menuIcon {
  padding-right: 4px;
}

.treeContainer {
  overflow: hidden auto;
  height: 100%;

  .scrollbar-style();
}

.templates {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  .item {
    position: relative;
    display: flex;
    height: 80px;
    padding: 16px;
    border: 1px solid rgb(0 10 26 / 16%);
    border-radius: 8px;
    margin-bottom: 16px;

    &.checked {
      border-color: @PrimaryColor;

      .icon {
        display: block;
        color: @PrimaryColor;
      }
    }

    .templateIcon {
      width: 48px;
      height: 48px;

      :global {
        .anticon svg {
          width: 48px;
          height: 48px;
        }
      }
    }

    .text {
      width: 160px;
      flex-basis: 160px;
      margin-left: 8px;

      .title {
        margin: 0;
        font-size: 14px;
      }

      .description {
        width: 100%;
        font-size: 12px;
      }
    }

    .icon {
      position: absolute;
      top: 8px;
      right: 8px;
      display: none;
      font-size: 21px;
    }
  }

  .showDisable {
    .title {
      color: rgb(0 0 0 / 25%);
    }

    .description {
      color: rgb(0 0 0 / 25%);
    }
  }
}

.buttonDisable {
  cursor: not-allowed !important;
  opacity: 0.4;
  pointer-events: none;
}
