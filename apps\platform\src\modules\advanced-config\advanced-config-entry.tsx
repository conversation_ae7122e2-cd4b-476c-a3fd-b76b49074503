import { SettingOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Toolt<PERSON> } from 'antd';

import { DefaultModalManager } from '@/modules/dag-modal-manager';
import { useModel } from '@/util/valtio-helper';

import {
  AdvancedConfig,
  AdvancedConfigDrawer,
} from './advanced-config-drawer/advanced-config-view';
import styles from './index.less';

export const AdvancedConfigComponent = ({ disabled }: { disabled?: boolean }) => {
  const modalManager = useModel(DefaultModalManager);

  const handleClick = () => {
    modalManager.openModal(AdvancedConfigDrawer.id);
  };

  return (
    <div>
      <Button
        disabled={disabled}
        size="small"
        onClick={handleClick}
        style={{ fontSize: 12 }}
        color="primary"
        variant="outlined"
      >
        全局配置
      </Button>
      <AdvancedConfig />
    </div>
  );
};
