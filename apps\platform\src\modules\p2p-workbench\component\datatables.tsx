import { Empty, Flex, Pagination, Popover, Tooltip } from 'antd';
import { useEffect } from 'react';

import { EllipsisText } from '@/components/text-ellipsis.tsx';
import { listDatatables } from '@/services/secretpad2/DatatableController';
import { Model, useModel } from '@/util/valtio-helper';

import styles from '../index.less';

import { Card } from './card';

export const DataTables = () => {
  const model = useModel(WorkbenchDataTables);

  useEffect(() => {
    model.getDatatableList();
  }, []);

  return (
    <div className={styles.datatableWrapper}>
      <Card title="数据目录">
        <div className={styles.datatable}>
          <div className={styles.datatableList}>
            {model.datatableList.map((item) => {
              const authProjectsFixed = item.datatableVO?.authProjects || [];
              const psiProjects = item.datatableVO?.psiDatatableVO?.grantNodeIds || [];
              const fiProjects = item.datatableVO?.fiDatatableVO?.grantNodeIds || [];

              const auths: { name: string; type: string }[] = [];

              authProjectsFixed.forEach((i) => {
                auths.push({ name: `联合建模-${i.name}`, type: 'project' });
              });
              psiProjects.forEach((i) => {
                auths.push({ name: `隐私求交-${i}`, type: 'psi' });
              });
              fiProjects.forEach((i) => {
                auths.push({ name: `联合预测-${i}`, type: 'prediction' });
              });
              return (
                <div
                  key={item.datatableVO?.datatableId}
                  className={styles.datatableItem}
                >
                  <EllipsisText width="55%">
                    {item.datatableVO?.datatableName}
                  </EllipsisText>
                  <Popover
                    title="已授权"
                    content={
                      <div className={styles.authProjectListPopover}>
                        {(() => {
                          const authItems = auths.map((i) => (
                            <div
                              key={i.name}
                              className={styles.authProjectListPopoverItem}
                            >
                              {i.name}
                            </div>
                          ));
                          return auths.length > 0 ? (
                            authItems
                          ) : (
                            <Empty
                              description={false}
                              image={Empty.PRESENTED_IMAGE_SIMPLE}
                            />
                          );
                        })()}
                      </div>
                    }
                    trigger="hover"
                  >
                    <div className={styles.datatableItemInfo}>
                      共 <span style={{ color: '#4762b2' }}>{auths.length}</span> 个授权
                    </div>
                  </Popover>
                </div>
              );
            })}
          </div>
          <Flex justify="end">
            <Pagination
              size="small"
              showTotal={(total) => `共 ${total} 条`}
              current={model.page}
              pageSize={model.pageSize}
              total={model.datatableList.length}
              onChange={(page, pageSize) => {
                model.page = page;
                model.pageSize = pageSize;
                model.getDatatableList();
              }}
            />
          </Flex>
        </div>
      </Card>
    </div>
  );
};

export class WorkbenchDataTables extends Model {
  constructor() {
    super();
  }

  page = 1;
  pageSize = 10;
  total = 0;

  datatableList: API2.DatatableNodeVO[] = [];
  displayDatatableList: any[] = [];

  async getDatatableList() {
    const res = await listDatatables({
      pageNumber: this.page,
      pageSize: this.pageSize,
    });
    this.datatableList = res.data?.datatableNodeVOList || [];
    this.total = res.data?.totalDatatableNums || 0;
  }
}
