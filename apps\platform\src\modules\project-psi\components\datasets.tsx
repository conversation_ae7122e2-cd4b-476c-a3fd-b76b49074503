import { Select } from 'antd';
import useFormInstance from 'antd/es/form/hooks/useFormInstance';
import { parse } from 'query-string';
import { useEffect, useState } from 'react';
import { useLocation } from 'umi';

import { DataManagerService } from '@/modules/data-manager/data-manager.service';
import { useModel } from '@/util/valtio-helper';

import style from '../../project-prediction/index.less';
import type { AllocatedNode } from '../project-psi.service';
import { ProjectPSIService } from '../project-psi.service';

type Dataset = {
  nodeId?: string;
  datatableId?: string;
  colName?: string;
};

export type Datatables<T extends 'remote' | 'local'> = T extends 'remote'
  ? API2.PSIJobDatatables[]
  : API2.DatatableVO[];

export const PSIDataset = (props: {
  parties: AllocatedNode[];
  type: 'local' | 'remote';
  name: string;
  id?: string;
  value?: Dataset;
  onChange?: (value: Dataset) => void;
}) => {
  const form = useFormInstance();
  const errors = form.getFieldError(props.name);
  const { parties, id, value, onChange, type } = props;

  const { search } = useLocation();
  const parsedSearch = parse(search);

  const { ownerId } = parsedSearch as { ownerId: string };
  const service = useModel(ProjectPSIService);
  const dataManagerService = useModel(DataManagerService);

  const [nodeListOptions, setNodeListOptions] = useState<
    { label: string; value: string }[]
  >([]);
  const [datatables, setDatatables] = useState<Datatables<typeof type>>([]);

  const [tableListOptions, setTableListOptions] = useState<
    { label: string; value: string }[]
  >([]);

  const [colListOptions, setColListOptions] = useState<
    { label: string; value: string }[]
  >([]);

  const nodeIdError = errors.length && !value?.nodeId;
  const datatableIdError = errors.length && !value?.datatableId;
  const colNameError = errors.length && !value?.colName;

  const getLocalDatatables = async () => {
    const res = await dataManagerService.listDataTables(ownerId, 1, 1000);
    if (res) {
      setDatatables(
        res.datatableNodeVOList?.map((item) => item.datatableVO) as Datatables<'local'>,
      );
    }
  };

  const getDatatables = async (nodeId: string) => {
    const res = await service.getPSIDatatables({ nodeId });
    if (res) {
      setDatatables(res);
    }
  };

  const triggerChange = (changedDataset: Dataset) => {
    if (!changedDataset.nodeId) {
      changedDataset.nodeId = type === 'local' ? parties[0].nodeId : undefined;
    }
    onChange?.({
      // ...dataset,
      ...changedDataset,
    });
    // form?.setFieldValue(keyName, { ...dataset });
  };

  const onNodeChange = (val: string) => {
    getDatatables(val);
    // setDataset({ ...dataset });
    triggerChange({
      ...(value || {}),
      nodeId: val,
      datatableId: undefined,
      colName: undefined,
    });
  };

  const onTableChange = (val: string) => {
    // dataset.datatableId = val;
    const table = datatables.find((item) => item.datatableId === val);
    if (table) {
      setColListOptions(
        table.schema?.map((item) => ({
          label: item.colName || '',
          value: item.colName || '',
        })) || [],
      );
      // dataset.colName = undefined;
    }
    // setDataset({ ...dataset });
    triggerChange({
      ...(value || {}),
      datatableId: val,
      colName: undefined,
    });
  };

  useEffect(() => {
    if (type !== 'local') {
      setNodeListOptions(
        parties
          .filter((item) => item.instId !== ownerId)
          .map((item) => ({ label: item.instName, value: item.nodeId })),
      );
    } else {
      getLocalDatatables();
    }
  }, [parties, type]);

  useEffect(() => {
    console.log(datatables, 'datatables');
    if (datatables) {
      setTableListOptions(
        datatables.map((item) => ({
          label: item.datatableName || '',
          value: item.datatableId || '',
        })),
      );
    }
  }, [datatables]);

  return (
    <div id={id} className={style.predictionDataset}>
      <div className={style.datasetItem}>
        <span className={style.datasetItemLabel}>机构：</span>
        <Select
          status={nodeIdError ? 'error' : undefined}
          disabled={type === 'local'}
          value={value?.nodeId || (type === 'local' ? parties[0].nodeId : undefined)}
          options={nodeListOptions}
          onChange={(val) => onNodeChange(val)}
        ></Select>
      </div>
      <div className={style.datasetItem}>
        <span className={style.datasetItemLabel}>数据表：</span>
        <Select
          status={datatableIdError ? 'error' : undefined}
          value={value?.datatableId}
          options={tableListOptions}
          onChange={(val) => onTableChange(val)}
        />
      </div>
      <div className={style.datasetItem}>
        <span className={style.datasetItemLabel}>ID列: </span>
        <Select
          status={colNameError ? 'error' : undefined}
          mode="multiple"
          value={value?.colName}
          options={colListOptions}
          onChange={(val) => triggerChange({ ...value, colName: val })}
        />
      </div>
    </div>
  );
};
