import type { FormProps } from 'antd';
import { Form } from 'antd';
import classNames from 'classnames';
import { forwardRef, useEffect, useImperativeHandle } from 'react';

import EventBus from './form-eventbus';
import styles from './index.module.less';
import type { FormItemConfig } from './search-form';
import { FormItem, getFieldItem } from './search-form';

interface FormBuilderProps {
  config: FormItemConfig[];
  formConfig?: FormProps;
  onValuesChange?: (
    changedValues: Record<string, any>,
    allValues: Record<string, any>,
  ) => void;
}

const initConfig = (config: FormItemConfig[]) => {
  const bus = EventBus.getInstance();

  config.forEach((item) => {
    if (item.watchFields) {
      item.watchFields.forEach((f: string) => {
        bus.subscribe(item.name, f, item.watchHooks![f]);
      });
    }
  });
};

const FormBuilder = forwardRef((props: FormBuilderProps, ref) => {
  const [form] = Form.useForm();
  useImperativeHandle(ref, () => ({
    form,
  }));

  useEffect(() => {
    initConfig(props.config);
    console.log(EventBus.getInstance(), 'initbus');
  }, [props.config]);

  return (
    <Form
      className={classNames({
        [styles.formBuilderInline]: props.formConfig?.layout === 'inline',
      })}
      form={form}
      {...props.formConfig}
      onValuesChange={props.onValuesChange}
    >
      {props.config.map((item) => (
        <FormItem
          inline={props.formConfig?.layout === 'inline'}
          key={item.name}
          data={item}
          form={form}
        >
          {getFieldItem(item, form)}
        </FormItem>
      ))}
    </Form>
  );
});

FormBuilder.displayName = 'FormBuilder';

export default FormBuilder;
