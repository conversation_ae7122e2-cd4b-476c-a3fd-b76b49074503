import { message } from 'antd';
import { parse } from 'query-string';

import API from '@/services/secretpad';
import API2 from '@/services/secretpad2';
import { listDatatables } from '@/services/secretpad2/DatatableController';
import { Model } from '@/util/valtio-helper';

export class DataManagerService extends Model {
  async listDataTables(
    ownerId: string,
    pageNum: number,
    pageSize: number,
    status?: string,
    search?: string,
    typeFilters?: string[],
    nodeNamesFilter?: string[],
  ) {
    const result = await listDatatables({
      ownerId,
      pageNumber: pageNum,
      pageSize,
      statusFilter: status,
      datatableNameFilter: search,
      types: typeFilters,
      nodeNamesFilter,
    });
    return result.data;
  }

  /** 获取本方机构与之授权成功的节点 且合作节点路由是可用状态 */
  getNodeVoters = async (dstNodeId: string) => {
    const { ownerId } = parse(window.location.search);
    const { data } = await API.NodeRouteController.page({
      page: 1,
      size: 1000,
      search: '',
      sort: {},
      ownerId: ownerId as string,
    });
    return (data?.list || [])
      .filter(
        (router) => router.dstNodeId === dstNodeId && router.status === 'Succeeded',
      )
      .map((item: API.NodeRouterVO) => ({
        nodeId: item.srcNode?.nodeId || '',
        nodeName: item.srcNode?.nodeName || '',
        instId: item.srcNode?.instId || '',
        instName: item.srcNode?.instName || '',
      }));
  };

  deletePSIAuth = async (authData: API2.DeletePsiDatatableRequest) => {
    const { status } = await API2.ProjectController.deleteDatatableFromPsiProject(
      authData,
    );
    return status;
  };

  addPSIAuth = async (authData: API2.AddPsiDatatableGrantRequest) => {
    const { status } = await API2.ProjectController.addDatatablePsiToProject(authData);
    return status;
  };

  deleteFIAuth = async (authData: API2.DeletePsiDatatableRequest) => {
    const { status } = await API2.ProjectController.deleteDatatableFromFiProject(
      authData,
    );
    return status;
  };

  addFIAuth = async (authData: API2.AddPsiDatatableGrantRequest) => {
    const { status } = await API2.ProjectController.addDatatableFiToProject(authData);
    return status;
  };
}

export enum UploadStatus {
  RUNNING = 'RUNNING', // 数据正在加密
  SUCCESS = 'SUCCESS', // 数据已成功加密上传
  FAILED = 'FAILED', // 数据上传失败
}

export enum DataSheetType {
  'CSV' = 'CSV',
  'HTTP' = 'HTTP',
  'OSS' = 'OSS',
}

export enum DataSourceType {
  OSS = 'OSS',
  HTTP = 'HTTP',
  ODPS = 'ODPS',
  LOCAL = 'LOCAL',
  MYSQL = 'MYSQL',
}
