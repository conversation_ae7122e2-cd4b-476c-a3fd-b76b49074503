<svg width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M57 6.0004C60.3137 6.0004 63 8.68669 63 12.0004V41.0004C62.9998 44.3139 60.3136 47.0004 57 47.0004H43.0664L37.2803 52.7865C36.8897 53.1771 36.2567 53.1771 35.8662 52.7865L30.0801 47.0004H15C11.6864 47.0004 9.00021 44.3139 9 41.0004V12.0004C9 8.68669 11.6863 6.0004 15 6.0004H57Z" fill="url(#paint0_linear_1581_3680)"/>
<path d="M55 8.0004C58.3137 8.0004 61 10.6867 61 14.0004V42.0004C60.9998 45.3139 58.3136 48.0004 55 48.0004H41.0664L35.2803 53.7865C34.8897 54.1771 34.2567 54.1771 33.8662 53.7865L28.0801 48.0004H13C9.68642 48.0004 7.00021 45.3139 7 42.0004V14.0004C7 10.6867 9.68629 8.0004 13 8.0004H55Z" fill="url(#paint1_linear_1581_3680)"/>
<g filter="url(#filter0_di_1581_3680)">
<path d="M19.5 23.0005C21.9853 23.0005 24 25.0152 24 27.5005C23.9998 29.9855 21.9851 32.0005 19.5 32.0005C17.0149 32.0005 15.0002 29.9855 15 27.5005C15 25.0152 17.0147 23.0005 19.5 23.0005ZM34 23.0005C36.4853 23.0005 38.5 25.0152 38.5 27.5005C38.4998 29.9855 36.4851 32.0005 34 32.0005C31.5149 32.0005 29.5002 29.9855 29.5 27.5005C29.5 25.0152 31.5147 23.0005 34 23.0005ZM48.5 23.0005C50.9853 23.0005 53 25.0152 53 27.5005C52.9998 29.9855 50.9851 32.0005 48.5 32.0005C46.0149 32.0005 44.0002 29.9855 44 27.5005C44 25.0152 46.0147 23.0005 48.5 23.0005Z" fill="#CBF4FF"/>
</g>
<ellipse cx="36" cy="60.5" rx="19" ry="2.5" fill="#5AADEE"/>
<path d="M17 58.5V60.5H55V58.5H17Z" fill="#5AADEE"/>
<g filter="url(#filter1_i_1581_3680)">
<ellipse cx="36" cy="58.5" rx="19" ry="2.5" fill="url(#paint2_linear_1581_3680)"/>
</g>
<defs>
<filter id="filter0_di_1581_3680" x="13" y="21.0005" width="46" height="17" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.224146 0 0 0 0 0.604424 0 0 0 0 0.876052 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1581_3680"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1581_3680" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.79 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1581_3680"/>
</filter>
<filter id="filter1_i_1581_3680" x="17" y="56" width="38" height="5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1581_3680"/>
</filter>
<linearGradient id="paint0_linear_1581_3680" x1="54" y1="6" x2="60.5" y2="49.5" gradientUnits="userSpaceOnUse">
<stop offset="0.0570047" stop-color="#CBF4FF"/>
<stop offset="0.131155" stop-color="#5AADEE"/>
<stop offset="1" stop-color="#5AADEE"/>
</linearGradient>
<linearGradient id="paint1_linear_1581_3680" x1="34.1406" y1="7.61994" x2="34.1406" y2="54.0794" gradientUnits="userSpaceOnUse">
<stop stop-color="#AAEDFF"/>
<stop offset="1" stop-color="#74E1FF"/>
</linearGradient>
<linearGradient id="paint2_linear_1581_3680" x1="36" y1="56" x2="36" y2="61" gradientUnits="userSpaceOnUse">
<stop stop-color="#5AADEE"/>
<stop offset="1" stop-color="#86D7FF"/>
</linearGradient>
</defs>
</svg>
