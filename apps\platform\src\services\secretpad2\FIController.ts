/* eslint-disable */
import request from 'umi-request';

/** 创建联合预测任务 POST /api/v1alpha1/fi/job/create */
export async function createFiProjectJob(
  body: API2.CreateFiProjectJobRequest,
  options?: { [key: string]: any },
) {
  return request<API2.SecretPadResponseCreateFiProjectJobVO>(
    '/api/v1alpha1/fi/job/create',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

/** 下载预测结果 GET /api/v1alpha1/fi/job/result/${param0} */
export async function getPredictionResults(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API2.getPredictionResultsParams,
  options?: { [key: string]: any },
) {
  const { jobId: param0, ...queryParams } = params;
  return request<API2.SecretPadResponsePredictionResultVO>(
    `/api/v1alpha1/fi/job/result/${param0}`,
    {
      method: 'GET',
      params: { ...queryParams },
      ...(options || {}),
    },
  );
}

export async function getPredictionDatatables(
  nodeId: string,
  options?: { [key: string]: any },
) {
  return request<API2.PSIJobDatatablesResponse>(`/api/v1alpha1/fi/job/datatables`, {
    method: 'POST',
    data: {
      nodeId,
    },
    ...(options || {}),
  });
}

export async function getPredictionList(
  data: API2.PSIJobListRequest,
  options?: { [key: string]: any },
) {
  return request<API2.SecretPadResponseFIJobListResponse>(`/api/v1alpha1/fi/job/list`, {
    method: 'POST',
    data,
    ...(options || {}),
  });
}

export async function checkPredictionJobStatus(
  data: { jobIds: string[] },
  options?: { [key: string]: any },
) {
  return request<API2.PSITaskStatusResponse>(`/api/v1alpha1/fi/job/status`, {
    method: 'POST',
    data,
    ...(options || {}),
  });
}

export async function deletePredictionJob(
  data: { jobId: string; nodeId: string },
  options?: { [key: string]: any },
) {
  return request<API2.SecretPadResponse>(`/api/v1alpha1/fi/job/delete`, {
    method: 'POST',
    data,
    ...(options || {}),
  });
}

export async function cancelPredictionJob(
  data: { jobId: string; nodeId: string },
  options?: { [key: string]: any },
) {
  return request<API2.SecretPadResponse>(`/api/v1alpha1/fi/job/cancel`, {
    method: 'POST',
    data,
    ...(options || {}),
  });
}

export async function getPredictionJobLogs(
  data: { jobId: string; nodeId: string },
  options?: { [key: string]: any },
) {
  return request<API2.PSILogsResponse>(`/api/v1alpha1/fi/job/logs`, {
    method: 'POST',
    data,
    ...(options || {}),
  });
}

export async function getPredictionJobLogDetails(
  data: { jobId: string; nodeId: string },
  options?: { [key: string]: any },
) {
  return request<API2.PSILogDetailsResponse>(`/api/v1alpha1/fi/job/detail-logs`, {
    method: 'POST',
    data,
    ...(options || {}),
  });
}
