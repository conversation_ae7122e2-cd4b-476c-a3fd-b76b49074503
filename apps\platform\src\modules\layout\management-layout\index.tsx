import { MenuUnfoldOutlined, MenuFoldOutlined } from '@ant-design/icons';
import { useKeyPress } from 'ahooks';
import { Menu } from 'antd';
import classnames from 'classnames';
import { parse, stringify } from 'query-string';
import { useEffect, useState } from 'react';
import { history, Outlet, useLocation } from 'umi';

import { DefaultModalManager } from '@/modules/dag-modal-manager';
import { isWindows } from '@/util/platform';
import { useModel } from '@/util/valtio-helper';

import styles from './index.less';

type ManagementLayoutComponentProps = {
  menuItems: {
    label: React.ReactNode;
    icon: React.ReactNode;
    component?: React.ReactNode;
    key: string;
    children?: ManagementLayoutComponentProps['menuItems'];
  }[];
  origin: string;
  defaultTabKey?: string;
};

const getComponents = (menuItems: ManagementLayoutComponentProps['menuItems']) => {
  return menuItems.reduce((acc, item) => {
    if (item.component) {
      acc[item.key] = item.component;
    }
    if (item.children) {
      acc = { ...acc, ...getComponents(item.children) };
    }
    return acc;
  }, {} as Record<string, React.ReactNode>);
};

export const foldHotKey = {
  key: isWindows ? 'ctrl.uparrow' : 'meta.ctrl.uparrow',
  text: isWindows ? 'Ctrl + ↑' : '⌘ + ctrl + ↑ ',
};

const getParentMenuKey = (
  menuItems: ManagementLayoutComponentProps['menuItems'],
  currentKey: string,
) => {
  let parentKey = '';

  for (const item of menuItems) {
    if (item.children?.find((child) => child.key === currentKey)) {
      parentKey = item.key;
    }
  }

  return parentKey;
};

export const ManagementLayoutComponent = (props: ManagementLayoutComponentProps) => {
  const modalManager = useModel(DefaultModalManager);

  const { menuItems, origin, defaultTabKey } = props;
  const [collapsed, setCollapsed] = useState(false);

  const { pathname, search } = useLocation();
  const parsedSearch = parse(search);

  const { tab, ownerId } = parsedSearch as { tab?: string; ownerId?: string };
  const [tabKey, setTabKey] = useState<string>();
  // const componentsMap = getComponents(menuItems);

  const [defaultOpenKey] = useState([
    getParentMenuKey(menuItems, pathname.split('/').at(-1) || ''),
  ]);
  // useEffect(() => {
  //   history.push({
  //     pathname: pathname === '/' ? '/home' : pathname,
  //     search: stringify(
  //       ownerId
  //         ? {
  //             ...parsedSearch,
  //             ownerId,
  //             tab: tab || defaultTabKey,
  //           }
  //         : { tab: tab || defaultTabKey },
  //     ),
  //   });
  // }, [defaultTabKey, tab]);
  useEffect(() => {
    console.log(pathname, 'path');
    const tabName = pathname.split('/').at(-1);
    console.log(tabName, 'tabName');
    setTabKey(tabName || defaultTabKey);
  }, [pathname]);

  // useEffect(() => {
  //   setTabKey(tab || defaultTabKey);
  // }, [tab]);

  const [collapseInfo, setCollapsedInfo] = useState(`收起/展开 ${foldHotKey.text}`);
  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };

  useEffect(() => {
    if (!collapsed) {
      setTimeout(() => setCollapsedInfo(`收起/展开 ${foldHotKey.text}`), 250);
    } else {
      setCollapsedInfo(``);
    }
  }, [collapsed]);

  useKeyPress([foldHotKey.key], (event) => {
    event.preventDefault();
    event.stopPropagation();
    toggleCollapsed();
  });

  const closeAllModal = () => {
    modalManager.closeAllModals();
  };

  return (
    <div className={styles.layoutContainer}>
      <div
        className={classnames(
          styles.menuContainer,
          collapsed ? styles.fold : styles.unfold,
        )}
      >
        <Menu
          selectedKeys={[tabKey as string]}
          defaultOpenKeys={defaultOpenKey}
          mode="inline"
          inlineCollapsed={collapsed}
          items={menuItems}
          onSelect={({ key }) => {
            closeAllModal();
            history.push({
              pathname: `/${origin}/${key}`,
              search: stringify({ ownerId }),
            });
          }}
        />

        <div className={styles.collapseInfo}>
          <div className={styles.collapseIcon} onClick={toggleCollapsed}>
            {collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          </div>
          <div className={classnames(styles.collapseText)}>{collapseInfo}</div>
        </div>
      </div>

      <div
        className={classnames(styles.contentContainer, {
          [styles.workbenchContentContainer]: tabKey === 'home',
        })}
      >
        {/* {componentsMap[tabKey as string] || null} */}
        <Outlet />
      </div>
    </div>
  );
};
