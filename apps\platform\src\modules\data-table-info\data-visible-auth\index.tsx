import { PlusOutlined } from '@ant-design/icons';
import { Button, Flex, message, Modal, Popconfirm, Space, Typography } from 'antd';
import type { ColumnType } from 'antd/es/table';
import { useEffect } from 'react';

import CommonTable from '@/components/common-table';
import { formatTimestamp } from '@/modules/dag-result/utils';
import { DataManagerService } from '@/modules/data-manager/data-manager.service';
import { DataManagerView } from '@/modules/data-manager/data-manager.view';
import { getModel, Model, useModel } from '@/util/valtio-helper';

import type { AuthComponentProps } from '../data-table-auth-drawer';
import { AuthType } from '../data-table-auth-drawer';

import { AddVisibleAuthComponent } from './add-visible-auth';
import style from './index.less';

export interface VisibleAuthVO {
  nodeId?: string;
  gmtTime?: string;
}

export const DataVisibleAuthComponent = (props: AuthComponentProps) => {
  const model = useModel(DataVisibleAuthModel);
  const { type, data: tableData } = props;

  const columns: ColumnType<VisibleAuthVO>[] = [
    {
      title: '已授权节点',
      dataIndex: 'nodeId',
      key: 'nodeId',
      width: '30%',
    },
    {
      title: '授权时间',
      dataIndex: 'gmtTime',
      key: 'gmtTime',
      width: '30%',
      render: (gmtTime: string) => <div>{formatTimestamp(gmtTime)}</div>,
    },
    {
      title: '操作',
      key: 'options',
      width: '30%',
      render: (_, record) => (
        <Popconfirm
          title="取消授权"
          description="是否确定取消授权?"
          onConfirm={() => model.cancleAuth(type, tableData, record)}
          onCancel={() => console.log('cancle')}
          okText="确定"
          cancelText="取消"
        >
          <Typography.Link>取消授权</Typography.Link>
        </Popconfirm>
      ),
    },
  ];

  useEffect(() => {
    console.log(props.data.nodeId);
    model.getTable(type, tableData);
  }, [type, tableData]);

  return (
    <div className={style.dataVisibleAuth}>
      <div className={style.addVisible}>
        <Flex justify="right">
          <Button type="primary" onClick={() => (model.showAddVisible = true)}>
            添加授权机构
          </Button>
        </Flex>
      </div>
      <CommonTable
        columns={columns}
        hideScroll
        dataSource={model.authNodes}
        rowKey="nodeId"
      />
      <Modal
        title="添加授权机构"
        open={model.showAddVisible}
        mask={false}
        width={600}
        onCancel={() => model.resetAuthModel()}
        onOk={() => model.saveAuthChange(type, tableData)}
        footer={
          <>
            <Space>
              <Button onClick={() => model.resetAuthModel()}>取消</Button>
              <Button
                type="primary"
                disabled={
                  !model.pendingTasks.add.length && !model.pendingTasks.remove.length
                }
                onClick={() => model.saveAuthChange(type, tableData)}
              >
                确定
              </Button>
            </Space>
          </>
        }
      >
        <AddVisibleAuthComponent
          authNodes={model.authNodes}
          nodeVoters={props.nodeVoters}
          onChange={(addNodes: string[], removeNodes: string[]) => {
            model.pendingTasks.add = addNodes;
            model.pendingTasks.remove = removeNodes;
          }}
        />
      </Modal>
    </div>
  );
};

export class DataVisibleAuthModel extends Model {
  showAddVisible = false;

  authNodes: VisibleAuthVO[] = [];

  pendingTasks: { [key: string]: string[] } = {
    add: [],
    remove: [],
  };

  service = getModel(DataManagerService);
  dataManagerIns = getModel(DataManagerView);

  resetAuthModel() {
    this.showAddVisible = false;
    this.pendingTasks = { add: [], remove: [] };
  }

  getTable(type: AuthType, tableData: API2.DatatableVO) {
    console.log(type);
    if (type === AuthType.PSI) {
      this.authNodes =
        tableData.psiDatatableVO?.grantNodeIds?.map((id) => {
          return {
            nodeId: id,
            gmtTime: tableData.psiDatatableVO?.gmtTime,
          };
        }) || [];
    } else if (type === AuthType.PREDICTION) {
      this.authNodes =
        tableData.fiDatatableVO?.grantNodeIds?.map((id) => {
          return {
            nodeId: id,
            gmtTime: tableData.fiDatatableVO?.gmtTime,
          };
        }) || [];
    }
  }

  async cancleAuth(type: AuthType, tableData: API2.DatatableVO, record: VisibleAuthVO) {
    let status: API2.SecretPadResponseStatus | undefined;
    if (type === AuthType.PSI) {
      status = await this.service.deletePSIAuth({
        datasourceId: tableData.datasourceId!,
        nodeId: tableData.nodeId!,
        datatableId: tableData.datatableId!,
        grantNodeId: record.nodeId!,
      });
    } else if (type === AuthType.PREDICTION) {
      // TODO: handle prediction auth cancellation
      status = await this.service.deleteFIAuth({
        datasourceId: tableData.datasourceId!,
        nodeId: tableData.nodeId!,
        datatableId: tableData.datatableId!,
        grantNodeId: record.nodeId!,
      });
    }
    if (status?.code === 0) {
      message.success('取消授权成功');
      this.dataManagerIns.getTableList().then(() => {
        this.dataManagerIns.setTableInfo(tableData);
      });
    } else {
      message.error('取消授权失败');
    }
  }

  saveAuthChange(authType: AuthType, tableData: API2.DatatableVO) {
    console.log(authType, this.pendingTasks, tableData);
    const addTask = this.pendingTasks.add.map((item) => {
      if (authType === AuthType.PSI) {
        return this.service.addPSIAuth({
          projectType: 'PSI',
          nodeId: tableData.nodeId!,
          datatableId: tableData.datatableId!,
          grantNodeId: item,
        });
      } else if (authType === AuthType.PREDICTION) {
        return this.service.addFIAuth({
          projectType: 'FI',
          nodeId: tableData.nodeId!,
          datatableId: tableData.datatableId!,
          grantNodeId: item,
        });
      }
    });

    const removeTask = this.pendingTasks.remove.map((item) => {
      if (authType === AuthType.PSI) {
        return this.service.deletePSIAuth({
          grantNodeId: item,
          datasourceId: tableData.datasourceId!,
          nodeId: tableData.nodeId!,
          datatableId: tableData.datatableId!,
        });
      } else if (authType === AuthType.PREDICTION) {
        return this.service.deleteFIAuth({
          grantNodeId: item,
          datasourceId: tableData.datasourceId!,
          nodeId: tableData.nodeId!,
          datatableId: tableData.datatableId!,
        });
      }
    });

    Promise.all([...addTask, ...removeTask])
      .then(() => {
        message.success('授权变更成功');
        this.resetAuthModel();
        this.dataManagerIns.getTableList().then(() => {
          this.dataManagerIns.setTableInfo(tableData);
        });
      })
      .catch(() => {
        message.error('授权变更失败');
      });
  }
}
