import request from 'umi-request';

export async function getMetricsHistory(
  body: {
    duration?: number;
    interval?: number;
  },
  options?: { [key: string]: any },
) {
  return request<API2.SecretPadResponse>('/api/v1alpha1/system/metrics/history', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function getTaskStatistics(
  body: {
    startDate?: string;
    endDate?: string;
  },
  options?: { [key: string]: any },
) {
  return request<API2.SecretPadResponse>('/api/v1alpha1/system/task/statistics', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
