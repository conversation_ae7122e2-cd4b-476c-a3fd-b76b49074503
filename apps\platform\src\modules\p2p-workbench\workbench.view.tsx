import classNames from 'classnames';
import { Renderer } from 'pb-bi-render';
import { useState } from 'react';

import { Bars } from './component/bars';
import { Charts } from './component/charts';
import { DataTables } from './component/datatables';
import { Message } from './component/message';
import { Nodes } from './component/nodes';
import { pageConfig } from './config';
import styles from './index.less';

const P2PWorkbenchComponent = () => {
  return (
    <div className={styles.main}>
      <div className={styles.left}>
        <div className={styles.header}>
          <div className={styles.title}>
            Hi～欢迎来到
            <span className={styles.titleName}>燕云隐私计算平台</span>
          </div>
          <div className={styles.titleDesc}>
            服务万千行业数据安全，促进数据价值流通，实现数据要素跨主体、跨行业、跨区域的高效流通利用及数据价值释放
          </div>
        </div>
        <Bars />
        <Charts />
        <Message />
      </div>
      <div className={styles.right}>
        <DataTables />
        <Nodes />
      </div>
      {/* <Renderer mode="grid" config={pageConfig} /> */}
      {/* <div className={classNames(styles.mainContent, styles.message)}>
        <div className={styles.eventTitle}>申请事项</div>
        <div className={styles.messageCard}>
          <MessageComponent />
        </div>
      </div> */}
      {/* <div className={classNames(styles.mainContent, styles.project)}>
        <P2pProjectListComponent />
      </div> */}
    </div>
  );
};

export default P2PWorkbenchComponent;
